package models

type OrderTracks struct {
	Partition
	OrderId           string         `gorm:"column:order_id;type:char(100);comment:订单号;NOT NULL" json:"order_id"`
	TrackId           int            `gorm:"column:track_id;type:int(11);NOT NULL" json:"track_id"`
	TrackNumber       string         `gorm:"column:track_number;type:char(50);comment:运单号;NOT NULL" json:"track_number"`
	Courier           string         `gorm:"column:courier;type:char(100);comment:运输商公司简码;NOT NULL" json:"courier"`
	TrackStatus       uint           `gorm:"column:track_status;type:tinyint(3) unsigned;default:0;comment:最新状态值;NOT NULL" json:"track_status"`
	AlterTrackStatus  uint           `gorm:"column:alter_track_status;type:tinyint(3) unsigned;default:0;comment:自定义状态值;NOT NULL" json:"alter_track_status"`
	TransitTime       uint           `gorm:"column:transit_time;type:int(10) unsigned;default:0;comment:运输时间(天);NOT NULL" json:"transit_time"`
	LastDate          uint           `gorm:"column:last_date;type:int(10) unsigned;default:0;comment:最后一条物流时间;NOT NULL" json:"last_date"`
	LastEvent         string         `gorm:"column:last_event;type:varchar(500);comment:物流最后一条信息;NOT NULL" json:"last_event"`
	Destination       string         `gorm:"column:destination;type:char(2);comment:shopify 目的地;NOT NULL" json:"destination"`
	OrderName         string         `gorm:"column:order_name;type:char(50);comment:订单名称;NOT NULL" json:"order_name"`
	CustomerName      string         `gorm:"column:customer_name;type:char(50);comment:客户名称;NOT NULL" json:"customer_name"`
	CustomerEmail     string         `gorm:"column:customer_email;type:char(50);comment:客户邮箱;NOT NULL" json:"customer_email"`
	CustomerPhone     string         `gorm:"column:customer_phone;type:char(50);comment:客户电话号码;NOT NULL" json:"customer_phone"`
	FulfillmentStatus int            `gorm:"column:fulfillment_status;type:tinyint(4);comment:发货状态;NOT NULL" json:"fulfillment_status"`
	FinancialStatus   int            `gorm:"column:financial_status;type:tinyint(4);comment:支付状态;NOT NULL" json:"financial_status"`
	OrderCreateTime   int            `gorm:"column:order_create_time;type:int(11);default:0;comment:订单创建时间;NOT NULL" json:"order_create_time"`
	FulfillmentId     string         `gorm:"column:fulfillment_id;type:char(255);comment:订单发货id;NOT NULL" json:"fulfillment_id"`
	OrderFulfillTime  int            `gorm:"column:order_fulfill_time;type:int(11);default:0;comment:订单发货时间" json:"order_fulfill_time"`
	Notes             string         `gorm:"column:notes;type:varchar(20);comment:物流单号标注" json:"notes"`
	IsDelete          int            `gorm:"column:is_delete;type:tinyint(4);default:0;comment:是否删除;NOT NULL" json:"is_delete"`
	OrderRecords      []OrderRecords `gorm:"foreignKey:OrderId;references:OrderId" json:"order_records"`
	OrderProducts     []OrderProduct `gorm:"foreignKey:OrderId;references:OrderId" json:"order_products"`
	TrackInfos        TrackInfos     `gorm:"foreignKey:TrackId" json:"track_infos"`
	Model
}

func (OrderTracks) TableName() string {
	return "order_track"
}
