package impl

import (
	"github.com/dromara/carbon/v2"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type TrackPageProductClicks struct {
	Helper
}

func (t *TrackPageProductClicks) ProductClick(userId int, productId string) error {
	createDate := carbon.SetTimezone(carbon.UTC).StartOfDay().StdTime()
	var trackPageProductClicks *models.TrackPageProductClicks
	result := database.DB.Where("user_id = ? AND product_id = ? AND create_date = ?", userId, productId, createDate.Unix()).First(&trackPageProductClicks)
	if result.RowsAffected != 0 && result.Error == nil {
		err := database.DB.Model(&models.TrackPageProductClicks{}).Where("user_id = ? AND product_id = ? AND create_date = ?", userId, productId, createDate.Unix()).Updates(map[string]interface{}{
			"click_num": trackPageProductClicks.ClickNum + 1,
		}).Error
		return err
	} else {
		err := database.DB.Create(&models.TrackPageProductClicks{
			UserId:     uint(userId),
			ProductId:  productId,
			CreateDate: uint(createDate.Unix()),
			ClickNum:   1,
		}).Error
		return err
	}
}
