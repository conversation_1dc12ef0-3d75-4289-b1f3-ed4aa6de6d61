package models

type ThemesSetting struct {
	ModType  string `gorm:"column:mod_type;type:varchar(255);comment:模板类型;NOT NULL" json:"mod_type"`
	ModClass string `gorm:"column:mod_class;type:text;comment:添加 class json;NOT NULL" json:"mod_class"`
	IsActive int    `gorm:"column:is_active;type:tinyint(4);default:0;comment:是否启用；0，否；1，是;NOT NULL" json:"is_active"`
	Model
}

func (t *ThemesSetting) TableName() string {
	return "themes_setting"
}
