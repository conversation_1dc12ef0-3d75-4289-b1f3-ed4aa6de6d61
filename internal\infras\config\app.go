package config

import (
	"log"
	"time"

	"github.com/channelwill/athena/settings"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// AppConfig 应用基本配置
type AppConfig struct {
	// 这里的 mapstructure tag标签用于指定配置文件的字段名字
	AppDebug         bool          `mapstructure:"app_debug"`          // 是否开启调试模式
	HttpPort         uint16        `mapstructure:"http_port"`          // http 服务端口
	HttpMonitorPort  uint16        `mapstructure:"http_monitor_port"`  // http服务对应的metrics端口
	GracefulWait     time.Duration `mapstructure:"graceful_wait"`      // 平滑退出等待时间
	LogLevel         string        `mapstructure:"log_level"`          // 日志等级
	DB               DB            `mapstructure:"db"`                 // DB
	ES               ES            `mapstructure:"es"`                 // ES
	Auth             Auth          `mapstructure:"auth"`               // auth
	ShopifyApiKey    string        `mapstructure:"shopify_api_key"`    // shopify api key
	ShopifyApiSecret string        `mapstructure:"shopify_api_secret"` // shopify api secret
	ClientScope      string        `mapstructure:"client_scope"`       // shopify api scope
}

type Auth struct {
	JwtSecret    string `mapstructure:"jwt_secret"`
	JwtExpireDay int    `mapstructure:"jwt_expire_day"`
}

type DB struct {
	Prefix string `mapstructure:"prefix"` // 数据库表名前缀
}

type ES struct {
	Index  string `mapstructure:"index"`  // 索引
	Scroll int    `mapstructure:"scroll"` // 分页获取从开始到结束总共的时间，单位：分钟
	Size   int    `mapstructure:"size"`   // 每次分页获取的行数
}

type OSS struct {
	BucketName     string `mapstructure:"bucket_name"`     // 桶名称
	AppId          string `mapstructure:"app_id"`          // 根目录名称
	PresignExpires int    `mapstructure:"presign_expires"` // 下载地址过期时间，单位：分钟
}

// 配置文件读取的接口
var conf settings.Config

// InitAppConfig 初始化app config
// 这个函数的代码可以根据实际情况在main.go初始化
func InitAppConfig() *AppConfig {
	var err error
	// 读取配置文件，并初始化redis和mysql
	conf, err = LoadConfig("./app.yaml")
	if err != nil {
		log.Fatalln("failed to load config:", err)
	}

	appConfig := &AppConfig{}
	err = conf.ReadSection("app_conf", appConfig)
	// log.Println("read app_conf err: ", err)
	if appConfig.AppDebug {
		log.Println("app_conf:", appConfig)
	}

	return appConfig
}

func (appConfig *AppConfig) GetLogLevel() zapcore.Level {
	switch appConfig.LogLevel {
	case "debug":
		return zap.DebugLevel
	case "info":
		return zap.InfoLevel
	case "warn":
		return zap.WarnLevel
	default:
		return zap.ErrorLevel
	}
}
