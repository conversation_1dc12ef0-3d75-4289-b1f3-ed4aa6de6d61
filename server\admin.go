package server

import (
	"net/http"

	"tmshopify/internal/admin/api"
	"tmshopify/server/middleware"
	"tmshopify/server/response"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func InitAdminRouter(router *gin.Engine) *gin.Engine {

	err := router.SetTrustedProxies([]string{"*************"})
	if err != nil {
		return nil
	}

	v1 := router.Group("/api/v1")
	{

		v1.GET("/", func(c *gin.Context) {
			// If the client is ***********, use the X-Forwarded-For
			// header to deduce the original client IP from the trust -
			// worthy parts of that header.
			// Otherwise, simply return the direct client IP
			logrus.Warnf("ClientIP: %s\n", c.ClientIP())
			response.Success(c, &response.SuccessResponse{
				Code: http.StatusOK,
			})
		})
		common := new(api.Common)
		auth := new(api.Auth)

		v1.POST("/login", auth.Login)
		v1.POST("/login-passwd", auth.LoginPasswd)
		v1.POST("/reset-passwd", auth.SendResetLink)
		v1.POST("/email_check", auth.SendEmailCheck) // 发送邮件验证码
		v1.GET("/test/token/:id", auth.TokenById)
		//中台路由组
		adminGroup := v1.Group("/admin", middleware.JWT("admin"))
		{
			adminGroup.GET("/user", middleware.Auth(), auth.User)             //登出
			adminGroup.GET("/logout", auth.Logout)                            //登出
			adminGroup.PUT("/passwd", middleware.Auth(), auth.ChangePassword) // 修改密码

			adminGroup.POST("/upload", common.Upload) // 上传文件到oss
			commonGroup := adminGroup.Group("/common")
			{
				commonGroup.GET("couriers", common.Couriers)
				commonGroup.GET("shopify-countries", common.ShopifyCountries)
			}
			//数据统计图表
			statistics := new(api.Statistics)
			adminGroup.POST("/paid_subscribers_chart", statistics.PaidSubscribersChart)                    //付费用户数统计图
			adminGroup.POST("/paid_subscriber_growth_chart", statistics.PaidSubscriberGrowthChart)         //付费用户增长统计图
			adminGroup.POST("/mrr_chart", statistics.MRRChart)                                             //MRR月度周期收入统计图
			adminGroup.POST("/revenue_per_subscriber_chart", statistics.RevenuePerSubscriberChart)         //客单价统计图
			adminGroup.POST("/subscriber_conversion_rate_chart", statistics.SubscriberConversionRateChart) //付费转化率统计图
			adminGroup.POST("/merchants_chart", statistics.MerchantsChart)                                 //用户数统计图
			adminGroup.POST("/merchants_growth_chart", statistics.MerchantsGrowthChart)                    //用户增长统计图
			adminGroup.POST("/country_users_chart", statistics.CountryUsersDistribution)                   //国家地区用户分布图
			adminGroup.POST("/retention_by_subscriber", statistics.RetentionBySubscriber)                  //付费用户留存数统计图

			integration := new(api.Integration)
			adminGroup.POST("/integration/app_list", integration.AppList)
			adminGroup.POST("/integration/app", integration.CreateApp)
			adminGroup.PUT("/integration/app", integration.UpdateApp)
			adminGroup.DELETE("/integration/app/:id", integration.DeleteApp)
			adminGroup.PUT("/integration/app/:id/status/:status", integration.ChangeAppStatus)
			adminGroup.PUT("/integration/app/:id/sort/:sort", integration.UpdateAppSort)
			adminGroup.GET("/integration/category_list", integration.CategoryList)
			adminGroup.POST("/integration/category", integration.CreateCategory)
			adminGroup.PUT("/integration/category", integration.UpdateCategory)
			adminGroup.DELETE("/integration/category/:id", integration.DeleteCategory)
			adminGroup.POST("/integration/promotion_click_log", integration.GetPromotionClickData)
			adminGroup.GET("/integration/ads", integration.GetAdsList)
			adminGroup.POST("/integration/ads", integration.CreateAds)
			adminGroup.PUT("/integration/ads", integration.UpdateAds)
			adminGroup.PUT("/integration/ads/:id/sort/:sort", integration.UpdateAdSort)

			notificationLog := new(api.Notification)
			adminGroup.POST("/notification/shopify_log", notificationLog.ShopifyNotificationLog)

			buriedPoint := new(api.BuriedPoint)
			adminGroup.GET("/buried_point/buried_point_log", buriedPoint.BuriedPointLog) //埋点数据列表

			user := new(api.User)
			userGroup := adminGroup.Group("/user")
			{

				userGroup.GET("/charge-plan", user.ChargePlans)                  //套餐列表
				userGroup.POST("/charge-plan", user.CreateChargePlan)            //套餐创建
				userGroup.PUT("/charge-plan/:id", user.UpdateChargePlan)         //套餐修改
				userGroup.PUT("/charge-plan/:id/:status", user.ChangePlanStatus) //套餐删除
				userGroup.GET("/search", user.SearchUser)
				userGroup.GET("/version", user.VersionList)
				userGroup.GET("/subscription", user.GetSubscriptionList)
				userGroup.PUT("/subscription-price", user.ChangeSubscriptionExtraPrice)
			}

			commonRecognition := new(api.CommonRecognition)
			commonRecognitionGroup := adminGroup.Group("/common-recognition")
			{
				commonRecognitionGroup.GET("/total-table", commonRecognition.TotalTable)
				commonRecognitionGroup.GET("/data-table", commonRecognition.DataTable)
				commonRecognitionGroup.POST("/common-rule", middleware.Auth(), commonRecognition.CreateCommonRule)
				commonRecognitionGroup.PUT("/common-rule", middleware.Auth(), commonRecognition.UpdateCommonRule)
				commonRecognitionGroup.DELETE("/common-rule/:id", commonRecognition.DeleteCommonRule)
				commonRecognitionGroup.POST("/common-rule-list", commonRecognition.GetCommonRuleList)
			}

			aiEdd := new(api.AiEdd)

			aiEddGroup := adminGroup.Group("ai-edd")
			{
				aiEddGroup.GET("/countries", aiEdd.GetCountries)
				aiEddGroup.PUT("/country", aiEdd.StoreCountries)
				aiEddGroup.POST("/country", aiEdd.StoreCountries)
				aiEddGroup.DELETE("/country/:id", aiEdd.DeleteCountry)
				aiEddGroup.GET("/couriers", aiEdd.GetCouriers)
				aiEddGroup.PUT("/courier", aiEdd.StoreCouriers)
				aiEddGroup.POST("/courier", aiEdd.StoreCouriers)
				aiEddGroup.DELETE("/courier/:id", aiEdd.DeleteCourier)
			}

			affiliateApi := new(api.Affiliate)
			affiliateApiGroup := adminGroup.Group("affiliate")
			{
				affiliateApiGroup.GET("/apps", affiliateApi.AppList)
				affiliateApiGroup.PUT("/app", affiliateApi.AppUpdate)
			}
		}

	}
	return router

}
