package models

import (
	"fmt"
	"strconv"
	"time"

	"gorm.io/gorm"
)

type Model struct {
	ID        int       `gorm:"primaryKey;authIncrement" json:"id,omitempty" `
	CreatedAt time.Time ` json:"created_at,omitempty" gorm:"autoCreateTime" `
	UpdatedAt time.Time ` json:"updated_at,omitempty" gorm:"autoUpdateTime" `
}

type Partition struct {
	UserId int   `gorm:"column:user_id;type:int(11);NOT NULL" json:"user_id"`
	User   Users `gorm:"foreignKey:UserId;references:ID" json:"user,omitempty"`
}

// TableOfUser 动态表写法 ：DB.Scopes(orderRecord.TableOfUser(orderRecord.TableName(),userId)).Find(&orderRecord)
func (p Partition) TableOfUser(tableName string, userId int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		//每2000个用户为一个表
		tableIndex := (userId - 1) / 2000
		tableName := fmt.Sprintf("%s_%d_%d", tableName, tableIndex*2000+1, (tableIndex+1)*2000)
		return db.Table(tableName)
	}
}

func (m *Model) IDtoString() string {
	return strconv.Itoa(int(m.ID))
}
