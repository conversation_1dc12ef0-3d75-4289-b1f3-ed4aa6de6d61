package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/dromara/carbon/v2"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"

	"tmshopify/config"
	"tmshopify/internal/app/definition"
	"tmshopify/internal/app/entity"
	"tmshopify/internal/app/request"
	"tmshopify/internal/app/utils"
	"tmshopify/pkg/cache"
	"tmshopify/pkg/domain"
	"tmshopify/pkg/handlers"
	"tmshopify/pkg/shopify"
	"tmshopify/pkg/shopify/graphql"
	"tmshopify/store/database"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type TrackPageService struct {
	trackPageSettingRepo     *impl.TrackPageSetting
	trackPageThemeRepo       *impl.TrackPageTheme
	orderTrackRepo           *impl.OrderTrack
	orderRecordRepo          *impl.OrderRecord
	reviewRep                *impl.Review
	userRepo                 *impl.User
	orderProductRep          *impl.OrderProduct
	trackPageProductClickRep *impl.TrackPageProductClicks
	trackGlobalData          definition.TrackPageGlobalData
	orderProductHandler      *handlers.OrderProductHandler
}

var (
	recommendLimit  = 5
	defaultProducts = []definition.TrackPageProduct{
		{
			Title: "Birthday Gift Photo Props",
			Url:   "//trackingmore-test.myshopify.com/collections/all/products/birthday-gift-photo-props",
			Price: decimal.NewFromInt(70),
			Img:   "//cdn.shopify.com/s/files/1/0261/4014/7794/products/451402887090_180x.jpg?v=1616749252",
		},
		{
			Title: "Hand Holding Flowers, Wedding Bouquet",
			Url:   "//trackingmore-test.myshopify.com/collections/all/products/korean-hand-holding-flowers-wedding-bouquet-home-bouquet",
			Price: decimal.NewFromInt(35),
			Img:   "//cdn.shopify.com/s/files/1/0261/4014/7794/products/2561296596751_180x.jpg?v=1616749235",
		},
		{
			Title: "Home Decoration Wedding Photography Set Flowers",
			Url:   "//trackingmore-test.myshopify.com/collections/all/products/artificial-flowers-artificial-flowers-home-decoration-wedding-photography-set",
			Price: decimal.NewFromInt(50),
			Img:   "//cdn.shopify.com/s/files/1/0261/4014/7794/products/1614655311628_180x.jpg?v=1616749145",
		},
		{
			Title: "Natural dried flower peony",
			Url:   "//trackingmore-test.myshopify.com/collections/all/products/natural-dried-flower-peony",
			Price: decimal.NewFromInt(40),
			Img:   "//cdn.shopify.com/s/files/1/0261/4014/7794/products/1135750434608_180x.jpg?v=1616749394",
		},
	}
)

type TrackPageInterface interface {
	DealRequestParams(user models.Users, trackRequestDomain domain.TrackRequestDomain, userSetting models.TrackPageSettings, activeTheme models.TrackPageThemes)
	HandleOrderData(userId int, orderName, email, trackNumber, mapSet, phone string, themeCode models.Theme, isOrderId, isPhoneTrack bool)
	GetRecommendProduct(user models.Users, product string, noCache bool) []definition.TrackPageProduct
	CheckUser(storeName string) (user *models.Users, err error)
	GetCollectionProduct(user models.Users, collection string, noCache bool, productIds string) []definition.TrackPageProduct
	SaveReview(userId int, post impl.ReviewPostData) models.Reviews
	ReviewList(user *models.Users, data []impl.CourierTrack, lang string) interface{}
	SaveTag(review models.Reviews, tags []int)
	OrderProduct(user models.Users, order string) ([]definition.OrderProductImgData, error)
	FusionOrderAndNumberMessage(themeTrack IThemeTrack, numberMessage []definition.TrackNumberData)
	CheckAuth(params request.TrackRequest) (models.Users, models.TrackPageSettings, models.TrackPageThemes, error)
	handleShippedStatus(statusNode map[int]definition.ReturnInfo, trackInfoCreatedAt int) map[int]definition.ReturnInfo
	IsPlugin()
}

type IThemeTrack interface {
	BackData() map[string]interface{}
	orderEmailTracking(orderName string, isOrderId, isPhoneTrack bool)
	previewTracking()
	numberTracking()
	orderPhoneTracking()
	orderTracking()
	HandleTrackInfo(trackNumber definition.TrackNumberData) definition.TrackingData
	returnData() map[string]interface{}
}

func (t *TrackPageService) CheckAuth(params request.TrackRequest) (models.Users, models.TrackPageSettings, models.TrackPageThemes, error) {
	var userSetting models.TrackPageSettings
	var activeTheme models.TrackPageThemes
	shopName := params.Shop
	isPlugin := params.IsPlugin
	isPreview := params.IsPreview
	isEmbed := params.IsEmbed
	user, err := t.userRepo.PreloadSubscriptionDetailByStoreName(shopName)
	if err != nil || user.ID == 0 {
		if err == nil {
			err = errors.New("user not exist")
		}
		return user, userSetting, activeTheme, err
	}
	userId := user.ID
	userSetting, _ = t.trackPageSettingRepo.GetUserTrackPage(userId, "")
	activeTheme = userSetting.ActiveTheme

	// 插件暂时只处理classic
	plugin := isPlugin
	if plugin && activeTheme.ThemeCode != models.ThemeClassic {
		activeTheme, _ = t.trackPageThemeRepo.GetClassicTheme(userId)
	}
	// 嵌入暂时只处理classic
	if isEmbed && activeTheme.ThemeCode != models.ThemeClassic {
		activeTheme, _ = t.trackPageThemeRepo.GetClassicTheme(userId)
	}
	preview := isPreview
	if preview {
		var redisData []byte
		redisData, _ = database.RS.Get("preview_track_page_" + strconv.Itoa(userId))
		if len(redisData) > 0 {
			err = json.Unmarshal(redisData, &activeTheme)
		}
		if err != nil {
			logrus.Error(fmt.Sprintf("Get preview track page error %s,user is %d", err.Error(), userId))
			return user, userSetting, activeTheme, nil
		}
	}

	return user, userSetting, activeTheme, nil
}

func (t *TrackPageService) DealRequestParams(user models.Users, trackRequestDomain domain.TrackRequestDomain, userSetting models.TrackPageSettings, activeTheme models.TrackPageThemes) {
	trackGlobalData := definition.TrackPageGlobalData{
		User:                  user,
		TrackSettingEntity:    entity.HandleSetting(userSetting),
		TrackThemeEntity:      entity.HandleTheme(activeTheme, trackRequestDomain.Lang),
		TrackRequestDomain:    trackRequestDomain,
		PermissionDomain:      t.userRepo.GetPermissionByUser(user),
		OrderRecord:           definition.OrderRecord{},
		CustomStatusTime:      nil,
		ShippingAddressResult: definition.HandleShippingAddressResult{},
		MapLocationResult:     definition.MapLocation{},
		NumberMessage:         nil,
		RootData:              definition.RootData{},
		TrackingData:          nil,
		ConfigData:            nil,
		IsPlugin:              false,
		EddSetting:            entity.HandleEddSetting(user.ID),
	}
	//没有权限不开放review
	if !trackGlobalData.PermissionDomain.VerifyPermission(domain.ShipmentReview) {
		trackGlobalData.TrackThemeEntity.ClassicSetting.General.ShipmentReview = false
		trackGlobalData.TrackThemeEntity.ModernSetting.General.ShipmentReview = false
	}
	//没有权限不开放coupon sell
	if !trackGlobalData.PermissionDomain.VerifyPermission(domain.CouponSells) {
		trackGlobalData.TrackThemeEntity.ClassicSetting.General.CouponSell = false
		trackGlobalData.TrackThemeEntity.ModernSetting.General.CouponSell = false
	}
	t.trackGlobalData = trackGlobalData
}

func (t *TrackPageService) IsPlugin() {
	t.trackGlobalData.IsPlugin = true
}

func (t *TrackPageService) HandleOrderData(userId int, orderName, email, trackNumber, mapSet, phone string, themeCode models.Theme, isOrderId, isPhoneTrack bool) {
	originOrderName := orderName
	//检查订单号是否携带 # 号 没有则添加
	res := strings.HasPrefix(originOrderName, "#")
	if !res {
		orderName = "#" + originOrderName
	}
	//检查电话号码是否携带 + 号 没有则添加
	oldPhone := phone
	res = strings.HasPrefix(oldPhone, "+")
	if !res {
		phone = "+" + oldPhone
	}
	//数据过滤
	var orderRecord models.OrderRecords

	query := database.DB.Scopes(orderRecord.TableOfUser(orderRecord.TableName(), userId)).Where("user_id = ? AND is_delete = ?", userId, 0)
	if isOrderId {
		query.Where("order_id = ?", originOrderName)
	} else {
		//如果没有携带 # 号查询 那么同时查询有和没有 # 号的订单号
		if originOrderName != orderName {
			query.Where("order_name IN (?)", []string{originOrderName, orderName})
		} else {
			query.Where("order_name", originOrderName)
		}
		//判断是电话号码/邮箱+订单号查询
		if !isPhoneTrack {
			query.Where("customer_email = ?", email)
		} else if oldPhone != phone {
			query.Where("customer_phone IN (?)", []string{oldPhone, phone})
		} else {
			query.Where("customer_phone", phone)
		}
	}
	//查询数据
	var orderRecords []models.OrderRecords
	result := query.Limit(10).Find(&orderRecords)

	//如果存在多条结果 那么介入邮箱获取订单号信息 start
	if result.RowsAffected > 1 {
		for _, value := range orderRecords {
			//如果是邮箱相同 直接返回
			if value.CustomerEmail != "" && email != "" && strings.ToLower(value.CustomerEmail) == strings.ToLower(email) {
				orderRecord = value
				break
			}
			//如果是电话号码相同 直接返回
			if value.CustomerPhone != "" && phone != "" && (value.CustomerPhone == phone || value.CustomerPhone == oldPhone) {
				orderRecord = value
				break
			}
		}
	}
	//如果存在多条结果 那么介入邮箱获取订单号信息 end
	if orderRecord.ID == 0 && result.RowsAffected != 0 {
		orderRecord = orderRecords[0]
	}
	//记录是否有6个月之前的订单被查询
	//if result.RowsAffected != 0 {
	//	time.Time{}
	//	createTime := orderRecord.CreatedAt
	//	oid := orderRecord.ID
	//	uid := orderRecord.UserId
	//	orderName = orderRecord.OrderName
	//	orderCreateTime := orderRecord.OrderCreateTime
	//	hasDateEarly := time.Now().AddDate(0, -6, 0)
	//	if hasDateEarly.After(createTime) {
	//
	//	}
	//}

	//在提取单号信息
	trackQuery := t.orderTrackRepo.PreloadTrackInfo(userId).Where("is_delete = ?", 0)
	hasSql := false
	//如果是订单查询
	if orderName != "" && ((email != "" && !isOrderId) || (phone != "" && isPhoneTrack)) && orderRecord.ID > 0 {
		//如果没有携带 # 号查询 那么同时查询有和没有 # 号的订单号
		if orderName != originOrderName {
			trackQuery.Where("order_name IN (?)", []string{orderName, originOrderName})
		} else {
			trackQuery.Where("order_name = ?", orderName)
		}
		hasSql = true
	}
	if isOrderId {
		trackQuery.Where("order_id = ?", originOrderName)
		hasSql = true
	}
	if trackNumber != "" {
		trackQuery.Where("track_number = ?", trackNumber)
		hasSql = true
	}
	var numberMessage []models.OrderTracks
	if hasSql {
		// 查询可能会有多个结果
		result = trackQuery.Find(&numberMessage)
	}
	if len(numberMessage) == 0 {
		numberMessage = nil
	}
	//用户自定义状态 键为状态码 值为时间戳
	var customStatusTime map[config.StatusNodeNumber]int

	//判断使用 shipping address 还是 billing address
	var shippingAddress *graphql.Address
	var address *struct {
		ShippingAddress *graphql.Address `json:"shipping_address"`
		BillingAddress  *graphql.Address `json:"billing_address"`
	}
	var ShippingAddressStr = []byte(orderRecord.ShippingAddress)
	if len(orderRecord.ShippingAddress) != 0 {
		err := json.Unmarshal(ShippingAddressStr, &shippingAddress)
		if err != nil || reflect.ValueOf(*shippingAddress).IsZero() {
			_ = json.Unmarshal(ShippingAddressStr, &address)
			shippingAddress = address.ShippingAddress
			var postalCode string
			if numberMessage != nil {
				database.DB.Model(models.CourierSettings{}).Where("user_id = ? AND courier_code = ? AND is_delete != ?", userId, numberMessage[0].Courier, 1).Select("postal_code").Scan(&postalCode)
			}
			if postalCode == "billing_address" {
				shippingAddress = address.BillingAddress
			}
			ShippingAddressStr, _ = json.Marshal(shippingAddress)
		}
	}
	orderRecord.ShippingAddress = string(ShippingAddressStr)

	//保存订单号信息
	if orderRecord.ID != 0 {
		if themeCode == models.ThemeModern {
			orderRecord, customStatusTime = t.orderRecordRepo.DealModernOrderMessageData(orderRecord)
		} else {
			var shippingAddressResult definition.HandleShippingAddressResult
			var mapLocationResult definition.MapLocation
			mapLocation := mapSet == "location"
			orderRecord, customStatusTime, shippingAddressResult, mapLocationResult = t.orderRecordRepo.DealOrderMessageData(orderRecord, mapLocation)
			t.trackGlobalData.ShippingAddressResult = shippingAddressResult
			t.trackGlobalData.MapLocationResult = mapLocationResult
		}
	}
	var customStatusTimeStruct map[config.StatusNodeNumber]int
	_ = json.Unmarshal([]byte(orderRecord.CustomStatusTime), &customStatusTimeStruct)
	t.trackGlobalData.OrderRecord = definition.OrderRecord{
		OrderId:              orderRecord.OrderId,
		UserId:               orderRecord.UserId,
		OrderName:            orderRecord.OrderName,
		OrderCreateTime:      orderRecord.OrderCreateTime,
		CustomerName:         orderRecord.CustomerName,
		CustomerEmail:        orderRecord.CustomerEmail,
		CustomerPhone:        orderRecord.CustomerPhone,
		Product:              orderRecord.Product,
		Currency:             orderRecord.Currency,
		OrderNote:            orderRecord.OrderNote,
		OrderSource:          orderRecord.OrderSource,
		FulfillmentStatus:    orderRecord.FulfillmentStatus,
		FinancialStatus:      orderRecord.FinancialStatus,
		RecipientPhone:       orderRecord.RecipientPhone,
		RecipientZip:         orderRecord.RecipientZip,
		RecipientCountryCode: orderRecord.RecipientCountryCode,
		ShippingAddress:      orderRecord.ShippingAddress,
		CustomTrackStatus:    orderRecord.CustomTrackStatus,
		CustomStatusTime:     customStatusTimeStruct,
		IsDelete:             orderRecord.IsDelete,
		DataSource:           orderRecord.DataSource,
		WhetherPay:           orderRecord.WhetherPay,
	}
	t.trackGlobalData.CustomStatusTime = customStatusTime
	if len(numberMessage) > 0 {
		for _, orderTrack := range numberMessage {
			var originInfo definition.TrackingInfoData
			var destinationInfo definition.TrackingInfoData
			_ = json.Unmarshal([]byte(orderTrack.TrackInfos.OriginInfo), &originInfo)
			_ = json.Unmarshal([]byte(orderTrack.TrackInfos.DestinationInfo), &destinationInfo)
			var trackInfoCreatedAt int
			if !orderTrack.TrackInfos.CreatedAt.IsZero() {
				trackInfoCreatedAt = int(orderTrack.TrackInfos.CreatedAt.Unix())
			}
			t.trackGlobalData.NumberMessage = append(t.trackGlobalData.NumberMessage, definition.TrackNumberData{
				ID:                orderTrack.ID,
				OrderId:           orderTrack.OrderId,
				TrackId:           orderTrack.TrackId,
				TrackNumber:       orderTrack.TrackNumber,
				Courier:           orderTrack.Courier,
				TrackStatus:       orderTrack.TrackStatus,
				AlterTrackStatus:  orderTrack.AlterTrackStatus,
				TransitTime:       orderTrack.TransitTime,
				LastDate:          orderTrack.LastDate,
				LastEvent:         orderTrack.LastEvent,
				Destination:       orderTrack.Destination,
				OrderName:         orderTrack.OrderName,
				CustomerName:      orderTrack.CustomerName,
				CustomerEmail:     orderTrack.CustomerEmail,
				CustomerPhone:     orderTrack.CustomerPhone,
				FulfillmentStatus: orderTrack.FulfillmentStatus,
				FinancialStatus:   orderTrack.FinancialStatus,
				OrderCreateTime:   orderTrack.OrderCreateTime,
				FulfillmentId:     orderTrack.FulfillmentId,
				OrderFulfillTime:  orderTrack.OrderFulfillTime,
				Notes:             orderTrack.Notes,
				IsDelete:          orderTrack.IsDelete,
				UpdatedAt:         orderTrack.UpdatedAt,
				TrackInfos: definition.TrackInfos{
					ID:                   orderTrack.TrackInfos.ID,
					OriginalCountry:      orderTrack.TrackInfos.OriginalCountry,
					DestinationCountry:   orderTrack.TrackInfos.DestinationCountry,
					ExpectedDeliveryTime: orderTrack.TrackInfos.ExpectedDeliveryTime,
					OriginInfo:           originInfo,
					DestinationInfo:      destinationInfo,
				},
				TrackInfoCreatedAt: trackInfoCreatedAt,
			})
		}
	}
}

func (t *TrackPageService) FusionOrderAndNumberMessage(themeTrack IThemeTrack, numberMessage []definition.TrackNumberData) {
	//0为异常情况
	var dataState int
	//如果没有运单信息 并且没有订单信息 那么返回订单不存在
	if t.trackGlobalData.OrderRecord.OrderId == "" && numberMessage == nil {
		dataState = 1
	}
	//如果有运单信息 但是没有订单信息
	//此类情况属于以前的 shopify 单号或者非 shopify 单号(例如:单个添加导入 其他平台同步)
	if t.trackGlobalData.OrderRecord.OrderId == "" && numberMessage != nil {
		dataState = 2
	}
	//如果没有运单信息 但是有订单信息
	//此类情况属于 此订单号没有发货或者还没有填写运单号
	if t.trackGlobalData.OrderRecord.OrderId != "" && numberMessage == nil {
		dataState = 3
	}
	//如果有运单信息 也有订单号信息 此类情况属于 shopify 同步并且已经发货 已经填写了运单号
	if t.trackGlobalData.OrderRecord.OrderId != "" && numberMessage != nil {
		dataState = 4
	}
	//检查当前数据情况
	switch dataState {
	case 2:
		for _, item := range numberMessage {
			t.handleSingleNumber(themeTrack, item)
		}
	case 3:
		orderRecord := t.trackGlobalData.OrderRecord
		//是否通过了电话号码审核
		trackEmail := strings.ToLower(orderRecord.CustomerEmail)
		email := strings.ToLower(t.trackGlobalData.TrackRequestDomain.Email)
		trackingNumber := t.trackGlobalData.TrackRequestDomain.TrackNumber
		//是否通过了电话号码审核
		isValidatePhone := t.checkPhoneIsR()
		if email != trackEmail && isValidatePhone == 0 && trackingNumber == "" {
			t.trackGlobalData.RootData.Error = 401
			return
		}
		var trackingData definition.TrackingData
		// 获取发货地址地图信息
		if t.trackGlobalData.TrackThemeEntity.ThemeCode == models.ThemeClassic {
			if t.trackGlobalData.TrackThemeEntity.ClassicSetting.TrackingHistory.Map != "none" && orderRecord.ShippingAddress != "" {
				_ = json.Unmarshal([]byte(orderRecord.ShippingAddress), &trackingData.ShippingMap)
			}
		}
		// 获取产品图片 start
		imgInfo := t.orderProductRep.GetImgInfoByOrderId(orderRecord.OrderId, t.trackGlobalData.User.ID)
		if len(imgInfo) == 0 {
			t.orderProductHandler.Handle(t.trackGlobalData.User, orderRecord.OrderId)
			imgInfo = t.orderProductRep.GetImgInfoByOrderId(orderRecord.OrderId, t.trackGlobalData.User.ID)
		}
		trackingData.Title = imgInfo
		// 当前数字状态
		trackingData.StausDataNum = 1
		if orderRecord.CustomTrackStatus != 0 && !(orderRecord.CustomTrackStatus == config.OrderedNodeStatusNumber || orderRecord.CustomTrackStatus == config.OrderReadyStatusNumber) {
			trackingData.StausDataNum = orderRecord.CustomTrackStatus
		}
		// 根据用户全局设置的节点信息、单个单号设置的节点信息、订单创建时间、发货时间 返回节点对应信息和对应时间
		statusNode, statusNum := t.HandleTrackingNode(map[config.StatusNodeNumber]definition.ReturnInfo{}, 0)
		// 提取状态节点
		trackingData.StatusNode = statusNode
		//提取用户定义的状态
		trackingData.StatusNum = statusNum
		// 无订单有状态时显示 status
		if statusNum.StatusDescription != "" {
			trackingData.Status = statusNum.StatusDescription
		}
		// 订单创建时间
		trackingData.OrderCreateTime = orderRecord.OrderCreateTime
		if t.trackGlobalData.TrackThemeEntity.ThemeCode == models.ThemeModern {
			trackingData.OrderCreateTime = utils.HandleTimeFormatBySet(int64(orderRecord.OrderCreateTime),
				t.trackGlobalData.TrackThemeEntity.ModernSetting.TrackingInfo.DateFormat, 2, false, false)
		}
		// 订单备注信息
		trackingData.Notes = orderRecord.OrderNote
		// 发货时间
		trackingData.OrderFulfillTime = orderRecord.OrderFulfillTime
		// 只有订单号没有单号 也显示预计投递时间
		t.trackGlobalData.OrderRecord.Destination = orderRecord.RecipientCountryCode
		eddService := NewEddService()
		calculateEntity := eddService.GetCalculateEntity(t.trackGlobalData.EddSetting, TrackCalculateData{
			UserId:           orderRecord.UserId,
			OrderCreateTime:  orderRecord.OrderCreateTime,
			OrderFulfillTime: orderRecord.OrderFulfillTime,
			Destination:      trackingData.Destination,
			Courier:          "",
			TrackNumber:      trackingData.TrackNumber,
			FulfillmentId:    "",
		})
		dateShow := eddService.CalculateEddByOrderAndDestinationByEntities(orderRecord.UserId,
			calculateEntity, t.trackGlobalData.EddSetting)
		if dateShow.StartShow != 0 && dateShow.EndShow != 0 {
			if dateShow.EndShow > 0 {
				startShow := utils.HandleTimeFormatBySet(dateShow.StartShow, t.trackGlobalData.EddSetting.DateFormat,
					2, false, false)
				endShow := utils.HandleTimeFormatBySet(dateShow.EndShow, t.trackGlobalData.EddSetting.DateFormat,
					2, false, false)
				trackingData.ShippingTimeCon = fmt.Sprintf("%s_%s", startShow, endShow)
			}
		}
		// 是否显示预计投递时间
		if trackingData.ShippingTimeCon != "" {
			trackingData.ShippingTimeShow = 1
		}
		t.trackGlobalData.TrackingData = append(t.trackGlobalData.TrackingData, trackingData)
	case 4:
		//是否通过了电话号码审核
		trackEmail := strings.ToLower(t.trackGlobalData.OrderRecord.CustomerEmail)
		email := strings.ToLower(t.trackGlobalData.TrackRequestDomain.Email)
		phone := strings.ToLower(t.trackGlobalData.TrackRequestDomain.Phone)
		trackingNumber := t.trackGlobalData.TrackRequestDomain.TrackNumber
		//是否通过了电话号码审核
		isValidatePhone := t.checkPhoneIsR()
		// 单号查询或者order name查询可以不用验证
		if (email != "" || phone != "") && email != trackEmail && isValidatePhone == 0 && trackingNumber == "" {
			t.trackGlobalData.RootData.Error = 401
			return
		}
		if len(numberMessage) != 0 {
			for _, item := range numberMessage {
				t.handleSingleNumber(themeTrack, item)
			}
		}
	default:
		t.trackGlobalData.RootData.Error = 404
		return
	}

}

func (t *TrackPageService) handleSingleNumber(themeTrack IThemeTrack, item definition.TrackNumberData) {
	trackEmail := strings.ToLower(item.CustomerEmail)
	trackingNumber := item.TrackNumber
	email := strings.ToLower(t.trackGlobalData.TrackRequestDomain.Email)
	//是否通过了电话号码审核
	isValidatePhone := t.checkPhoneIsR()

	if trackingNumber != "" || email == trackEmail || isValidatePhone == 1 {
		//当前单号
		currentTrackNumber := item.TrackNumber

		if item.OrderCreateTime != 0 {
			t.trackGlobalData.OrderRecord.OrderCreateTime = item.OrderCreateTime
		}

		//多个产品 id 只取一个
		if t.trackGlobalData.RootData.Product == "" && t.trackGlobalData.OrderRecord.Product != "" {
			t.trackGlobalData.RootData.Product = t.trackGlobalData.OrderRecord.Product
		}
		//如果使用的是单号查询 那么将邮箱赋值
		if trackingNumber != "" {
			t.trackGlobalData.RootData.OrderNumber = t.trackGlobalData.OrderRecord.OrderName
			t.trackGlobalData.RootData.Email = trackEmail
			t.trackGlobalData.TrackRequestDomain.Email = t.trackGlobalData.RootData.Email
		}
		t.trackGlobalData.OrderRecord.OrderFulfillTime = item.OrderFulfillTime
		//获取当前单号对应的发货时间
		var trackingData definition.TrackingData
		trackingData = themeTrack.HandleTrackInfo(item)
		trackingData.Destination = item.TrackInfos.DestinationCountry
		trackingData.Original = item.TrackInfos.OriginalCountry
		trackingData.TrackNumber = currentTrackNumber
		trackingData.Notes = t.trackGlobalData.OrderRecord.OrderNote
		trackingData.Comment = item.Notes
		imgInfo := t.orderProductRep.GetImgInfoByFulfillmentId(item.FulfillmentId, t.trackGlobalData.User.ID)
		if len(imgInfo) == 0 {
			var orderProductHandler handlers.OrderProductHandler
			orderProductHandler.Handle(t.trackGlobalData.User, t.trackGlobalData.OrderRecord.OrderId)
			imgInfo = t.orderProductRep.GetImgInfoByFulfillmentId(item.FulfillmentId, t.trackGlobalData.User.ID)
		}
		if len(imgInfo) > 0 {
			t.trackGlobalData.RootData.Product = imgInfo[0].ProductId
		}
		trackingData.Title = imgInfo
		if t.trackGlobalData.TrackThemeEntity.ThemeCode == models.ThemeClassic {
			t.trackGlobalData.OrderRecord.Destination = item.Destination
			trackingData.OrderCreateTime = item.OrderCreateTime
			if t.trackGlobalData.TrackThemeEntity.ClassicSetting.TrackingHistory.Map != "none" && t.trackGlobalData.OrderRecord.ShippingAddress != "" {
				_ = json.Unmarshal([]byte(t.trackGlobalData.OrderRecord.ShippingAddress), &trackingData.ShippingMap)
			}
		}
		if t.trackGlobalData.TrackThemeEntity.ThemeCode == models.ThemeModern {
			trackingData.OrderCreateTime = utils.HandleTimeFormatBySet(int64(item.OrderCreateTime), t.trackGlobalData.TrackThemeEntity.ModernSetting.TrackingInfo.DateFormat, 2, false, false)
		}
		//发货地址 只有设置地图显示的时候才返回地址信息
		if t.trackGlobalData.TrackThemeEntity.ThemeCode == models.ThemeClassic && t.trackGlobalData.TrackThemeEntity.ClassicSetting.TrackingHistory.Map != "" &&
			t.trackGlobalData.TrackThemeEntity.ClassicSetting.TrackingHistory.Map != "none" {
			trackingData.ShippingMap = t.trackGlobalData.ShippingAddressResult
		}

		if trackingData.Trackinfo == nil && trackingData.CompanyCode == "cainiao" {
			// 检查用户是否设置了菜鸟物流
			var courierSetting models.CourierSettings
			database.DB.Where("user_id=? and courier_code='cainiao' and is_delete=0", t.trackGlobalData.User.ID).Select("id").First(&courierSetting)
			if courierSetting.ID == 0 {
				// 用户没有设置菜鸟物流
				t.trackGlobalData.RootData.HideCainiao = true
			}
		}

		t.trackGlobalData.TrackingData = append(t.trackGlobalData.TrackingData, trackingData)
	}
}

var phoneRegex = regexp.MustCompile(`^\+?[0-9 \-]+$`)

// checkPhoneIsR 检查电话号码是否有效
func (t *TrackPageService) checkPhoneIsR() int {
	isValidatePhone := 0

	if t.trackGlobalData.OrderRecord.CustomerPhone != "" && phoneRegex.MatchString(t.trackGlobalData.TrackRequestDomain.Phone) {
		phone := phoneToNumber(t.trackGlobalData.TrackRequestDomain.Phone)
		customerPhone := phoneToNumber(t.trackGlobalData.OrderRecord.CustomerPhone)

		if phone != "" && customerPhone != "" && strings.HasPrefix(customerPhone, phone) {
			isValidatePhone = 1
		}
	}

	return isValidatePhone
}

// phoneToNumber 将电话号码处理为纯数字结构
var nonDigitRegexp = regexp.MustCompile(`\D`)

func phoneToNumber(phone string) string {
	if phone == "" {
		return phone
	}
	return nonDigitRegexp.ReplaceAllString(phone, "")
}

// SensitiveFiltering 物流信息过滤敏感词和勾选hide chinese cities功能
func (t *TrackPageService) SensitiveFiltering(info []definition.ReturnInfo) []definition.ReturnInfo {
	sensitiveWorld := t.trackGlobalData.TrackSettingEntity.HideSetting
	if len(info) == 0 {
		return info
	}
	canUseWordMasking := t.trackGlobalData.PermissionDomain.VerifyPermission(domain.WordMasking)

	if canUseWordMasking && len(sensitiveWorld) != 0 {
		hideWordsHandler := handlers.NewHideWordsHandler()
		hideWordsHandler.InitSetting(sensitiveWorld)

		info = hideWordsHandler.HandleHideWords(info)
	} else {
		// 处理地址
		for k := len(info) - 1; k >= 0; k-- {
			locationStartArr := []string{"-", ",", "，"}
			item := info[k]
			location := strings.TrimSpace(item.Location)
			// 优化文本显示 location
			if location != "" {
				for _, start := range locationStartArr {
					// 去掉开头的特殊符号
					location = strings.TrimPrefix(location, start)
				}
			}
			if k < len(info) {
				// 针对地图添加地址
				info[k].LocationMap = location
			}
		}
	}
	return info
}

// HandleInfoStatus 处理物流信息的展示状态
func (t *TrackPageService) HandleInfoStatus(info []definition.ReturnInfo, trackNumber definition.TrackNumberData, showYear bool) ([]definition.ReturnInfo, map[config.StatusNodeNumber]definition.ReturnInfo) {
	statusNumber := config.StatusNodeNumber(trackNumber.TrackStatus)
	// 节点时间和状态
	statusNode := make(map[config.StatusNodeNumber]definition.ReturnInfo)
	// 是否存在签收子状态
	isExitsDelivered := false
	isExitsInTransit := false

	var transitKey, infoLength int
	infoLength = len(info)
	if infoLength != 0 {
		var checkpointStatus string
		for key, value := range info {
			// 时间转换
			if value.CheckpointDate == "" || value.CheckpointDeliveryStatus == "" {
				continue
			}

			info[key].StatusDescription = value.TrackingDetail
			info[key].Details = value.Location
			info[key].CheckpointStatus = value.CheckpointDeliveryStatus
			checkpointStatus = value.CheckpointDeliveryStatus

			info[key].Substatus = value.CheckpointDeliverySubstatus
			value.Substatus = value.CheckpointDeliverySubstatus

			// transit 第二次出现时将前一次出现的状态修改为空白
			if transitKey != 0 && checkpointStatus == "transit" {
				info[transitKey].CheckpointStatus = "blank"
				transitKey = 0
			}
			// 记录上一次标记 transit 的位置
			if checkpointStatus == "transit" {
				// 不是notfound的
				if !strings.Contains(value.Substatus, "notfound") {
					if key+1 < infoLength && !strings.Contains(info[key+1].CheckpointDeliverySubstatus, "notfound") {
						transitKey = key
					}
				} else {
					info[key].CheckpointStatus = "blank"
					checkpointStatus = "notfound"
				}
			}
			// 先试试字符串有没有时区信息
			checkpointDate, err := utils.ParseTimeManual(value.CheckpointDate)
			if err != nil {
				checkpointDate = carbon.Parse(value.CheckpointDate).StdTime()
			}
			// 时间格式转换 根据主题区分计算 showYear 为 true 时,是classic模板
			timeFormat := utils.HandleTimeFormatSet(t.trackGlobalData.TrackThemeEntity.ClassicSetting.TrackingHistory.DateFormat,
				t.trackGlobalData.TrackThemeEntity.ClassicSetting.TrackingHistory.TimeFormat, !showYear, false)
			info[key].Date = checkpointDate.Format(timeFormat)
			value.Date = value.CheckpointDate
			value.Name = t.trackGlobalData.TrackThemeEntity.Translation[checkpointStatus]
			status := utils.GetStatusConfig(0)[checkpointStatus].Status
			if !(status == 0 || status == -1) {
				statusNode[status] = value
			}
			// 20210715 检查物流信息列表中是否存在签收关键词 start
			if statusNumber == 4 && isExitsDelivered == false && status == 4 {
				isExitsDelivered = true
			}
			if statusNumber == 2 && isExitsInTransit == false && status == 2 {
				isExitsInTransit = true
			}
			// 20210715 检查物流信息列表中是否存在签收关键词 end
		}
		// 20210715 如果状态时签收 但是不存在签收节点 那么将第一条设置为签收节点 start
		if (isExitsDelivered == false && (statusNumber == config.DeliveredStatusNumber || statusNumber == config.ExceptionStatusNumber)) || (isExitsInTransit == false && statusNumber == config.TransitStatusNumber) {
			// 提取第一条信息
			value := info[0]
			// 时间转换
			if value.Date != "" && value.CheckpointStatus != "" {
				info[0].CheckpointStatus, checkpointStatus = domain.StatusKey[statusNumber], domain.StatusKey[statusNumber]
				value.Name = t.trackGlobalData.TrackThemeEntity.Translation[checkpointStatus]
				statusNode[statusNumber] = value
			}
		}
	} else if statusNumber == config.DeliveredStatusNumber || statusNumber == config.ExceptionStatusNumber || statusNumber == config.TransitStatusNumber {
		date := trackNumber.UpdatedAt
		if trackNumber.LastDate != 0 {
			date = time.Unix(int64(trackNumber.LastDate), 0)
		}
		checkpointStatus := domain.StatusKey[statusNumber]
		statusNode[statusNumber] = definition.ReturnInfo{
			Date: date.Format(time.DateTime),
			Name: t.trackGlobalData.TrackThemeEntity.Translation[checkpointStatus],
		}
	}
	return info, statusNode
}

// HandleTrackingNode 依据用户全局设置的节点信息、单个单号设置的节点信息、订单创建时间、发货时间 返回节点对应信息和对应时间
func (t *TrackPageService) HandleTrackingNode(statusNode map[config.StatusNodeNumber]definition.ReturnInfo, trackInfoCreatedAt int) (map[config.StatusNodeNumber]definition.ReturnInfo, definition.StatusNum) {
	orderFulfillTime := t.trackGlobalData.OrderRecord.OrderFulfillTime
	// 记录物流信息中的状态
	oldStatusNode := make(map[config.StatusNodeNumber]definition.ReturnInfo)
	for i, info := range statusNode {
		oldStatusNode[i] = info
	}
	// 初始化返回数据
	var statusNum, customStatusNum, newCustomStatusNum, nliStatusNum definition.StatusNum
	//如果订单创建时间为空
	if t.trackGlobalData.OrderRecord.OrderCreateTime == 0 {
		return statusNode, statusNum
	}
	nodeKeyOrdered := "ordered"
	nodeKeyOrderReady := "order_ready"
	translation := t.trackGlobalData.TrackThemeEntity.Translation
	orderedName := translation[nodeKeyOrdered]
	orderReadyName := translation[nodeKeyOrderReady]
	notYetShippedName := translation["not_yet_shipped"]
	waitingUpdateName := translation["waiting_update"]
	statusKeyArr := utils.GetStatusKey([]config.StatusNodeNumber{})
	statusSetStart := statusKeyArr[0]
	statusSetEnd := statusKeyArr[len(statusKeyArr)-1]
	// 订单创建节点
	statusNode[statusSetStart] = definition.ReturnInfo{
		Date: time.Unix(int64(t.trackGlobalData.OrderRecord.OrderCreateTime), 0).Format(time.DateTime),
		Name: orderedName,
	}
	newStatusKeyArr := []config.StatusNodeNumber{statusSetStart}
	// 记录最后一个状态点的时间
	previousOrderStatusTime := t.trackGlobalData.OrderRecord.OrderCreateTime
	if t.trackGlobalData.TrackThemeEntity.ThemeCode == models.ThemeClassic &&
		t.trackGlobalData.TrackThemeEntity.ClassicSetting.ProgressBar.ShowOrderReady {
		customStatusSetting := t.trackGlobalData.TrackThemeEntity.ClassicSetting.ProgressBar.BeforeOrderReadyStatus
		orderReadyStatus := definition.StatusData{
			Name:        orderReadyName,
			Description: waitingUpdateName,
			Key:         statusSetEnd,
			Time:        orderFulfillTime,
		}
		statusNode, newStatusKeyArr, newCustomStatusNum = t.handleCustomStatus(statusNode, newStatusKeyArr, customStatusSetting, orderReadyStatus, previousOrderStatusTime)
		if orderFulfillTime > 0 {
			previousOrderStatusTime = orderFulfillTime
		}
		if newCustomStatusNum.Name != "" {
			customStatusNum = newCustomStatusNum
		}
	}
	customStatusSetting := t.trackGlobalData.TrackThemeEntity.ClassicSetting.ProgressBar.BeforeShippedStatus
	if t.trackGlobalData.TrackThemeEntity.ThemeCode == models.ThemeClassic {
		if t.trackGlobalData.TrackThemeEntity.ClassicSetting.ProgressBar.ShowShipped {
			shippedStatus := definition.StatusData{
				Name:        translation["shipped"],
				Description: "",
				Key:         config.ShippedStatusNumber,
				Time:        trackInfoCreatedAt,
			}
			customStatusSetting := t.trackGlobalData.TrackThemeEntity.ClassicSetting.ProgressBar.BeforeShippedStatus
			statusNode, newStatusKeyArr, newCustomStatusNum = t.handleCustomStatus(statusNode, newStatusKeyArr, customStatusSetting, shippedStatus, previousOrderStatusTime)
		} else if len(customStatusSetting) > 0 {
			var lastStatus definition.StatusData
			// 如果有后续时间那用后续时间
			for nodeNumber, node := range statusNode {
				if node.CheckpointDate == "" {
					continue
				}
				nodeTime, err := utils.ParseTimeManual(node.CheckpointDate)

				if err != nil {
					continue
				}
				if nodeNumber == config.TransitStatusNumber {
					logrus.Warn(node.CheckpointDate, nodeTime.Unix())
					lastStatus = definition.StatusData{
						Name:        utils.GetStatusSummary(translation, nodeNumber, ""),
						Description: node.Details,
						Key:         nodeNumber,
						Time:        int(nodeTime.Unix()),
					}
					break
				}
				if nodeNumber > config.TransitStatusNumber && lastStatus.Key != 0 {
					lastStatus = definition.StatusData{
						Name:        utils.GetStatusSummary(translation, nodeNumber, ""),
						Description: node.Details,
						Key:         nodeNumber,
						Time:        int(nodeTime.Unix()),
					}
				}
			}

			statusNode, newStatusKeyArr, newCustomStatusNum = t.handleCustomStatus(statusNode, newStatusKeyArr, customStatusSetting, lastStatus, previousOrderStatusTime)
		}

		if newCustomStatusNum.Name != "" {
			customStatusNum = newCustomStatusNum
		}
	}

	t.trackGlobalData.RootData.StatusKeyArr = newStatusKeyArr
	// 如果此单号没有物流信息 并且没有发货 将此单号状态设置为初始状态
	if len(oldStatusNode) == 0 {
		// 检查此单号是否已经发货
		if orderFulfillTime == 0 && t.trackGlobalData.OrderRecord.CustomTrackStatus != 1100 {
			statusNum = definition.StatusNum{Status: statusSetStart, Name: orderedName, StatusDescription: notYetShippedName}
		} else if customStatusNum.Name != "" {
			statusNum = customStatusNum
		} else {
			statusNum = definition.StatusNum{Status: statusSetEnd, Name: orderReadyName, StatusDescription: waitingUpdateName}
		}
	}
	// 无单号只要有状态则显示 Status 20210423
	if t.trackGlobalData.OrderRecord.CustomTrackStatus != 0 &&
		!(t.trackGlobalData.OrderRecord.CustomTrackStatus == config.OrderedNodeStatusNumber || t.trackGlobalData.OrderRecord.CustomTrackStatus == config.OrderReadyStatusNumber) {
		statusNum = t.statusDepict(t.trackGlobalData.OrderRecord.CustomTrackStatus)
	}
	// 节点状态时间调整 transit < order_ready 时 transit 取 order_ready
	// 如果发货时间 orderFulfillTime 大于签收时间 不改
	_, hasInTransit := statusNode[2]
	_, hasOrder := statusNode[1100]
	if statusNum != nliStatusNum && hasInTransit && hasOrder {
		orderTime := statusNode[1100].Date
		transitTime := statusNode[2].Date
		deliveredNode, exists := statusNode[4]
		var deliveredTime = ""

		if exists {
			deliveredTime = deliveredNode.Date
		}
		changeTime := 0
		var deliveredTimeS, orderTimeS, transitTimeS int
		if deliveredTime != "" {
			if timeS, err := time.Parse(time.DateTime, deliveredTime); err != nil {
				deliveredTimeS = int(timeS.Unix())
			}
		}
		if orderTime != "" {
			if timeS, err := time.Parse(time.DateTime, orderTime); err != nil {
				orderTimeS = int(timeS.Unix())
			}
		}
		if transitTime != "" {
			transitTimeS = int(carbon.Parse(transitTime).StdTime().Unix())
		}
		if deliveredTimeS != 0 {
			if deliveredTimeS < orderTimeS {
				changeTime = 1
			}
		}
		if orderTimeS != 0 && transitTimeS != 0 && changeTime == 0 && transitTimeS < orderTimeS {
			if trackInfo, ok := statusNode[2]; ok {
				// 修改字段值
				trackInfo.Date = statusNode[1100].Date
				// 将修改后的结构体放回 map
				statusNode[2] = trackInfo
			}
		}
	}
	for k, v := range statusNode {
		if v.Date == "" {
			continue
		}
		if trackInfo, ok := statusNode[k]; ok {
			// 修改字段值
			times, err := utils.ParseTimeManual(trackInfo.Date)
			if err == nil {
				trackInfo.Date = utils.HandleTimeFormatBySet(times.Unix(), t.trackGlobalData.TrackThemeEntity.ClassicSetting.ProgressBar.DateFormat,
					t.trackGlobalData.TrackThemeEntity.ClassicSetting.ProgressBar.TimeFormat, false, false)
			}
			// 将修改后的结构体放回 map
			statusNode[k] = trackInfo
		}
	}

	//如果这个状态存在 并且 自定义状态有大于一条
	_, exist := statusNode[config.OrderedNodeStatusNumber]
	if exist && len(t.trackGlobalData.OrderRecord.CustomStatusTime) > 0 {
		temp := statusNode[config.OrderedNodeStatusNumber]
		statusNode = map[config.StatusNodeNumber]definition.ReturnInfo{}
		for key, v := range t.trackGlobalData.OrderRecord.CustomStatusTime {
			if key == config.OrderReadyStatusNumber {
				statusNode[config.OrderedNodeStatusNumber] = temp
			}
			statusNode[key] = definition.ReturnInfo{
				Date: utils.HandleTimeFormatBySet(int64(v), t.trackGlobalData.TrackThemeEntity.ClassicSetting.ProgressBar.DateFormat,
					t.trackGlobalData.TrackThemeEntity.ClassicSetting.ProgressBar.TimeFormat, false, false),
				Name: utils.GetStatusSummary(translation, key, ""),
			}
		}
	}
	return statusNode, statusNum
}

func (t *TrackPageService) statusDepict(customTrackStatus config.StatusNodeNumber) definition.StatusNum {
	translation := t.trackGlobalData.TrackThemeEntity.Translation
	var data definition.StatusNum
	switch customTrackStatus {
	case config.TransitStatusNumber:
		data = definition.StatusNum{
			Status:            config.TransitStatusNumber,
			Name:              "transit",
			StatusDescription: translation["transit"],
		}
	case config.PickupStatusNumber:
		data = definition.StatusNum{
			Status:            config.PickupStatusNumber,
			Name:              "pickup",
			StatusDescription: translation["pickup"],
		}
	case config.DeliveredStatusNumber:
		data = definition.StatusNum{
			Status:            config.DeliveredStatusNumber,
			Name:              "delivered",
			StatusDescription: translation["delivered"],
		}
	default:
		data = definition.StatusNum{}
	}
	return data
}

func (t *TrackPageService) doSomeSetSpecialUser() {
	// <EMAIL>  110927  wildestclub.myshopify.com
	// 隐藏速卖通（cainiao）运输商名称及 logo
	if t.trackGlobalData.TrackSettingEntity.UserId != 0 {
		shop := t.trackGlobalData.TrackRequestDomain.Shop
		if shop == "wildestclub.myshopify.com" {
			t.trackGlobalData.ConfigData["hidden_courier"] = []string{"Aliexpress Standard Shipping"}
		}
	}
	// 订单状态显示问题 进度条显示 运输途中 地图上方文字显示 pending
	pageDataTracking := t.trackGlobalData.TrackingData
	translation := t.trackGlobalData.TrackThemeEntity.Translation
	transit := translation["transit"]
	if transit == "" {
		transit = "In Transit"
	}
	if len(pageDataTracking) != 0 {
		for k, value := range pageDataTracking {
			statusNode := value.StatusNode
			statusDataNum := value.StausDataNum
			if _, exist := statusNode[2]; len(statusNode) != 0 && exist && statusDataNum == 1 {
				if statusNode[2].Name != "" {
					t.trackGlobalData.TrackingData[k].Status = strings.TrimSpace(statusNode[2].Name)
				} else {
					t.trackGlobalData.TrackingData[k].Status = transit
				}
			}
			// 运输信息显示调整
			endArr := []string{".", ","}
			startArr := []string{"-", ","}
			trackInfo := value.Trackinfo
			if len(trackInfo) != 0 {
				for i, info := range trackInfo {
					statusDescription := strings.TrimSpace(info.StatusDescription)
					details := strings.TrimSpace(info.Details)
					// 判断最后是否有句号存在
					for _, v1 := range endArr {
						statusDescription = strings.TrimSuffix(statusDescription, v1)
					}
					t.trackGlobalData.TrackingData[k].Trackinfo[i].StatusDescription = statusDescription
					// 判断开头是否有特殊符号存在
					for _, v2 := range startArr {
						details = strings.TrimPrefix(details, v2)
					}
					t.trackGlobalData.TrackingData[k].Trackinfo[i].Details = details
				}
			}
		}
	}
}

func (t *TrackPageService) GetRecommendProduct(user models.Users, product string, noCache bool) []definition.TrackPageProduct {
	productId, _ := strconv.Atoi(product)
	var recommend []shopify.RecommendProduct
	var trackPageProducts []definition.TrackPageProduct
	var err error
	if productId == 1234 {
		return defaultProducts
	}
	c := cache.NewRedisCache().Tags([]string{strconv.Itoa(user.ID), "recommend"})
	cacheKey := config.CacheKey.RecommendProductKey + product
	client := shopify.API.NewClient(user.StoreName, "", shopify.WithLogger(), shopify.WithoutPathPrefix())
	if !noCache {
		productCache := c.Get(cacheKey)
		err = json.Unmarshal(productCache, &trackPageProducts)
		if err == nil && len(trackPageProducts) > 0 {
			return trackPageProducts
		}
	}
	recommend, err = client.Product.GetRecommend(int64(productId), recommendLimit)
	if err != nil {
		return trackPageProducts
	}

	for _, in := range recommend {
		if strings.Contains(in.Title, "Shipping Protection") {
			continue
		}
		if strings.Contains(in.Title, "Assurance Livraison") {
			continue
		}
		trackPageProducts = append(trackPageProducts, definition.TrackPageProduct{
			Title:              in.Title,
			HandleTitle:        in.Handle,
			Vendor:             in.Vendor,
			Url:                in.Url,
			Price:              decimal.NewFromInt(in.Price),
			Img:                in.FeaturedImage,
			ComparePrice:       decimal.NewFromInt(in.CompareAtPrice),
			ProductType:        in.Type,
			PublishedTime:      in.PublishedAt,
			ProductCreatedTime: in.CreatedAt,
		})
	}
	if len(trackPageProducts) > 4 {
		trackPageProducts = trackPageProducts[0:4]
	}
	trackProductsJson, _ := json.Marshal(trackPageProducts)
	c.SetWithTTL(cacheKey, string(trackProductsJson), 600)
	return trackPageProducts
}

func (t *TrackPageService) CheckUser(storeName string) (user *models.Users, err error) {
	if config.Get().TrackTest {
		storeName = config.Get().TestShop
	}
	return t.userRepo.FirstByStoreName(storeName)
}

func (t *TrackPageService) GetCollectionProduct(user models.Users, collection string, noCache bool, productIds string) []definition.TrackPageProduct {
	collectionId, _ := strconv.Atoi(collection)
	var trackPageProducts []definition.TrackPageProduct
	var err error
	if collectionId == 1234 {
		return defaultProducts
	}
	client := shopify.API.NewClient(user.StoreName, user.Token, shopify.WithLogger())
	c := cache.NewRedisCache().Tags([]string{strconv.Itoa(user.ID), "recommend"})
	cacheKey := config.CacheKey.CollectionProductCache + collection

	var products []graphql.Product
	if collectionId > 0 {
		if !noCache {
			productCache := c.Get(cacheKey)
			err = json.Unmarshal(productCache, &trackPageProducts)
			if err == nil && len(trackPageProducts) > 0 {
				return trackPageProducts
			}
		}
		var resp graphql.CollectionResult
		variables := struct {
			Id string `json:"id"`
		}{
			Id: fmt.Sprintf("gid://shopify/Collection/%d", collectionId),
		}
		err = client.GraphQL.Query(graphql.ProductByCollectionQuery, variables, &resp)
		products = resp.Collection.Products.Nodes
	}
	if len(productIds) > 0 {
		var queryIds string
		for key, value := range strings.Split(productIds, ",") {
			if key == 0 {
				queryIds = fmt.Sprintf("(id:%s)", value)
				continue
			}
			queryIds = fmt.Sprintf("%s OR (id:%s)", queryIds, value)
		}

		variables := struct {
			Query string `json:"query"`
		}{
			Query: queryIds,
		}
		var resp graphql.ProductResult
		err = client.GraphQL.Query(graphql.ProductByIdQuery, variables, &resp)
		products = resp.Products.Nodes
	}

	if collectionId == 0 && len(productIds) == 0 {
		var resp graphql.ProductResult
		variables := struct{}{}
		err = client.GraphQL.Query(graphql.ProductList, variables, &resp)
		products = resp.Products.Nodes
	}

	if err != nil {
		return trackPageProducts
	}
	for _, in := range products {
		if strings.Contains(in.Title, "Shipping Protection") {
			continue
		}
		if strings.Contains(in.Title, "Assurance Livraison") {
			continue
		}
		id, _ := strconv.Atoi(strings.TrimLeft(in.ID, "gid://shopify/Product/"))
		var price decimal.Decimal
		if !in.PriceRange.MinVariantPrice.Amount.IsZero() {
			price = (*in.PriceRange.MinVariantPrice.Amount).Mul(decimal.NewFromInt(100))
		}
		trackPageProducts = append(trackPageProducts, definition.TrackPageProduct{
			Id:                 int64(id),
			Title:              in.Title,
			HandleTitle:        in.Handle,
			Url:                fmt.Sprintf("//%s/products/%s", user.StoreName, in.Handle),
			Price:              price,
			Img:                in.FeaturedMedia.Preview.Image.URL,
			ProductCreatedTime: in.CreatedAt,
		})
	}
	if len(trackPageProducts) > 4 {
		trackPageProducts = trackPageProducts[0:4]
	}
	if collectionId > 0 {
		trackProductsJson, _ := json.Marshal(trackPageProducts)
		c.SetWithTTL(cacheKey, string(trackProductsJson), 600)
	}
	return trackPageProducts
}

func (t *TrackPageService) ReviewList(user *models.Users, data []impl.CourierTrack, lang string) interface{} {
	var returnData struct {
		Review []models.Reviews         `json:"review"`
		Tags   []map[string]interface{} `json:"tags"`
	}
	for _, row := range data {
		review, err := t.reviewRep.GetReviewWithTags(user.ID, row)
		if err != nil {
			continue
		}
		returnData.Review = append(returnData.Review, review)
	}
	activeTheme, _ := t.trackPageSettingRepo.GetUserActiveTheme(user.ID)
	switch activeTheme.ThemeCode {
	case models.ThemeModern:
		returnData.Tags = t.reviewRep.GetTagTranslationByLang(lang, models.ThemeModern)
	default:
		returnData.Tags = t.reviewRep.GetTagTranslationByLang(lang, models.ThemeClassic)
	}

	return returnData
}

func (t *TrackPageService) SaveReview(userId int, post impl.ReviewPostData) models.Reviews {
	review, _ := t.reviewRep.UpdateOrCreate(userId, post)
	return review
}

func (t *TrackPageService) SaveTag(review models.Reviews, tags []int) {
	for _, tagId := range tags {
		err := t.reviewRep.SaveReviewTag(review, tagId)
		if err != nil {
			continue
		}
	}
}

func (t *TrackPageService) OrderProduct(user models.Users, order string) ([]definition.OrderProductImgData, error) {
	orderRecord := t.orderRecordRepo.FirstByOrderNameWithUser(user.ID, order)
	if orderRecord.ID == 0 {
		return nil, errors.New("order not found")
	}
	var orderProductHandler handlers.OrderProductHandler
	orderProductHandler.Handle(orderRecord.User, orderRecord.OrderId)
	return t.orderProductRep.GetImgInfoByOrderId(orderRecord.OrderId, user.ID), nil
}

// handleShippedStatus 处理 classic 模板 shipped 状态显示
func (t *TrackPageService) handleShippedStatus(statusNode map[config.StatusNodeNumber]definition.ReturnInfo, trackInfoCreatedAt int) map[config.StatusNodeNumber]definition.ReturnInfo {
	//customStatusSetting := t.trackGlobalData.TrackThemeEntity.ClassicSetting.ProgressBar.BeforeShippedStatus
	//for _, customStatus := range customStatusSetting {
	//
	//}
	translation := t.trackGlobalData.TrackThemeEntity.Translation
	statusSetShipped := config.ShippedStatusNumber

	var trackInfoCreateDate string
	if trackInfoCreatedAt != 0 {
		trackInfoCreateDate = time.Unix(int64(trackInfoCreatedAt), 0).Format(time.DateTime)
	}
	shippedName := translation["shipped"]
	statusNode[statusSetShipped] = definition.ReturnInfo{
		Date: trackInfoCreateDate,
		Name: shippedName,
	}
	return statusNode
}

// ProductClick 记录推荐产品点击
func (t *TrackPageService) ProductClick(userId int, productId string) {
	_ = t.trackPageProductClickRep.ProductClick(userId, productId)
}

// handleCustomStatus 处理 classic 模板 custom status 显示
// 参数说明:
// - statusTime: 下一个状态点时间戳;
// - lastOrderStatusTime: 目前最后一条状态点的时间戳;
func (t *TrackPageService) handleCustomStatus(statusNode map[config.StatusNodeNumber]definition.ReturnInfo, statusKeyArr []config.StatusNodeNumber, customStatusSetting []config.CustomStatusItem, statusData definition.StatusData, previousOrderStatusTime int) (map[config.StatusNodeNumber]definition.ReturnInfo, []config.StatusNodeNumber, definition.StatusNum) {
	var customStatusNum definition.StatusNum
	statusTime := statusData.Time
	statusKey := statusData.Key
	statusName := statusData.Name
	description := statusData.Description
	var lastStatus config.StatusNodeNumber
	lastStatus = statusKeyArr[len(statusKeyArr)-1]
	nowTime := int(time.Now().Unix())
	// 遍历自定义状态设置
	for _, customStatus := range customStatusSetting {

		if !customStatus.Checked {
			continue
		}
		// 当前可设置的节点时间
		checkTime := previousOrderStatusTime + (customStatus.Days * int(time.Hour.Seconds()) * 24)
		// 下一个节点可设置的最小时间
		previousOrderStatusTime = checkTime
		lastStatus++
		// 错开固定状态点
		if lastStatus == config.OrderedNodeStatusNumber || lastStatus == config.OrderReadyStatusNumber || lastStatus == config.ShippedStatusNumber {
			lastStatus++
		}
		customStatusTime := ""
		// 已经抵达下一个状态点, 并且自定义状态的时间比下一个状态的时间大的话直接使用下一个状态的时间
		if statusTime != 0 && checkTime >= statusTime {
			customStatusTime = utils.HandleTimeFormatBySet(int64(statusTime), t.trackGlobalData.TrackThemeEntity.ClassicSetting.ProgressBar.DateFormat,
				t.trackGlobalData.TrackThemeEntity.ClassicSetting.ProgressBar.TimeFormat, false, false)
		} else if (statusTime == 0 && checkTime < nowTime) || (statusTime != 0 && checkTime < statusTime) {
			// 若未到下一个状态点, 则需要检查自定义时间点是否小于当前时间, 小于当前时间的话说明已抵达该自定义状态
			customStatusTime = utils.HandleTimeFormatBySet(int64(checkTime), t.trackGlobalData.TrackThemeEntity.ClassicSetting.ProgressBar.DateFormat,
				t.trackGlobalData.TrackThemeEntity.ClassicSetting.ProgressBar.TimeFormat, false, false)
			customStatusNum.Status = lastStatus
			customStatusNum.Name = customStatus.Name
			customStatusNum.StatusDescription = customStatus.Description
		}
		statusNode[lastStatus] = definition.ReturnInfo{
			Date: customStatusTime,
			Name: customStatus.Name,
		}
		statusKeyArr = append(statusKeyArr, lastStatus)
	}
	// 当前状态节点时间
	var statusDate string
	if statusTime != 0 {
		statusDate = time.Unix(int64(statusTime), 0).Format(time.DateTime)
		customStatusNum.Status = statusKey
		customStatusNum.Name = statusName
		customStatusNum.StatusDescription = description
	}
	if statusKey != 0 {
		statusNode[statusKey] = definition.ReturnInfo{
			Date: statusDate,
			Name: statusName,
		}
		statusKeyArr = append(statusKeyArr, statusKey)
	}
	return statusNode, statusKeyArr, customStatusNum
}

func NewTrackPageService() *TrackPageService {
	return &TrackPageService{
		trackPageSettingRepo:     &impl.TrackPageSetting{},
		trackPageThemeRepo:       &impl.TrackPageTheme{},
		reviewRep:                &impl.Review{},
		userRepo:                 &impl.User{},
		orderProductRep:          &impl.OrderProduct{},
		trackGlobalData:          definition.TrackPageGlobalData{},
		trackPageProductClickRep: &impl.TrackPageProductClicks{},
	}
}
