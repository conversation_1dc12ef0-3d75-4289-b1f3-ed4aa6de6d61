package queue

import (
	"context"
	"sync"
)

type Job struct {
	Name     string
	Ctx      context.Context
	Function func(context.Context) error
}

type JobQueue struct {
	queue chan Job
	wg    sync.WaitGroup
}

func NewJobQueue(workerCount int) *JobQueue {
	q := &JobQueue{
		queue: make(chan <PERSON>, 100),
	}

	// 启动工作线程
	for i := 0; i < workerCount; i++ {
		q.wg.Add(1)
		go q.worker()
	}

	return q
}

func (q *JobQueue) worker() {
	defer q.wg.Done()

	for job := range q.queue {
		// 执行任务
		err := job.Function(job.Ctx)
		if err != nil {
			// 记录错误
		}
	}
}

func (q *JobQueue) Enqueue(job Job) {
	q.queue <- job
}

func (q *JobQueue) Stop() {
	close(q.queue)
	q.wg.Wait()
}
