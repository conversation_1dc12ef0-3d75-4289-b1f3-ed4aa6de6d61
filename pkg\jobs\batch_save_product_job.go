package jobs

import (
	"encoding/json"
	"fmt"
	"github.com/sirupsen/logrus"
	"tmshopify/pkg/shopify/graphql"
	"tmshopify/store/database"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type BatchSaveProductJob struct {
	BatchSaveProductJobStruct
	DispatchAble
}
type BatchSaveProductJobStruct struct {
	Products []graphql.ProductInfo
	User     models.Users
}

func (p *BatchSaveProductJob) Handle() {
	orderProduct := impl.OrderProduct{}
	logrus.Warn("1")
	// 获取 product_ids
	var productIDs []string
	for _, pro := range p.Products {
		productIDs = append(productIDs, fmt.Sprintf("gid://shopify/Product/%s", pro.ProductID))
	}
	// 如果 product_ids 为空，则记录错误日志并返回
	if len(productIDs) == 0 {
		logrus.Warn("empty product_id", nil)
		return
	}
	jumpUserKey := fmt.Sprintf("jump_user_because_is_no_auth_%d", p.User.ID)
	val, err := database.RS.Get(jumpUserKey)
	if err != nil || string(val) == "" {
		for _, pro := range p.Products {
			// 尝试保存订单产品信息
			err := orderProduct.UpdateOrCreate(map[string]interface{}{
				"user_id":        p.User.ID,
				"fulfillment_id": pro.FulfillmentID,
				"line_item_id":   pro.LineItemID,
			},
				map[string]interface{}{
					"order_id":   pro.OrderID,
					"variant_id": pro.VariantID,
					"product_id": pro.ProductID,
					"quantity":   pro.Quantity,
					"title":      pro.Title,
					"sku":        pro.SKU,
					"price":      pro.Price,
					"image":      pro.Image,
					"handle":     pro.Handle,
					"name":       pro.Name,
				})
			if err != nil {
				logrus.Error("save one of order product fail", map[string]interface{}{"exception": err.Error(), "product": p, "key": "key"})
			} else {
				logrus.Warning("save one of order product", map[string]interface{}{"order_product": pro.OrderID, "user_id": p.User.ID})
			}
		}
	} else {
		logrus.Error("jump_fetch_user_because_is_no_auth", map[string]interface{}{"user": p.User.ID})
	}
}

func (p *BatchSaveProductJob) Serialize() []byte {
	res, _ := json.Marshal(
		BatchSaveProductJobStruct{
			Products: p.Products,
			User:     p.User,
		})
	return res
}
func (p *BatchSaveProductJob) Deserialize(data []byte) error {
	return json.Unmarshal(data, &p.BatchSaveProductJobStruct)
}
func (p *BatchSaveProductJob) Dispatch() {
	data := p.Serialize()
	// todo send in queue
	fmt.Println(data)
}
func (p *BatchSaveProductJob) DispatchSync() {
	p.Handle()
	logrus.Warn("3")

}
