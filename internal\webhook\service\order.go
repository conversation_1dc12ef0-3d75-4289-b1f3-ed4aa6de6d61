package service

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"tmshopify/config"
	"tmshopify/pkg/shopify"
	"tmshopify/pkg/shopify/webhook"
	"tmshopify/store/database"
	"tmshopify/store/repo/impl"
)

type Order struct {
	userRepo *impl.User
}

type OrderService interface {
	OrderCreate(c *gin.Context) bool
	OrderUpdate(c *gin.Context) bool
}

func (o *Order) OrderCreate(c *gin.Context) bool {
	shop := c.GetHeader(webhook.XShopifyDomain)
	logrus.Warnf("Order from %s is %s", c.<PERSON>(webhook.XShopifyDomain), c.<PERSON>(webhook.XShopifyTopic))
	var order shopify.Order
	if err := c.ShouldBind(&order); err != nil {
		logrus.Error("the body should be shopify order, error:" + err.Error() + " shop is:" + shop)
	}
	data := struct {
		Shop string        `json:"shop"`
		Data shopify.Order `json:"data"`
	}{
		Shop: shop,
		Data: order,
	}
	orderJson, _ := json.Marshal(data)
	_, err := database.RS.SAdd(config.CacheKey.CreateOrderQueue, string(orderJson))
	if err != nil {
		logrus.Warn("redis save error" + err.Error())
		return false
	}
	return true
}

func NewOrder() *Order {
	return &Order{
		userRepo: &impl.User{},
	}
}
