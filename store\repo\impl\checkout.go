package impl

type Checkout struct {
	Helper
}
type OrderStatusSetting struct {
	ExtraContents    ExtraContents `json:"extra_contents"`
	TrackButtonColor string        `json:"track_button_color"`
	Status           Status        `json:"status"`
	Description      Description   `json:"description"`
	Others           Others        `json:"others"`
}
type ExtraContents struct {
	EstimatedDeliveryTime int `json:"estimated_delivery_time"`
	TrackingNumber        int `json:"tracking_number"`
}
type Status struct {
	Ordered           string `json:"ordered"`
	Fulfilled         string `json:"fulfilled"`
	HasShippingUpdate string `json:"has_shipping_update"`
}
type Description struct {
	Ordered           string `json:"ordered"`
	Fulfilled         string `json:"fulfilled"`
	HasShippingUpdate string `json:"has_shipping_update"`
}
type Others struct {
	EstimatedDeliveryTime string `json:"estimated_delivery_time"`
	TrackYourOrder        string `json:"track_your_order"`
	TrackingNumber        string `json:"tracking_number"`
}
