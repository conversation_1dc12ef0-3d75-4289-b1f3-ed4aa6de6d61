package models

import (
	"time"
)

type Users struct {
	Name               string                  `gorm:"column:name;type:varchar(255);comment:用户名;NOT NULL" json:"name"`
	Token              string                  `gorm:"column:token;type:varchar(255);comment:token;NOT NULL" json:"token"`
	Vendor             string                  `gorm:"column:vendor;type:varchar(255);comment:店铺拥有者;NOT NULL" json:"vendor"`
	StoreName          string                  `gorm:"column:store_name;type:varchar(255);comment:shopify domain;NOT NULL" json:"store_name"`
	StoreRealName      string                  `gorm:"column:store_real_name;type:varchar(255);comment:店铺真实域名;NOT NULL" json:"store_real_name"`
	Phone              string                  `gorm:"column:phone;type:char(100);NOT NULL" json:"phone"`
	Email              string                  `gorm:"column:email;type:char(200);NOT NULL" json:"email"`
	Plans              int                     `gorm:"column:plans;type:mediumint(9);default:0;comment:套餐等级;NOT NULL" json:"plans"`
	PlanId             int                     `gorm:"column:plan_id;type:bigint(20);default:NULL;comment:套餐id" json:"plan_id"`
	Total              int                     `gorm:"column:total;type:int(11);default:0;comment:用户套餐总量;NOT NULL" json:"total"`
	Consume            int                     `gorm:"column:consume;type:int(11);default:0;comment:用户套餐使用;NOT NULL" json:"consume"`
	OldTotal           int                     `gorm:"column:old_total;type:int(11);default:0;comment:用户旧套���总量;NOT NULL" json:"old_total"`
	OldConsume         int                     `gorm:"column:old_consume;type:int(11);default:0;comment:用户旧套餐使用;NOT NULL" json:"old_consume"`
	FreeOrderUse       int                     `gorm:"column:free_order_use;type:int(11);default:0;comment:免费单号使用量;NOT NULL" json:"free_order_use"`
	TimeZone           string                  `gorm:"column:time_zone;type:varchar(32);comment:用户时区;NOT NULL" json:"time_zone"`
	Country            string                  `gorm:"column:country;type:varchar(255);NOT NULL" json:"country"`
	Province           string                  `gorm:"column:province;type:varchar(255);NOT NULL" json:"province"`
	City               string                  `gorm:"column:city;type:varchar(255);NOT NULL" json:"city"`
	Address            string                  `gorm:"column:address;type:varchar(255);NOT NULL" json:"address"`
	Zip                string                  `gorm:"column:zip;type:varchar(24);NOT NULL" json:"zip"`
	Longitude          string                  `gorm:"column:longitude;type:char(24);NOT NULL" json:"longitude"`
	Latitude           string                  `gorm:"column:latitude;type:char(24);NOT NULL" json:"latitude"`
	PrimaryLocale      string                  `gorm:"column:primary_locale;type:char(24);NOT NULL" json:"primary_locale"`
	Currency           string                  `gorm:"column:currency;type:char(10);NOT NULL" json:"currency"`
	ShopifyPlanName    string                  `gorm:"column:shopify_plan_name;type:varchar(30);NOT NULL" json:"shopify_plan_name"`
	ShopCreateTime     time.Time               `gorm:"column:shop_create_time;type:timestamp" json:"shop_create_time"`
	Redact             int                     `gorm:"column:redact;type:tinyint(4);default:0;comment:是否删除了;NOT NULL" json:"redact"`
	AutoSyncOn         int                     `gorm:"column:auto_sync_on;type:tinyint(4);default:1;NOT NULL" json:"auto_sync_on"`
	Rating             int                     `gorm:"column:rating;type:tinyint(4);default:0;comment:用户shopify评论星级(0未评论);NOT NULL" json:"rating"`
	IsFirstSync        int                     `gorm:"column:is_first_sync;type:tinyint(4);default:1;NOT NULL" json:"is_first_sync"`
	IsTokenError       int                     `gorm:"column:is_token_error;type:tinyint(4);default:0;NOT NULL" json:"is_token_error"`
	SyncDay            int                     `gorm:"column:sync_day;type:mediumint(9);default:30;comment:单号同步天数：1、7、30、60;NOT NULL" json:"sync_day"`
	IsActive           int                     `gorm:"column:is_active;type:tinyint(4);default:0;comment:是否活跃用户；0，否；1，是;NOT NULL" json:"is_active"`
	IsDelete           int                     `gorm:"column:is_delete;type:tinyint(4);default:0;comment:卸载店铺;NOT NULL" json:"is_delete"`
	LastLoginTime      time.Time               `gorm:"column:last_login_time;type:timestamp;comment:最后登录时间" json:"last_login_time"`
	EmailVerifiedAt    int                     `gorm:"column:email_verified_at;type:int(11)" json:"email_verified_at"`
	Password           string                  `gorm:"column:password;type:varchar(255);NOT NULL" json:"password"`
	OldId              int                     `gorm:"column:old_id;type:int(11);comment:旧用户体系id" json:"old_id"`
	OrderTracks        []OrderTracks           `gorm:"foreignKey:UserId" json:"order_tracks"`
	TrackInfos         []TrackInfos            `gorm:"foreignKey:UserId" json:"track_infos"`
	SubscriptionDetail DataSubscriptionDetails `gorm:"foreignKey:UserId" json:"subscription_detail"`
	ChargeGroupStore   ChargeGroupStores       `gorm:"foreignKey:UserId" json:"charge_group_store"`
	UserPermissions    []Permissions           `gorm:"many2many:user_permissions;ForeignKey:ID;joinForeignKey:UserId;References:ID;joinReferences:PermissionId" json:"user_permissions"`
	Model
	Session Sessions `gorm:"foreignKey:Uid" json:"session,omitempty"`
}

func (Users) TableName() string {
	return "users"
}

type AuthSession struct {
	Token string `json:"token"`
	Shop  string `json:"shop"`
}

func (u Users) AuthSession() AuthSession {
	return AuthSession{
		Shop:  u.StoreName,
		Token: u.Token,
	}
}
