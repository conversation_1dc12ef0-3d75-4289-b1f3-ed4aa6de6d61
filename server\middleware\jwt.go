package middleware

import (
	"github.com/sirupsen/logrus"
	"net/http"
	"tmshopify/pkg/auth"
	"tmshopify/server/response"

	"github.com/gin-gonic/gin"
)

func JWT(guard string) gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.<PERSON>("Authorization")
		if len(token) < 8 {
			token = ""
		} else {
			token = token[7:]
		}
		var message string
		var err error
		var claims *auth.JwtCustomClaims
		if token == "" {
			message = "token不能为空"
		} else {
			claims, err = new(auth.JwtToken).ParseToken(token)
			logrus.Warning(claims, err)
			if err != nil {
				message = err.Error()
			} else if guard != claims.Guard {
				message = "Guard 有误"
			}
		}

		if len(message) > 0 {
			response.Fail(c, &response.FailResponse{Code: http.StatusUnauthorized, Message: message})
			c.Abort()
		}
		c.Set("UserId", claims.ID)
		c.Next()
	}
}
