package affiliate

import "tmshopify/store/models"

type OauthClients struct {
	UserId               uint64 `json:"user_id"`
	Name                 string `json:"name"`
	Secret               string `json:"secret"`
	Provider             string `json:"provider"`
	Redirect             string `json:"redirect"`
	PersonalAccessClient int8   `json:"personal_access_client"`
	PasswordClient       int8   `json:"password_client"`
	Revoked              int8   `json:"revoked"`

	models.Model
}
