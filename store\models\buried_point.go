package models

type BuriedPoint struct {
	UserId        string `gorm:"column:user_id;type:int(11);default:0;NOT NULL" json:"user_id"`
	Name          string `gorm:"column:name;type:varchar(255);NOT NULL" json:"name"`
	TriggerNumber string `gorm:"column:trigger_number;type:int(11);default:0;NOT NULL" json:"trigger_number"`
	Model
}

func (BuriedPoint) TableName() string {
	return "buried_point"
}

func BuriedPointName() map[string]string {
	return map[string]string{
		"欢迎页弹窗关闭": "新人引导欢迎页弹窗关闭",
		//进行新人引导(首页)
		"欢迎页弹窗打开": "新人引导欢迎页弹窗打开",
		//完成新人引导(首页)
		"步骤三All Right": "完成新人引导",
		//通过首页轮播图跳转tracking page页面
		"carouser_track_page_jump": "通过首页轮播图跳转tracking",
		//通过首页轮播图打开intercom对话框
		"carouser_intercom_open": "通过首页轮播图打开intercom对话框",
		//关闭首页轮播图
		"carouser_close": "关闭首页轮播图",
		//评价弹窗打开
		"evaluate_model_open": "评价弹窗打开",
		//关闭评价弹窗
		"evaluate_model_close": "关闭评价弹窗",
		//评价弹窗点击五星好评
		"evaluate_rating_5": "评价弹窗点击五星好评",
		//点击付费用户弹窗upgrade按钮
		"pay_model_upgrade_click": "点击付费用户弹窗upgrade按钮",
		//付费用户弹窗
		"pay_model_open": "付费用户弹窗",
		//开启实验性功能用户点击数
		"shopify_external_yes": "开启实验性功能用户点击数",
		//关闭实验性功能用户点击数
		"shopify_external_no": "关闭实验性功能用户点击数",
		//开启功能后点击灯泡用户数
		"shopify_external_tip": "开启实验性功能后点击灯泡用户数",
		// 首页新人引导模块按钮埋点 start
		"Check orders":             "新人引导-Check orders",
		"Report issues":            "新人引导-Report issues",
		"Preview":                  "新人引导-Preview",
		"Add to store":             "新人引导-Add to store",
		"Send sample email":        "新人引导-Send sample email",
		"Send sample notification": "新人引导-Send sample notification",
		"Send sample summary":      "新人引导-Send sample summary",
		//首页新人引导模块按钮埋点

		//track page 功能记录 start
		"Online Store - Navigation": "Track Page Online Store - Navigation",
		"Faq":                       "Track Page Faq",
		"Copy Subdirectory":         "Track Page Copy Subdirectory",
		"Customize url click":       "Track Page Customize url click",
		"Customize url done":        "Track Page Customize url done",
		"Update track page":         "Track Page Update",
		"Preview track page":        "Track Page Preview",
		//track page 功能记录  end

		//11月的新首次充值弹窗按钮点击
		"first_recharge_modal_love": "first_recharge_modal_love",
		"first_recharge_modal_nope": "first_recharge_modal_nope",

		//升级套餐弹窗
		"upgrade_plan_modal_love": "upgrade_plan_modal_love",
		"upgrade_plan_modal_nope": "upgrade_plan_modal_nope",

		//挽留评论弹窗
		"retain_comment_modal_love": "retain_comment_modal_love",
		"retain_comment_modal_nope": "retain_comment_modal_nope",

		// banner以及推广键
		"pagefly_click":          "banner以及推广键-pagefly点击数",
		"dropshippingman_click":  "banner以及推广键-dropshippingman点击数",
		"insurance_click":        "banner以及推广键-insurance点击数",
		"shipwill_click":         "banner以及推广键-shipwill点击数",
		"seoant_click":           "banner以及推广键-seoant点击数",
		"loyalty_click":          "banner以及推广键-loyalty点击数",
		"banner_discount_click":  "banner以及推广键-banner_discount点击数",
		"banner_expansion_click": "banner以及推广键-banner_expansion点击数",

		// notification custom rule
		"custom_rule_click":  "Add custom rule 的点击次数",
		"custom_rule_delete": "custom rule 删除按钮的点击次数",

		//
		"double_quota_click": "想要 double quota 的用户点击数",

		"ship_aid_click": "ship_aid 的推广点击次数",
	}
}
