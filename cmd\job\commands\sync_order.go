package commands

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/channelwill/athena/ctxkeys"
	"github.com/channelwill/athena/gutils"
	"github.com/channelwill/athena/logger"
	"github.com/spf13/cobra"
)

func newSyncOrderCommand() *cobra.Command {
	var UserID int
	var FromDate, ToDate string
	cmd := &cobra.Command{
		Use:   "sync-order",
		Short: "同步订单数据",
		Long:  `从外部系统同步订单数据到本地数据库`,
		PreRunE: func(cmd *cobra.Command, args []string) error {
			// 设置日期范围
			yesterday, _ := cmd.Flags().GetBool("yesterday")
			last, _ := cmd.Flags().GetString("last")

			// 处理特殊的日期选项
			if yesterday {
				now := time.Now().AddDate(0, 0, -1)
				dateStr := now.Format("20060102")
				FromDate = dateStr
				ToDate = dateStr
			} else if last != "" {
				// 解析"last"参数，例如"7d"、"1m"等
				duration, err := parseDuration(last)
				if err != nil {
					return fmt.Errorf("无效的时间范围格式: %v", err)
				}

				end := time.Now()
				start := end.Add(-duration)

				FromDate = start.Format("20060102")
				ToDate = end.Format("20060102")
			} else {
				// 处理常规的from/to参数
				if cmd.Flags().Changed("from") {
					fromDate, _ := cmd.Flags().GetString("from")
					if fromDate != "" {
						// 验证日期格式
						if _, err := time.Parse("20060102", fromDate); err != nil {
							return fmt.Errorf("无效的开始日期格式，请使用YYYYMMDD格式: %v", err)
						}
						FromDate = fromDate
					}
				}

				if cmd.Flags().Changed("to") {
					toDate, _ := cmd.Flags().GetString("to")
					if toDate != "" {
						// 验证日期格式
						if _, err := time.Parse("20060102", toDate); err != nil {
							return fmt.Errorf("无效的结束日期格式，请使用YYYYMMDD格式: %v", err)
						}
						ToDate = toDate
					}
				}
			}

			// 验证日期范围
			if FromDate != "" && ToDate != "" {
				from, _ := time.Parse("20060102", FromDate)
				to, _ := time.Parse("20060102", ToDate)

				if from.After(to) {
					return fmt.Errorf("开始日期不能晚于结束日期")
				}
			}

			// 设置用户ID
			if cmd.Flags().Changed("user-id") {
				userID, _ := cmd.Flags().GetInt("user-id")
				if userID <= 0 {
					return fmt.Errorf("用户ID必须大于0")
				}
				UserID = userID
			}

			return nil
		},
		Run: func(cmd *cobra.Command, args []string) {
			ctx := context.Background()
			// 在这里执行实际的同步操作
			logger.Info(ctx, fmt.Sprintf("开始同步订单数据：用户ID=%d，日期范围=%s至%s", UserID, FromDate, ToDate))
			// 创建uuid作为日志log_id
			logId := gutils.Uuid()
			ctx = context.WithValue(ctx, ctxkeys.XRequestID, logId)

			logger.Info(ctx, "exec batch query")
			trackingService := initService()
			trackingService.SyncOrder(ctx, UserID, FromDate, ToDate)
		},
	}

	// 添加该任务特定的参数
	cmd.Flags().StringVar(&FromDate, "from", "", "同步订单的开始日期 (格式: YYYYMMDD)")
	cmd.Flags().StringVar(&ToDate, "to", "", "同步订单的结束日期 (格式: YYYYMMDD)")
	cmd.Flags().IntVar(&UserID, "user-id", 11845, "指定用户ID")
	cmd.Flags().Bool("yesterday", false, "同步昨天的订单数据")
	cmd.Flags().String("last", "", "同步过去一段时间的数据，如 '7d'(7天) 或 '1m'(1个月)")
	cmd.Flags().Int("parallel", 1, "并行执行的数量")

	return cmd
}

// newSyncOrderCommand 实现TaskCommandBuilder接口

// parseDuration 解析自定义持续时间格式
func parseDuration(s string) (time.Duration, error) {
	// 预期格式: "Nd", "Nw", "Nm", 其中N是数字，
	// d=天, w=周, m=月

	if len(s) < 2 {
		return 0, fmt.Errorf("格式错误")
	}

	numStr := s[:len(s)-1]
	unit := s[len(s)-1:]

	num, err := strconv.Atoi(numStr)
	if err != nil {
		return 0, fmt.Errorf("无效的数字部分: %v", err)
	}

	switch unit {
	case "d":
		return time.Duration(num) * 24 * time.Hour, nil
	case "w":
		return time.Duration(num) * 7 * 24 * time.Hour, nil
	case "m":
		return time.Duration(num) * 30 * 24 * time.Hour, nil
	default:
		return 0, fmt.Errorf("无效的时间单位: %s", unit)
	}
}
