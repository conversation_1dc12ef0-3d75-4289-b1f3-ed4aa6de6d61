package config

type couponSell struct {
	Display         string `json:"display"`
	MainTitle       string `json:"main_title"`
	Description     string `json:"description"`
	CouponCode      string `json:"coupon_code"`
	ButtonText      string `json:"button_text"`
	ButtonLink      string `json:"button_link"`
	BackgroundColor string `json:"background_color"`
}

type shipmentReview struct {
	StarColor string `json:"star_color"`
	Lang      string `json:"lang"`
}

type productList struct {
	ProductId  int    `json:"product_id"`
	ProductImg string `json:"product_img"`
}

type CustomStatusItem struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Days        int    `json:"days"`
	Icon        string `json:"icon"`
	Checked     bool   `json:"checked"`
}

type MileSetting struct {
	Switch           bool `json:"switch"`
	Icon             bool `json:"icon"`
	ToCourierWebsite bool `json:"to_courier_website"`
}

type CourierSetting struct {
	Switch    bool        `json:"switch"`
	FirstMile MileSetting `json:"first_mile"`
	LastMile  MileSetting `json:"last_mile"`
}

type TrackingNumberSetting struct {
	Switch    bool `json:"switch"`
	FirstMile bool `json:"first_mile"`
	LastMile  bool `json:"last_mile"`
}

type ClassicTheme struct {
	GoogleTranslate bool `json:"google_translate"`
	General         struct {
		Width            int    `json:"width"`
		Font             string `json:"font"`
		EstimatedShow    int    `json:"estimated_show"`
		OrderLookup      bool   `json:"order_lookup"`
		ProgressBar      bool   `json:"progress_bar"`
		TrackingHistory  bool   `json:"tracking_history"`
		CouponSell       bool   `json:"coupon_sell"`
		DetailInfo       bool   `json:"detail_info"`
		Border           bool   `json:"border"`
		TextColor        string `json:"text_color"`
		PrimaryColor     string `json:"primary_color"`
		ShipmentReview   bool   `json:"shipment_review"`
		FeaturedProducts bool   `json:"featured_products"`
		AutoLanguage     bool   `json:"auto_language"`
	} `json:"general"`
	OrderLookup struct {
		Order       bool   `json:"order"`
		Number      bool   `json:"number"`
		ButtonColor string `json:"button_color"`
		TextAbove   string `json:"text_above"`
		TextBelow   string `json:"text_below"`
		BorderCoder string `json:"border_coder"`
		Color       string `json:"color"`
		InputColor  string `json:"input_color"`
		OrderNumber string `json:"order_number"`
		Placeholder bool   `json:"placeholder"`
		Border      bool   `json:"border"`
		MergeTrack  bool   `json:"merge_track"`
	} `json:"order_lookup"`
	ProgressBar struct {
		DateFormat             int                `json:"date_format"`
		TimeFormat             int                `json:"time_format"`
		Color                  string             `json:"color"`
		ShowShipped            bool               `json:"show_shipped"`
		ShowOrderReady         bool               `json:"show_order_ready"`
		BeforeOrderReadyStatus []CustomStatusItem `json:"before_order_ready_status"`
		BeforeShippedStatus    []CustomStatusItem `json:"before_shipped_status"`
	} `json:"progress_bar"`
	TrackingHistory struct {
		DateFormat      int    `json:"date_format"`
		TimeFormat      int    `json:"time_format"`
		BackgroundColor string `json:"background_color"`
		Color           string `json:"color"`
		Map             string `json:"map"`
		TimelineColor   string `json:"timeline_color"`
	} `json:"tracking_history"`
	DetailInfo struct {
		Courier   CourierSetting        `json:"courier"`
		Number    TrackingNumberSetting `json:"number"`
		LineItems bool                  `json:"line_items"`
		Note      bool                  `json:"note"`
	} `json:"detail_info"`
	FeaturedProducts struct {
		BeforeTracking TrackCollection `json:"before_tracking"`
		AfterTracking  TrackCollection `json:"after_tracking"`
	} `json:"featured_products"`
	CouponSell     couponSell     `json:"coupon_sell"`
	ShipmentReview shipmentReview `json:"shipment_review"`
}

type ModernTheme struct {
	General struct {
		Color              string `json:"color"`
		Font               string `json:"font"`
		OrderLookup        bool   `json:"order_lookup"`
		TrackingInfo       bool   `json:"tracking_info"`
		MarketingAssetsOne bool   `json:"marketing_assets_one"`
		FeaturedProducts   bool   `json:"featured_products"`
		TextColor          string `json:"text_color"`
		PrimaryColor       string `json:"primary_color"`
		CouponSell         bool   `json:"coupon_sell"`
		MarketingAssetsTwo bool   `json:"marketing_assets_two"`
		ShipmentReview     bool   `json:"shipment_review"`
		AutoLanguage       bool   `json:"auto_language"`
	} `json:"general"`
	OrderLookup struct {
		Order       bool   `json:"order"`
		Number      bool   `json:"number"`
		ButtonColor string `json:"button_color"`
		Color       string `json:"color"`
		OrderNumber string `json:"order_number"`
		BorderColor string `json:"border_color"`
		InputColor  string `json:"input_color"`
	} `json:"order_lookup"`
	TrackingInfo struct {
		EstimatedShow     bool   `json:"estimated_show"`
		ProgressBar       bool   `json:"progress_bar"`
		TrackingHistory   bool   `json:"tracking_history"`
		ShipmentInfo      bool   `json:"shipment_info"`
		OrderInfo         bool   `json:"order_info"`
		DateFormat        int    `json:"date_format"`
		TimeFormat        int    `json:"time_format"`
		ProgressColor     string `json:"progress_color"`
		TranslationWidget bool   `json:"translation_widget"`
	} `json:"tracking_info"`
	MarketingAssetsOne []struct {
		Image string `json:"image"`
		Link  string `json:"link"`
	} `json:"marketing_assets_one"`
	FeaturedProducts struct {
		BeforeTracking struct {
			Collection int  `json:"collection"`
			Selected   int  `json:"selected"`
			Open       bool `json:"open"`
		} `json:"before_tracking"`
		AfterTracking struct {
			Collection int  `json:"collection"`
			Selected   int  `json:"selected"`
			Open       bool `json:"open"`
		} `json:"after_tracking"`
		PriceShow bool `json:"price_show"`
	} `json:"featured_products"`
	MarketingAssetsTwo []struct {
		Image string `json:"image"`
		Link  string `json:"link"`
	} `json:"marketing_assets_two"`
	CouponSell     couponSell     `json:"coupon_sell"`
	ShipmentReview shipmentReview `json:"shipment_review"`
}

var defaultCouponSell = couponSell{
	Display:         "pop_up",
	MainTitle:       "",
	Description:     "",
	CouponCode:      "",
	ButtonText:      "Get discount",
	ButtonLink:      "",
	BackgroundColor: "white",
}

type TrackCollection struct {
	Collection  interface{}   `json:"collection"`
	Selected    int           `json:"selected"`
	Open        bool          `json:"open"`
	Position    int           `json:"position,omitempty"`
	ProductList []productList `json:"product_list"`
}

var defaultShipmentReview = shipmentReview{
	StarColor: "#FF5924",
	Lang:      "en",
}

var DefaultClassicTheme = ClassicTheme{
	GoogleTranslate: false,
	General: struct {
		Width            int    `json:"width"`
		Font             string `json:"font"`
		EstimatedShow    int    `json:"estimated_show"`
		OrderLookup      bool   `json:"order_lookup"`
		ProgressBar      bool   `json:"progress_bar"`
		TrackingHistory  bool   `json:"tracking_history"`
		CouponSell       bool   `json:"coupon_sell"`
		DetailInfo       bool   `json:"detail_info"`
		Border           bool   `json:"border"`
		TextColor        string `json:"text_color"`
		PrimaryColor     string `json:"primary_color"`
		ShipmentReview   bool   `json:"shipment_review"`
		FeaturedProducts bool   `json:"featured_products"`
		AutoLanguage     bool   `json:"auto_language"`
	}{
		Width:            1200,
		Font:             "",
		EstimatedShow:    0,
		OrderLookup:      true,
		ProgressBar:      true,
		TrackingHistory:  true,
		CouponSell:       false,
		DetailInfo:       true,
		Border:           false,
		TextColor:        "#000000",
		PrimaryColor:     "#007a5c",
		ShipmentReview:   false,
		FeaturedProducts: false,
	},
	OrderLookup: struct {
		Order       bool   `json:"order"`
		Number      bool   `json:"number"`
		ButtonColor string `json:"button_color"`
		TextAbove   string `json:"text_above"`
		TextBelow   string `json:"text_below"`
		BorderCoder string `json:"border_coder"`
		Color       string `json:"color"`
		InputColor  string `json:"input_color"`
		OrderNumber string `json:"order_number"`
		Placeholder bool   `json:"placeholder"`
		Border      bool   `json:"border"`
		MergeTrack  bool   `json:"merge_track"`
	}{
		Order:       true,
		Number:      true,
		ButtonColor: "",
		TextAbove:   "",
		TextBelow:   "",
		BorderCoder: "",
		Color:       "",
		InputColor:  "",
		OrderNumber: "0",
		Placeholder: true,
		Border:      true,
		MergeTrack:  false,
	},
	ProgressBar: struct {
		DateFormat             int                `json:"date_format"`
		TimeFormat             int                `json:"time_format"`
		Color                  string             `json:"color"`
		ShowShipped            bool               `json:"show_shipped"`
		ShowOrderReady         bool               `json:"show_order_ready"`
		BeforeOrderReadyStatus []CustomStatusItem `json:"before_order_ready_status"`
		BeforeShippedStatus    []CustomStatusItem `json:"before_shipped_status"`
	}{
		DateFormat:             0,
		TimeFormat:             0,
		Color:                  "#008000",
		ShowShipped:            false,
		ShowOrderReady:         true,
		BeforeOrderReadyStatus: []CustomStatusItem{},
		BeforeShippedStatus:    []CustomStatusItem{},
	},
	TrackingHistory: struct {
		DateFormat      int    `json:"date_format"`
		TimeFormat      int    `json:"time_format"`
		BackgroundColor string `json:"background_color"`
		Color           string `json:"color"`
		Map             string `json:"map"`
		TimelineColor   string `json:"timeline_color"`
	}{
		DateFormat:      0,
		TimeFormat:      0,
		BackgroundColor: "#ffffff",
		Color:           "#000000",
		Map:             "none",
		TimelineColor:   "#f5f6fa",
	},
	DetailInfo: struct {
		Courier   CourierSetting        `json:"courier"`
		Number    TrackingNumberSetting `json:"number"`
		LineItems bool                  `json:"line_items"`
		Note      bool                  `json:"note"`
	}{
		Courier: CourierSetting{
			Switch: true,
			FirstMile: MileSetting{
				Switch:           true,
				Icon:             true,
				ToCourierWebsite: true,
			},
			LastMile: MileSetting{
				Switch:           true,
				Icon:             true,
				ToCourierWebsite: true,
			},
		},
		Number: TrackingNumberSetting{
			Switch:    true,
			LastMile:  true,
			FirstMile: true,
		},
		LineItems: true,
		Note:      true,
	},
	FeaturedProducts: struct {
		BeforeTracking TrackCollection `json:"before_tracking"`
		AfterTracking  TrackCollection `json:"after_tracking"`
	}{
		BeforeTracking: TrackCollection{
			Collection: "0",
			Selected:   0,
			Open:       false,
		},
		AfterTracking: TrackCollection{
			Collection: "0",
			Selected:   0,
			Open:       true,
			Position:   2,
		},
	},
	CouponSell:     defaultCouponSell,
	ShipmentReview: defaultShipmentReview,
}

var DefaultModernTheme = ModernTheme{
	General: struct {
		Color              string `json:"color"`
		Font               string `json:"font"`
		OrderLookup        bool   `json:"order_lookup"`
		TrackingInfo       bool   `json:"tracking_info"`
		MarketingAssetsOne bool   `json:"marketing_assets_one"`
		FeaturedProducts   bool   `json:"featured_products"`
		TextColor          string `json:"text_color"`
		PrimaryColor       string `json:"primary_color"`
		CouponSell         bool   `json:"coupon_sell"`
		MarketingAssetsTwo bool   `json:"marketing_assets_two"`
		ShipmentReview     bool   `json:"shipment_review"`
		AutoLanguage       bool   `json:"auto_language"`
	}{
		Color:              "",
		Font:               "",
		OrderLookup:        true,
		TrackingInfo:       true,
		MarketingAssetsOne: true,
		FeaturedProducts:   true,
		TextColor:          "#000000",
		PrimaryColor:       "#007a5c",
		CouponSell:         false,
		MarketingAssetsTwo: true,
		ShipmentReview:     false,
	},
	OrderLookup: struct {
		Order       bool   `json:"order"`
		Number      bool   `json:"number"`
		ButtonColor string `json:"button_color"`
		Color       string `json:"color"`
		OrderNumber string `json:"order_number"`
		BorderColor string `json:"border_color"`
		InputColor  string `json:"input_color"`
	}{
		Order:       true,
		Number:      true,
		ButtonColor: "",
		Color:       "#000000",
		OrderNumber: "0",
		BorderColor: "",
		InputColor:  "",
	},
	TrackingInfo: struct {
		EstimatedShow     bool   `json:"estimated_show"`
		ProgressBar       bool   `json:"progress_bar"`
		TrackingHistory   bool   `json:"tracking_history"`
		ShipmentInfo      bool   `json:"shipment_info"`
		OrderInfo         bool   `json:"order_info"`
		DateFormat        int    `json:"date_format"`
		TimeFormat        int    `json:"time_format"`
		ProgressColor     string `json:"progress_color"`
		TranslationWidget bool   `json:"translation_widget"`
	}{
		EstimatedShow:     false,
		ProgressBar:       false,
		TrackingHistory:   true,
		ShipmentInfo:      true,
		OrderInfo:         false,
		DateFormat:        0,
		TimeFormat:        0,
		ProgressColor:     "",
		TranslationWidget: false,
	},
	MarketingAssetsOne: []struct {
		Image string `json:"image"`
		Link  string `json:"link"`
	}{
		{
			Image: "",
			Link:  "",
		},
		{
			Image: "",
			Link:  "",
		},
	},
	FeaturedProducts: struct {
		BeforeTracking struct {
			Collection int  `json:"collection"`
			Selected   int  `json:"selected"`
			Open       bool `json:"open"`
		} `json:"before_tracking"`
		AfterTracking struct {
			Collection int  `json:"collection"`
			Selected   int  `json:"selected"`
			Open       bool `json:"open"`
		} `json:"after_tracking"`
		PriceShow bool `json:"price_show"`
	}{
		BeforeTracking: struct {
			Collection int  `json:"collection"`
			Selected   int  `json:"selected"`
			Open       bool `json:"open"`
		}{
			Collection: 0,
			Selected:   0,
			Open:       true,
		},
		AfterTracking: struct {
			Collection int  `json:"collection"`
			Selected   int  `json:"selected"`
			Open       bool `json:"open"`
		}{
			Collection: 0,
			Selected:   0,
			Open:       true,
		},
		PriceShow: true,
	},
	MarketingAssetsTwo: []struct {
		Image string `json:"image"`
		Link  string `json:"link"`
	}{
		{
			Image: "",
			Link:  "",
		},
		{
			Image: "",
			Link:  "",
		},
	},
	CouponSell:     defaultCouponSell,
	ShipmentReview: defaultShipmentReview,
}

type StatusNodeNumber int

const (
	OrderedNodeStatusNumber StatusNodeNumber = 1001
	OrderReadyStatusNumber  StatusNodeNumber = 1100
	ShippedStatusNumber     StatusNodeNumber = 1200
	BlankStatusNumber       StatusNodeNumber = -1
	PendingStatusNumber     StatusNodeNumber = 0
	InfoReceiveStatusNumber StatusNodeNumber = 1
	TransitStatusNumber     StatusNodeNumber = 2
	PickupStatusNumber      StatusNodeNumber = 3
	DeliveredStatusNumber   StatusNodeNumber = 4
	ExpiredStatusNumber     StatusNodeNumber = 5
	FailAttemptStatusNumber StatusNodeNumber = 6
	ExceptionStatusNumber   StatusNodeNumber = 7
)
