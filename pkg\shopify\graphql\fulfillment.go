package graphql

type FulfillmentLineItem struct {
	ID       string   `json:"id,omitempty"`
	Name     string   `json:"name,omitempty"`
	Quantity int      `json:"quantity,omitempty"`
	LineItem LineItem `json:"line_item,omitempty"`
}

type PresentmentMoney struct {
	Amount       string `json:"amount,omitempty"`
	CurrencyCode string `json:"currencyCode,omitempty"`
}

type ShopMoney struct {
	Amount       string `json:"amount,omitempty"`
	CurrencyCode string `json:"currencyCode,omitempty"`
}

type LineItem struct {
	Name             string `json:"name,omitempty"`
	ID               string `json:"id,omitempty"`
	Quantity         int    `json:"quantity,omitempty"`
	SKU              string `json:"sku,omitempty"`
	Title            string `json:"title,omitempty"`
	VariantTitle     string `json:"variantTitle,omitempty"`
	Image            `json:"image,omitempty"`
	OriginalTotalSet struct {
		PresentmentMoney `json:"presentmentMoney,omitempty"`
		ShopMoney        `json:"shopMoney,omitempty"`
	} `json:"originalTotalSet,omitempty"`
	DiscountTotalSet struct {
		PresentmentMoney `json:"presentmentMoney,omitempty"`
		ShopMoney        `json:"shopMoney,omitempty"`
	} `json:"DiscountTotalSet,omitempty"`
	Product `json:"product,omitempty"`
	Variant `json:"variant,omitempty"`
}
