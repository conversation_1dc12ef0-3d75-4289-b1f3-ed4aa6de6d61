package models

import (
	"database/sql"
	"time"
)

type Version string

type DataSubscriptionDetails struct {
	UserId       int          `gorm:"column:user_id;type:int(11);default:0;comment:用户id;NOT NULL" json:"user_id"`
	Plans        int          `gorm:"column:plans;type:mediumint(9);default:0;comment:用户当前套餐等级;NOT NULL" json:"plans"`
	PlanId       int          `gorm:"column:plan_id;type:bigint(20);default:NULL;comment:套餐id" json:"plan_id"`
	RecordId     int          `gorm:"column:record_id;type:bigint(20);default:NULL;comment:record 表id" json:"record_id"`
	ActivatedAt  time.Time    `gorm:"column:activated_at;type:timestamp;default:CURRENT_TIMESTAMP;comment:初次激活套餐日期;NOT NULL" json:"activated_at"`
	RenewAt      sql.NullTime `gorm:"column:renew_at;type:timestamp;comment:最近续订日期" json:"renew_at"`
	FrozenAt     sql.NullTime `gorm:"column:frozen_at;type:timestamp;comment:最近冻结日期" json:"frozen_at"`
	CanceledAt   sql.NullTime `gorm:"column:canceled_at;type:timestamp;comment:最近取消套餐日期" json:"canceled_at"`
	ExpiredAt    sql.NullTime `gorm:"column:expired_at;type:timestamp;comment:最近套餐过期日期" json:"expired_at"`
	Status       int          `gorm:"column:status;type:tinyint(4);default:0;comment:当前订阅状态;NOT NULL" json:"status"`
	Price        float64      `gorm:"column:price;type:decimal(10,2);default:0.00;comment:当前订阅金额;NOT NULL" json:"price"`
	Version      Version      `gorm:"column:version;type:char(6);default:202306;comment:当前订阅版本，无订阅则是最新;NOT NULL" json:"version"`
	PlanType     string       `gorm:"column:plan_type;type:VARCHAR(20);default:monthly;" json:"plan_type"`
	LineItemId   string       `gorm:"column:line_item_id;type:VARCHAR(100);comment:订阅lineItems 的 id;not null;" json:"line_item_id"`
	ExtraPrice   float64      `gorm:"column:extra_price;type:DECIMAL(10, 2) UNSIGNED;comment:额外单号单价;not null;default:0.05;" json:"extra_price"`
	CappedAmount float64      `gorm:"column:capped_amount;type:DECIMAL(10, 2) UNSIGNED;comment:扣费上限;not null;" json:"capped_amount"`
	BalanceUsed  float64      `gorm:"column:balance_used;type:DECIMAL(10, 2) UNSIGNED;comment:已经使用的额度;not null;" json:"balance_used"`
	Model
}

func (DataSubscriptionDetails) TableName() string {
	return "data_subscription_details"
}
