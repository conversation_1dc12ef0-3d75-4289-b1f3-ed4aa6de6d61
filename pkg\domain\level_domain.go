package domain

import (
	"encoding/json"
	"fmt"
	"time"

	"tmshopify/config"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type LevelDomain struct {
	PlanId  int
	Version models.Version
	Level   int
	Quota   int
	Price   float32
}

type PlanStruct struct {
	Price       float32        `json:"price,omitempty"`
	Count       int            `json:"count,omitempty"`
	Level       int            `json:"level,omitempty"`
	Unit        float32        `json:"unit,omitempty"`
	Plan        int            `json:"plan,omitempty"`
	Version     models.Version `json:"version,omitempty"`
	SupportType int            `json:"support_type,omitempty"`
	PlanType    int            `json:"plan_type,omitempty"`
	Id          int            `json:"id,omitempty"`
}

type PlanItem struct {
	Price        float32             `json:"price,omitempty"`
	Quota        int                 `json:"count,omitempty"`
	Level        int                 `json:"level,omitempty"`
	Version      models.Version      `json:"version,omitempty"`
	BillingCycle models.BillingCycle `json:"billing_cycle,omitempty"`
	PlanType     models.PlanType     `json:"plan_type,omitempty"`
	Id           int                 `json:"id,omitempty"`
}

type CustomPlanItem struct {
	Price               float32             `json:"price,omitempty"`
	Quota               int                 `json:"count,omitempty"`
	Level               int                 `json:"level,omitempty"`
	Version             models.Version      `json:"version,omitempty"`
	BillingCycle        models.BillingCycle `json:"billing_cycle,omitempty"`
	PlanType            models.PlanType     `json:"plan_type,omitempty"`
	Id                  int                 `json:"id,omitempty"`
	ReplaceSystemPlanId int                 `json:"replace_system_plan_id,omitempty"`
}

const (
	FREE int = iota
	SPECIAL
	BASIC
	PRO
	ENTERPRISE
)

func InitLevelDomain(planId int, version models.Version, userId int) LevelDomain {
	var domain LevelDomain
	domain.PlanId = planId
	domain.Version = version
	planItem := ConfigV2(version, userId)[planId]
	domain.Price = planItem.Price
	domain.Quota = planItem.Quota
	domain.Level = planItem.Level
	return domain
}

func ConfigV2(version models.Version, userId int) map[int]PlanItem {
	if version == "" {
		version = LATEST
	}

	if !IsValid(version) {
		return nil
	}
	var planDB []models.ChargePlans
	var planList = make(map[int]PlanItem)
	customPlan := make(map[int]CustomPlanItem)
	redisKey := config.CacheKey.ChargePlansV2
	planRS, _ := database.RS.Get(redisKey)
	_ = json.Unmarshal(planRS, &planList)

	if len(planList) != 0 {
		if userId != 0 {
			customPlanRedisKey := fmt.Sprintf("%s%d", config.CacheKey.UserChargeCustomizePlansV2, userId)
			customPlanRS, _ := database.RS.Get(customPlanRedisKey)
			// 如果 redis 信息为空, 则查询 mysql 是否存在自定义套餐
			if len(customPlanRS) == 0 {
				database.DB.Model(models.ChargePlans{}).Where("user_id = ? AND plan_type = ?", userId, models.CUSTOM).Find(&planDB)
				for _, plan := range planDB {
					planId := 0
					if plan.ReplaceSystemPlanId != nil {
						planId = *plan.ReplaceSystemPlanId
					}
					customPlan[plan.ID] = CustomPlanItem{
						Price:               plan.Price,
						Quota:               plan.Quota,
						Level:               plan.Level,
						Version:             plan.Version,
						BillingCycle:        plan.BillingCycle,
						PlanType:            plan.PlanType,
						Id:                  plan.ID,
						ReplaceSystemPlanId: planId,
					}
				}
				planJson, _ := json.Marshal(planDB)
				// 将自定义套餐写入缓存
				_ = database.RS.SetWithExpire(customPlanRedisKey, string(planJson), time.Hour*24*30)
			} else {
				_ = json.Unmarshal(customPlanRS, &customPlan)
				planList = mergePlans(planList, customPlan)
			}
		}
		return planList
	}
	if userId != 0 {
		database.DB.Model(models.ChargePlans{}).Where("version = ?", version).Where("((user_id = ? AND plan_type = ?) OR plan_type = ?)", userId, models.CUSTOM, models.SYSTEM).Find(&planDB)
	} else {
		database.DB.Model(models.ChargePlans{}).Where("version = ?", version).Where("plan_type = ?", models.SYSTEM).Find(&planDB)
	}

	for _, plan := range planDB {
		if plan.PlanType == models.SYSTEM {
			planList[plan.ID] = PlanItem{
				Price:        plan.Price,
				Quota:        plan.Quota,
				Level:        plan.Level,
				Version:      plan.Version,
				BillingCycle: plan.BillingCycle,
				PlanType:     plan.PlanType,
				Id:           plan.ID,
			}
		} else {
			planId := 0
			if plan.ReplaceSystemPlanId != nil {
				planId = *plan.ReplaceSystemPlanId
			}
			customPlan[plan.ID] = CustomPlanItem{
				Price:               plan.Price,
				Quota:               plan.Quota,
				Level:               plan.Level,
				Version:             plan.Version,
				BillingCycle:        plan.BillingCycle,
				PlanType:            plan.PlanType,
				Id:                  plan.ID,
				ReplaceSystemPlanId: planId,
			}
		}
	}
	planJson, _ := json.Marshal(planList)
	_ = database.RS.SetWithExpire(redisKey, string(planJson), time.Hour*24*30)
	if userId != 0 {
		customPlanRedisKey := fmt.Sprintf("%s%d", config.CacheKey.UserChargeCustomizePlansV2, userId)
		customPlanJson, _ := json.Marshal(customPlan)
		_ = database.RS.SetWithExpire(customPlanRedisKey, string(customPlanJson), time.Hour*24*30)
		planList = mergePlans(planList, customPlan)
	}
	return planList
}

func mergePlans(plans map[int]PlanItem, customPlans map[int]CustomPlanItem) map[int]PlanItem {
	for id, plan := range customPlans {
		plans[id] = PlanItem{
			Price:        plan.Price,
			Quota:        plan.Quota,
			Level:        plan.Level,
			Version:      plan.Version,
			BillingCycle: plan.BillingCycle,
			PlanType:     plan.PlanType,
			Id:           plan.Id,
		}
		// 删除替代的系统套餐
		if plan.ReplaceSystemPlanId != 0 {
			delete(plans, plan.ReplaceSystemPlanId)
		}
	}
	return plans
}
