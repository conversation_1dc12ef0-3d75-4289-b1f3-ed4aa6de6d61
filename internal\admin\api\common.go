package api

import (
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"tmshopify/pkg/file"
	"tmshopify/pkg/utils/paginate"
	"tmshopify/server/response"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type Common struct {
}

func (c *Common) Upload(ctx *gin.Context) {
	header, err := ctx.FormFile("file")
	if err != nil {
		response.BadRequest(ctx, "缺少上传的文件")
		return
	}

	oss := file.NewUpload("./uploads/admin")

	fileName, err := oss.SaveFile(header, ctx)
	logrus.Warnf("file name is:%s", fileName)
	if err != nil {
		logrus.Warnf("local upload fail:%s", err.Error())
		response.InternalServerError(ctx, "本地上传失败")
		return
	}
	result, err := oss.PostFile(fileName)
	if err != nil {
		logrus.Warnf("server upload fail:%s", err.Error())
		response.InternalServerError(ctx, "上传服务端失败")
		return
	}
	if result != nil {
		oss.RemoveLocalFile(fileName)
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: result.Data.FileUrl,
	})
}

func (c *Common) ShopifyCountries(ctx *gin.Context) {
	type Country struct {
		Name string `json:"name"`
		Code string `json:"code"`
		Id   int    `json:"id"`
	}
	rep := &impl.ShopifyCountry{}
	keyword := ctx.Query("keyword")
	var result []models.ShopifyCountries
	if keyword == "" {
		result, _, _ = rep.Paginate(&paginate.Param{Page: 1, PageSize: 20})
	} else {
		result = rep.SearchCountryByNameField(keyword)
	}
	var data []Country
	if len(result) > 0 {
		for _, country := range result {
			data = append(data, Country{
				Id:   country.ID,
				Name: country.Name,
				Code: country.Code,
			})
		}
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: data,
	})
}

func (c *Common) Couriers(ctx *gin.Context) {
	type Courier struct {
		Name string `json:"name"`
		Code string `json:"code"`
		Id   int    `json:"id"`
	}
	rep := &impl.Courier{}
	keyword := ctx.Query("keyword")
	var result []models.Couriers
	if keyword == "" {
		result, _, _ = rep.Paginate(&paginate.Param{Page: 1, PageSize: 20})
	} else {
		result = rep.SearchCourierByNameField(keyword)
	}
	var data []Courier
	if len(result) > 0 {
		for _, courier := range result {
			data = append(data, Courier{
				Id:   courier.ID,
				Name: courier.CompanyName,
				Code: courier.CompanyCode,
			})
		}
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: data,
	})
}
