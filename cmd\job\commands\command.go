package commands

import (
	"tmshopify/cmd/job/commands/export"
	"tmshopify/cmd/job/commands/queue"
	"tmshopify/cmd/job/commands/stats"
)

// InitTaskCommands 初始化所有任务命令
func InitTaskCommands() {
	// 添加通用任务命令
	rootCmd.AddCommand(newSyncOrderCommand())

	// export
	rootCmd.AddCommand(export.NewCommand())

	// queue
	rootCmd.AddCommand(queue.NewCommand())

	// stats
	rootCmd.AddCommand(stats.NewCommand())
}
