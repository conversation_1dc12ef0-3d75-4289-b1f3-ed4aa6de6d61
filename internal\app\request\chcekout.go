package request

import "github.com/thedevsaddam/govalidator"

type CheckoutSetting struct {
}

func (c *CheckoutSetting) Rules() govalidator.MapData {
	return govalidator.MapData{
		"shop":       []string{"required", "max:255"},
		"order_name": []string{"max:255"},
		"order_id":   []string{"max:255"},
		"nums":       []string{"max:255"},
	}
}

type OrderStatusWidgetRequest struct {
}

func (o *OrderStatusWidgetRequest) Rules() govalidator.MapData {
	return govalidator.MapData{
		"shop":       []string{"required", "max:255"},
		"order_name": []string{"required", "max:255"},
	}
}
