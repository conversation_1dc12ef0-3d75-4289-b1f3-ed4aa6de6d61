package models

type Couriers struct {
	CompanyName     string `gorm:"column:company_name;type:varchar(255);comment:快递公司名称;NOT NULL" json:"company_name"`
	Lang            string `gorm:"column:lang;type:char(255);comment:语言;NOT NULL" json:"lang"`
	CompanySort     string `gorm:"column:company_sort;type:char(255);comment:快递公司类型;NOT NULL" json:"company_sort"`
	CompanyCode     string `gorm:"column:company_code;type:char(255);comment:快递公司简码;NOT NULL" json:"company_code"`
	CountryCode     string `gorm:"column:country_code;type:char(255);comment:所属国家二字简码;NOT NULL" json:"country_code"`
	CompanyNum      uint   `gorm:"column:company_num;type:int(10) unsigned;comment:公司数字代号;NOT NULL" json:"company_num"`
	CompanyUrl      string `gorm:"column:company_url;type:varchar(255);comment:快递公司官方url;NOT NULL" json:"company_url"`
	CompanyTel      string `gorm:"column:company_tel;type:varchar(255);comment:快递公司官方电话;NOT NULL" json:"company_tel"`
	CompanyOrderBy  int    `gorm:"column:company_order_by;type:int(11);default:0;comment:排序;NOT NULL" json:"company_order_by"`
	IsVerify        uint   `gorm:"column:is_verify;type:tinyint(3) unsigned;default:0;comment:是否通过审核(0否,1是);NOT NULL" json:"is_verify"`
	CompanyBigArea  uint   `gorm:"column:company_big_area;type:tinyint(3) unsigned;default:0;comment:公司所属洲;NOT NULL" json:"company_big_area"`
	IsSupport       uint   `gorm:"column:is_support;type:tinyint(3) unsigned;default:1;comment:是否支持查询(1是,0否);NOT NULL" json:"is_support"`
	CompanyEmail    string `gorm:"column:company_email;type:char(255)" json:"company_email"`
	TrackUrl        string `gorm:"column:track_url;type:text;comment:快递官网查询入口链接;NOT NULL" json:"track_url"`
	SupportLanguage string `gorm:"column:support_language;type:varchar(255);default:[];comment:支持的语言;NOT NULL" json:"support_language"`
	Model
}

func (Couriers) TableName() string {
	return "couriers"
}
