package stats

import (
	"context"
	"time"

	"gorm.io/gorm"

	"tmshopify/internal/domain/entity"
	"tmshopify/internal/domain/repo/stats"
)

var _ stats.FunnelRepository = (*funnelRepoImpl)(nil)

type funnelRepoImpl struct {
	db *gorm.DB
}

// NewFunnelRepository 创建付费转化漏斗统计Repository实例
func NewFunnelRepository(db *gorm.DB) stats.FunnelRepository {
	return &funnelRepoImpl{db: db}
}

// CountNewInstallUsersInTimeRange 统计时段内新安装注册的用户数
// SQL: SELECT COUNT(*) FROM data_paid_conversion_logs WHERE create_time BETWEEN ? AND ?
func (r *funnelRepoImpl) CountNewInstallUsersInTimeRange(ctx context.Context, startTime, endTime time.Time) (int64, error) {
	// 转换时间为Unix时间戳
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.DataPaidConversionLog{}).
		Where("create_time BETWEEN ? AND ?", startTimestamp, endTimestamp).
		Count(&count).Error

	return count, err
}

// CountTrialUsersInTimeRange 统计时段内开启免费试用注册的用户数
// SQL: SELECT COUNT(*) FROM data_paid_conversion_logs WHERE create_time BETWEEN ? AND ? AND trial_time > ?
func (r *funnelRepoImpl) CountTrialUsersInTimeRange(ctx context.Context, startTime, endTime time.Time) (int64, error) {
	// 转换时间为Unix时间戳
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.DataPaidConversionLog{}).
		Where("create_time BETWEEN ? AND ? AND trial_time > ?", startTimestamp, endTimestamp, 0).
		Count(&count).Error

	return count, err
}

// CountPaidConversionUsersInTimeRange 统计时段内付费转化注册的用户数
// SQL: SELECT COUNT(*) FROM data_paid_conversion_logs WHERE create_time BETWEEN ? AND ? AND trial_time > ? AND payment_time > trial_time
func (r *funnelRepoImpl) CountPaidConversionUsersInTimeRange(ctx context.Context, startTime, endTime time.Time) (int64, error) {
	// 转换时间为Unix时间戳
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.DataPaidConversionLog{}).
		Where("create_time BETWEEN ? AND ? AND trial_time > ? AND payment_time > trial_time", startTimestamp, endTimestamp, 0).
		Count(&count).Error

	return count, err
}

// CountRenewalUsersInTimeRange 统计时段内次月续费注册的用户数
// SQL: SELECT COUNT(*) FROM data_paid_conversion_logs WHERE create_time BETWEEN ? AND ? AND payment_time > ? AND renewal_time > payment_time
func (r *funnelRepoImpl) CountRenewalUsersInTimeRange(ctx context.Context, startTime, endTime time.Time) (int64, error) {
	// 转换时间为Unix时间戳
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.DataPaidConversionLog{}).
		Where("create_time BETWEEN ? AND ? AND payment_time > ? AND renewal_time > payment_time", startTimestamp, endTimestamp, 0).
		Count(&count).Error

	return count, err
}

// GetUsersWithin60DaysAndStatus 获取注册60天内且指定状态的付费转化记录
// SQL: SELECT * FROM data_paid_conversion_logs WHERE create_time >= ? AND status = ?
func (r *funnelRepoImpl) GetUsersWithin60DaysAndStatus(ctx context.Context, status int) ([]*entity.DataPaidConversionLog, error) {
	// 计算60天前的Unix时间戳
	sixtyDaysAgo := time.Now().AddDate(0, 0, -60).Unix()

	var users []*entity.DataPaidConversionLog
	err := r.db.WithContext(ctx).
		Where("create_time >= ? AND status = ?", sixtyDaysAgo, status).
		Find(&users).Error

	return users, err
}

// UpdatePaidConversionLog 更新付费转化记录
// SQL: UPDATE data_paid_conversion_logs SET field1=?, field2=?, ... WHERE id = ?
func (r *funnelRepoImpl) UpdatePaidConversionLog(ctx context.Context, log *entity.DataPaidConversionLog) error {
	return r.db.WithContext(ctx).Save(log).Error
}

// GetPaidConversionLogByUserID 根据用户ID获取付费转换记录
// SQL: SELECT * FROM data_paid_conversion_logs WHERE user_id = ?
func (r *funnelRepoImpl) GetPaidConversionLogByUserID(ctx context.Context, userID int64) (*entity.DataPaidConversionLog, error) {
	var log entity.DataPaidConversionLog
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		First(&log).Error

	return &log, err
}

// CreatePaidConversionLog 创建付费转换记录
// SQL: INSERT INTO data_paid_conversion_logs (user_id, status, create_time, ...) VALUES (?, ?, ?, ...)
func (r *funnelRepoImpl) CreatePaidConversionLog(ctx context.Context, log *entity.DataPaidConversionLog) error {
	return r.db.WithContext(ctx).Create(log).Error
}
