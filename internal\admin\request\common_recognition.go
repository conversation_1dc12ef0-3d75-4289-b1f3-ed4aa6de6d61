package request

var CreateCommonRule = map[string]string{
	"from_user":         "required,number,max=11",
	"rule_content":      "required,max=255",
	"example_track":     "required,max=255",
	"courier":           "required,max=255",
	"applicable_length": "required,number",
	"only_use_set":      "required,number",
}

var UpdateCommonRule = map[string]string{
	"from_user":         "required,number,max=11",
	"rule_content":      "required,max=255",
	"example_track":     "required,max=255",
	"courier":           "required,max=255",
	"applicable_length": "required,number",
	"only_use_set":      "required,number",
	"id":                "required,number",
}
