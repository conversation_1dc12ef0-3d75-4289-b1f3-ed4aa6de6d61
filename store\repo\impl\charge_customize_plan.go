package impl

import (
	"gorm.io/gorm"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type ChargeCustomizePlanRepository interface {
	HelperInterface
	GetChargeCustomizePlan(userId int64, isDelete int) []models.ChargeCustomizePlans
	CreatePlan(m *models.ChargeCustomizePlans) error
	UpdatePlan(m *models.ChargeCustomizePlans) error
	ChangePlanStatus(id string, status string) error
	CheckHasSameByPlan(plan models.ChargeCustomizePlans) bool
}

type ChargeCustomizePlanRepositoryImpl struct {
	Helper
}

func (c *ChargeCustomizePlanRepositoryImpl) GetChargeCustomizePlan(userId int64, isDelete int) []models.ChargeCustomizePlans {
	var customizePlans []models.ChargeCustomizePlans
	var query *gorm.DB
	if isDelete == 2 {
		query = database.DB.Model(&models.ChargeCustomizePlans{}).Unscoped()
	} else if isDelete == 1 {
		query = database.DB.Model(&models.ChargeCustomizePlans{}).Unscoped().Where("is_delete = ?", isDelete).Find(&customizePlans)
	} else {
		query = database.DB.Model(&models.ChargeCustomizePlans{})
	}
	if userId > 0 {
		query.Where("user_id = ?", userId).Find(&customizePlans)
	} else {
		query.Find(&customizePlans)
	}
	return customizePlans
}

func (c *ChargeCustomizePlanRepositoryImpl) CreatePlan(m *models.ChargeCustomizePlans) error {
	return database.DB.Create(m).Error
}
func (c *ChargeCustomizePlanRepositoryImpl) UpdatePlan(m *models.ChargeCustomizePlans) error {
	return database.DB.Unscoped().Omit("created_at").Save(m).Error
}
func (c *ChargeCustomizePlanRepositoryImpl) ChangePlanStatus(id string, status string) error {
	if status == "0" {
		return database.DB.Model(&models.ChargeCustomizePlans{}).Unscoped().Where("id = ? ", id).Update("deleted_at", nil).Error
	} else {
		return c.DeleteById(&models.ChargeCustomizePlans{}, id)
	}
}
func (c *ChargeCustomizePlanRepositoryImpl) CheckHasSameByPlan(customizePlan models.ChargeCustomizePlans) bool {
	var count int64
	query := database.DB.Model(&models.ChargeCustomizePlans{}).Unscoped().Where("user_id = ? and quota = ? and version = ? and price = ?", customizePlan.UserId, customizePlan.Version, customizePlan.Price)
	if customizePlan.ID > 0 {
		query = query.Where("id != ?", customizePlan.ID)
	}
	query.Count(&count)
	return count > 0
}
