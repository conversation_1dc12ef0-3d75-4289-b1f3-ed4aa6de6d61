package service

import (
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type UserInterface interface {
	GetUserByStoreName(storeName string) (user *models.Users, err error)
	GetUserByToken(token string) (user models.Users, err error)
}
type UserService struct {
	userRepo *impl.User
}

func (u *UserService) GetUserByStoreName(storeName string) (user *models.Users, err error) {
	return u.userRepo.FirstByStoreName(storeName)
}

func (u *UserService) GetUserByToken(token string) (user models.Users, err error) {
	return u.userRepo.FirstByToken(token)
}

func NewUserService() *UserService {
	return &UserService{
		userRepo: &impl.User{},
	}
}
