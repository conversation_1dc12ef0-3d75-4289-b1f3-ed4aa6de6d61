package domain

import "tmshopify/store/models"

const (
	MARCH2022     models.Version = "202203"
	SEPTEMBER2021 models.Version = "202109"
	JUNE2023      models.Version = "202306"
	JULY2024      models.Version = "202407"

	LATEST = JULY2024
)

var AllVersions = []models.Version{
	MARCH2022, SEPTEMBER2021, JUNE2023, JULY2024,
}

func IsValid(version models.Version) bool {
	for _, v := range AllVersions {
		if v == version {
			return true
		}
	}
	return false
}
