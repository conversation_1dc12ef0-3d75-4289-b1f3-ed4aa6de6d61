package config

import (
	"crypto/tls"
	"fmt"
	"net/http"

	"github.com/channelwill/athena/ges"
	"github.com/elastic/go-elasticsearch/v7"
)

type esConfig struct {
	Addresses          []string
	Username           string
	Password           string
	InsecureSkipVerify bool
}

func NewES(name string) (*elasticsearch.Client, error) {
	var esConf esConfig
	err := conf.ReadSection(name, &esConf)
	if err != nil {
		return nil, fmt.Errorf("failed to read config for %s section: %v", name, err)
	}

	// 创建自定义 Transport
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: esConf.InsecureSkipVerify, // 关键：跳过证书验证
		},
	}
	opts := []ges.Option{
		ges.WithUsername(esConf.Username),
		ges.WithPassword(esConf.Password),
		ges.WithTransport(transport),
	}
	return ges.NewClient(esConf.Addresses, opts...)
}
