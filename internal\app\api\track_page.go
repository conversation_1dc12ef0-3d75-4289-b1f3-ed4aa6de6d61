package api

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"strings"
	"tmshopify/internal/app/request"
	"tmshopify/internal/app/service"
	"tmshopify/pkg/domain"
	"tmshopify/pkg/utils/validator"
	"tmshopify/server/response"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type TrackPage struct {
}

func (t *TrackPage) Tracking(ctx *gin.Context) {
	result := validator.DefaultMessageValidate(ctx, &request.TrackRequest{})
	if len(result) > 0 {
		response.UnprocessableEntity(ctx, result)
		return
	}
	var params request.TrackRequest
	if err := ctx.ShouldBindQuery(&params); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	trackPageService := service.NewTrackPageService()
	user, userSetting, activeTheme, err := trackPageService.CheckAuth(params)
	if err != nil {
		response.BadRequest(ctx, "Store does not exist")
		return
	}
	trackRequestDomain := domain.HandleTrackRequest(params)
	lang := ctx.GetHeader("Accept-Language")
	if lang != "" {
		langArr := strings.Split(lang, ",")
		if len(langArr) > 0 {
			lang = strings.TrimSpace(langArr[0])

			if len(lang) > 2 && !strings.Contains(lang, "zh") {
				// 获取前两位
				lang = lang[:2]
			}
		}
	}
	trackPageService.DealRequestParams(user, trackRequestDomain, userSetting, activeTheme)
	var data map[string]interface{}
	switch activeTheme.ThemeCode {
	case models.ThemeModern:
		modernTrack := service.NewModernTrackPageService()
		modernTrack.TrackPageService = trackPageService
		data = modernTrack.BackData()
		ctx.JSON(http.StatusOK, data)
		return
	default:
		classicTrack := service.NewClassicTrackPageService()
		classicTrack.TrackPageService = trackPageService
		data = classicTrack.BackData()
	}
	ctx.JSONP(http.StatusOK, data)
}

func (t *TrackPage) RecommendProduct(ctx *gin.Context) {
	result := validator.DefaultMessageValidate(ctx, &request.RecommendProduct{})
	if len(result) > 0 {
		response.UnprocessableEntity(ctx, result)
		return
	}
	trackService := service.NewTrackPageService()
	shop, _ := ctx.GetQuery("shop")
	product, _ := ctx.GetQuery("product")
	User, err := trackService.CheckUser(shop)
	if err != nil || User.ID == 0 {
		response.NotFound(ctx, "Please register tracking page!")
		return
	}
	noCache := ctx.DefaultQuery("nocache", "0")
	nocache := noCache != "0" && len(noCache) > 0
	products := trackService.GetRecommendProduct(*User, product, nocache)
	response.Success(ctx, &response.SuccessResponse{
		Data: products,
	})
}

func (t *TrackPage) CollectionProduct(ctx *gin.Context) {
	result := validator.DefaultMessageValidate(ctx, &request.CollectionProduct{})
	if len(result) > 0 {
		response.UnprocessableEntity(ctx, result)
		return
	}
	trackService := service.NewTrackPageService()
	shop, _ := ctx.GetQuery("shop")
	collection, _ := ctx.GetQuery("collection")
	product, _ := ctx.GetQuery("product")
	User, err := trackService.CheckUser(shop)
	if err != nil || User.ID == 0 {
		response.NotFound(ctx, "Please register tracking page!")
		return
	}
	noCache := ctx.DefaultQuery("nocache", "0")
	nocache := noCache != "0" && len(noCache) > 0
	products := trackService.GetCollectionProduct(*User, collection, nocache, product)
	response.Success(ctx, &response.SuccessResponse{
		Data: products,
	})
}

type ReviewPost struct {
	Data []impl.CourierTrack `json:"data" binding:"required"`
	Lang string              `json:"lang" binding:"required"`
}

func (t *TrackPage) ReviewList(ctx *gin.Context) {
	var reviewPost ReviewPost
	if err := ctx.ShouldBindJSON(&reviewPost); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	trackService := service.NewTrackPageService()
	User, err := trackService.CheckUser(ctx.Param("shop"))
	if err != nil || User.ID == 0 {
		response.NotFound(ctx, "Please register tracking page!")
		return
	}
	res := trackService.ReviewList(User, reviewPost.Data, reviewPost.Lang)
	response.Success(ctx, &response.SuccessResponse{
		Data: res,
	})
}

func (t *TrackPage) DoReview(ctx *gin.Context) {
	var reviewPost impl.ReviewPostData
	if err := ctx.ShouldBindJSON(&reviewPost); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	trackService := service.NewTrackPageService()
	User, err := trackService.CheckUser(ctx.Param("shop"))
	if err != nil || User.ID == 0 {
		response.NotFound(ctx, "Please register tracking page!")
		return
	}
	// 验证请求参数是否为空，如果是，则返回错误
	if reviewPost.Courier == "" || reviewPost.CourierName == "" || reviewPost.TrackNumber == "" {
		response.BadRequest(ctx, "Please fill in all parameters!")
		return
	}

	// 执行保存评论的操作
	review := trackService.SaveReview(User.ID, reviewPost)
	if review.ID != 0 && len(reviewPost.Tags) > 0 {
		trackService.SaveTag(review, reviewPost.Tags)
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: review,
	})
}

func (t *TrackPage) OrderProduct(ctx *gin.Context) {
	result := validator.DefaultMessageValidate(ctx, &request.OrderProduct{})
	if len(result) > 0 {
		response.UnprocessableEntity(ctx, result)
		return
	}
	trackService := service.NewTrackPageService()
	shop, _ := ctx.GetQuery("shop")
	order, _ := ctx.GetQuery("order")
	order = strings.ReplaceAll(order, "%23", "#")
	User, err := trackService.CheckUser(shop)
	if err != nil || User.ID == 0 {
		response.NotFound(ctx, "Please register tracking page!")
		return
	}
	data, err := trackService.OrderProduct(*User, order)
	if err != nil {
		response.NotFound(ctx, err.Error())
		return
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: data,
	})
}

func (t *TrackPage) PluginTracking(ctx *gin.Context) {
	result := validator.DefaultMessageValidate(ctx, &request.TrackRequest{})
	if len(result) > 0 {
		response.UnprocessableEntity(ctx, result)
		return
	}
	var params request.TrackRequest
	if err := ctx.ShouldBindQuery(&params); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	lang := ctx.GetHeader("Accept-Language")
	if lang != "" {
		langArr := strings.Split(lang, ",")
		if len(langArr) > 0 {
			lang = strings.TrimSpace(langArr[0])

			if len(lang) > 2 && !strings.Contains(lang, "zh") {
				// 获取前两位
				lang = lang[:2]
			}
		}
	}
	trackPageService := service.NewTrackPageService()
	user, userSetting, activeTheme, err := trackPageService.CheckAuth(params)
	if err != nil {
		response.BadRequest(ctx, "Store does not exist")
		return
	}
	trackRequestDomain := domain.HandleTrackRequest(params)
	trackPageService.DealRequestParams(user, trackRequestDomain, userSetting, activeTheme)
	trackPageService.IsPlugin()
	var data map[string]interface{}
	classicTrack := service.NewClassicTrackPageService()
	classicTrack.TrackPageService = trackPageService
	data = classicTrack.BackData()
	response.Success(ctx, &response.SuccessResponse{
		Data: data,
	})
}

type ProductClickPost struct {
	Shop      string `json:"shop" binding:"required"`
	ProductId string `json:"product_id" binding:"required"`
}

func (t *TrackPage) ProductClick(ctx *gin.Context) {
	var productClickPost ProductClickPost
	if err := ctx.ShouldBindJSON(&productClickPost); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	trackService := service.NewTrackPageService()
	shop := productClickPost.Shop
	productId := productClickPost.ProductId
	User, err := trackService.CheckUser(shop)

	if err != nil {
		response.BadRequest(ctx, "Store does not exist")
		return
	}
	trackService.ProductClick(User.ID, productId)

	response.Success(ctx, &response.SuccessResponse{
		Data: true,
	})
}
