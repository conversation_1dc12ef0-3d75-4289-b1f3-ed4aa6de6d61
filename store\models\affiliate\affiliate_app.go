package affiliate

import "tmshopify/store/models"

type Apps struct {
	Name          string `json:"name"` // 内部名称
	OauthClientId uint64 `json:"oauth_client_id"`
	Password      string `json:"password"`
	LandingPage   string `json:"landing_page"`  // landing page link
	ContactEmail  string `json:"contact_email"` // 对外联系邮箱
	ContactName   string `json:"contact_name"`  // 对外联系名
	ContactPhone  string `json:"contact_phone"` // 对外联系电话，没有时不显示

	Client OauthClients `gorm:"foreignKey:OauthClientId"`
	models.Model
}

func (Apps) TableName() string {
	return "affiliate_apps"
}
