package models

type CourierTopRules struct {
	FromUser         int    `gorm:"column:from_user;type:INT(11);comment:来源用户id;not null;" form:"from_user" json:"from_user"`                               // 来源用户id
	RuleContent      string `gorm:"column:rule_content;type:VARCHAR(255);comment:规则内容;not null;" form:"rule_content" json:"rule_content"`                   // 规则内容
	ExampleTrack     string `gorm:"column:example_track;type:VARCHAR(255);comment:示例单号;not null;" form:"example_track" json:"example_track"`                // 示例单号
	Courier          string `gorm:"column:courier;type:VARCHAR(255);comment:识别物流商简码;not null;" form:"courier" json:"courier"`                               // 识别物流商简码
	ApplicableLength int    `gorm:"column:applicable_length;type:TINYINT(4);comment:适用单号长度;not null;" form:"applicable_length" json:"applicable_length"`    // 适用单号长度
	OnlyUseSet       int    `gorm:"column:only_use_set;type:TINYINT(4);comment:是否只适用设置了运输商的用户;not null;default:0;" form:"only_use_set" json:"only_use_set"` // 是否只适用设置了运输商的用户
	UpdatedBy        int    `gorm:"column:updated_by;type:INT(11);comment:更新人admin id;not null;" form:"updated_by" json:"updated_by"`                       // 更新人admin id
	CreatedBy        int    `gorm:"column:created_by;type:INT(11);comment:创建人admin id;not null;" form:"created_by" json:"created_by"`                       // 创建人admin id
	Model
}
