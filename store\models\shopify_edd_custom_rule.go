package models

type ShopifyEddCustomRules struct {
	Name          string `gorm:"column:name;type:varchar(255);comment:规则名;not null;" json:"name"`                      // 规则名
	UserId        int32  `gorm:"column:user_id;type:int;comment:用户id;not null;" json:"user_id"`                        // 用户id
	Countries     string `gorm:"column:countries;type:text;comment:存放国家地区json;" json:"countries"`                      // 存放国家地区json
	CollectionIds string `gorm:"column:collection_ids;type:text;comment:自定义规则添加的集合id字符串，用,号隔开;" json:"collection_ids"` // 自定义规则添加的集合id字符串，用,号隔开
	ProductIds    string `gorm:"column:product_ids;type:text;comment:自定义规则添加的产品id字符串，用,号隔开;" json:"product_ids"`       // 自定义规则添加的产品id字符串，用,号隔开
	Settings      string `gorm:"column:settings;type:text;comment:产品集合配置;" json:"settings"`                            // 产品集合配置
	MaxDays       uint8  `gorm:"column:max_days;type:tinyint UNSIGNED;not null;default:0;" json:"max_days"`
	MinDays       uint8  `gorm:"column:min_days;type:tinyint UNSIGNED;not null;default:0;" json:"min_days"`
	Carriers      string `gorm:"column:carriers;type:text;comment:运输商设置列表;" json:"carriers"` // 运输商设置列表

	Model
}
