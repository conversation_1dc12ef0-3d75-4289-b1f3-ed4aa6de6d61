package service

import (
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"tmshopify/config"
	"tmshopify/internal/app/entity"
	"tmshopify/internal/app/utils"
	"tmshopify/store/database"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type CheckoutService struct {
	TrackPageSettingRep *impl.TrackPageSetting
	OrderRecordRep      *impl.OrderRecord
	OrderTrackRep       *impl.OrderTrack
	UserRep             *impl.User
}

type CheckoutInterface interface {
	GetCheckOutLinkByName(name, version string, isCss bool) string
	GetCheckoutReturnData(replaceNumberHrefArr map[string]string, trackLinkText, trackLinkHref string) map[string]interface{}
	GetCheckoutSetting(shopName string) *models.Checkout
	GetUserTrackPageMessage(userId int) *models.TrackPageSettings
	CheckOrder(userId int, order string) models.OrderRecords
	GetTrackNumberList(userId int, orderId string) []string
	GetOrderFulfillTime(userId int, trackNumber string) (int, int)
	GetTrackInfoByNumber(userId int, trackNumber string) *models.TrackInfos
	GetTrackLinkByOrder(user models.Users, order models.OrderRecords, trackUrl string, isTrackNumber bool) string
	GetOrderStatusWidgetText(user models.Users, orderName string, checkoutSetting *models.Checkout) map[string]interface{}
}

// GetCheckOutLinkByName 获取加载到 checkout 页面的静态文件地址
func (c *CheckoutService) GetCheckOutLinkByName(name, version string, isCss bool) string {
	ext := ".js"
	if isCss {
		ext = ".css"
	}
	return fmt.Sprintf("https://%s/static/checkout/%s%s?time=%s", config.Get().ServerDomain, name, ext, version)
}

// GetCheckoutReturnData 处理返回的 track link
func (c *CheckoutService) GetCheckoutReturnData(replaceNumberHrefArr map[string]string, trackLinkText, trackLinkHref string) map[string]interface{} {
	data := make(map[string]interface{})
	if trackLinkText != "" {
		data["customTrackLink"] = map[string]string{"href": trackLinkHref, "name": trackLinkText}
	}
	if len(replaceNumberHrefArr) != 0 {
		var replaceNumberLink []map[string]string
		for key, value := range replaceNumberHrefArr {
			replaceNumberLink = append(replaceNumberLink, map[string]string{"href": value, "nums": key})
		}
		data["replaceNumberLink"] = replaceNumberLink
	}
	return data
}

// GetCheckoutSetting 获取用户的 checkout 设置
func (c *CheckoutService) GetCheckoutSetting(shopName string) *models.Checkout {
	var checkout *models.Checkout
	result := database.DB.Where("shop = ?", shopName).Find(&checkout)
	if result.RowsAffected == 0 {
		user, _ := c.UserRep.FirstActiveUserIdByShop(shopName)
		if user.ID != 0 {
			result = database.DB.Where("user_id = ?", user.ID).Find(&checkout)
			// 初始化 checkout 设置
			if result.RowsAffected == 0 {
				cardSetting, _ := json.Marshal(config.OrderStatusSetting["card"])
				widgetSetting, _ := json.Marshal(config.OrderStatusSetting["widget"])
				checkout = &models.Checkout{
					UserId:             user.ID,
					Shop:               shopName,
					TrackLinkText:      config.OrderStatusSetting["link"]["track_link_text"].(string),
					OrderStatusSetting: string(cardSetting),
					TrackLinkColor:     config.OrderStatusSetting["link"]["track_link_color"].(string),
					AddOnType:          2,
					ReplaceWith:        1,
					WidgetSetting:      string(widgetSetting),
				}
				database.DB.Model(models.Checkout{}).Create(checkout)
			}
		}
	}
	return checkout
}

func (c *CheckoutService) GetUserTrackPageMessage(userId int) *models.TrackPageSettings {
	return c.TrackPageSettingRep.GetUserTrackPageMessage(userId)
}

func (c *CheckoutService) CheckOrder(userId int, order string) models.OrderRecords {
	orderRecord, _ := c.OrderRecordRep.FirstByOrderNameWithOrderTrack(userId, order)
	return orderRecord
}

func (c *CheckoutService) GetTrackNumberList(userId int, orderId string) []string {
	var trackNumbers []struct {
		TrackNumber string `json:"track_number"`
	}
	var trackNumberList []string
	err := database.DB.Scopes(models.OrderTracks{}.TableOfUser(models.OrderTracks{}.TableName(), userId)).Where("user_id = ? AND order_id = ? AND is_delete = ?", userId, orderId, 0).Select("track_number").Scan(&trackNumbers).Error
	if err != nil {
		logrus.Error(err)
	}
	for _, value := range trackNumbers {
		trackNumberList = append(trackNumberList, value.TrackNumber)
	}

	return trackNumberList
}

func (c *CheckoutService) GetOrderFulfillTime(userId int, trackNumber string) (int, int) {
	orderFulfillTime, trackStatus, _ := c.OrderTrackRep.GetOrderFulfillTime(userId, trackNumber)
	return orderFulfillTime, trackStatus
}

func (c *CheckoutService) GetTrackInfoByNumber(userId int, trackNumber string) *models.TrackInfos {
	var trackInfo *models.TrackInfos
	database.DB.Scopes(models.TrackInfos{}.TableOfUser(models.TrackInfos{}.TableName(), userId)).Where("user_id = ? AND track_number = ?", userId, trackNumber).Order("created_at DESC").First(&trackInfo)
	return trackInfo
}

// GetTrackLinkByOrder 根据订单和查询后缀获取查询链接
func (c *CheckoutService) GetTrackLinkByOrder(user models.Users, order models.OrderRecords, trackUrl string, isTrackNumber bool) string {
	// 当前用户的真实域名
	shopRealName := user.StoreRealName
	if shopRealName == "" {
		shopRealName = user.StoreName
	}
	if order.ID == 0 {
		return fmt.Sprintf("https://%s/%s", shopRealName, trackUrl)
	}
	orderName := order.OrderName
	customerEmail := order.CustomerEmail
	orderTracks := order.OrderTracks
	orderName = url.QueryEscape(orderName)
	// 链接参数列表
	params := []string{"order=" + orderName, "email=" + customerEmail}
	trackNumber := ""
	if len(orderTracks) != 0 {
		trackNumber = strings.TrimSpace(orderTracks[0].TrackNumber)
	}

	//如果用户没有邮箱(或者需要由 track number 组成的链接), 但是有单号, 那么将单号作为查询链接
	if (customerEmail == "" || isTrackNumber) && trackNumber != "" {
		params = []string{"nums=" + trackNumber}
	}
	// 单号的查询链接
	trackLinkHref := fmt.Sprintf("https://%s/%s/?%s", shopRealName, trackUrl, strings.Join(params, "&"))
	return trackLinkHref
}

func (c *CheckoutService) GetOrderStatusWidgetText(user models.Users, orderName string, checkoutSetting *models.Checkout) map[string]interface{} {
	userId := user.ID
	// 获取订单信息
	order := c.CheckOrder(userId, orderName)
	// 获取单号列表
	numberList := c.GetTrackNumberList(userId, order.OrderId)
	trackNumber := ""
	if len(numberList) != 0 {
		trackNumber = strings.TrimSpace(numberList[0])
	}
	// 获取 track link 设置
	checkPageSet := c.GetUserTrackPageMessage(userId)
	trackUrl := "apps/trackingmore"
	if checkPageSet.ID != 0 {
		trackUrl = checkPageSet.TrackLink
	}

	// 初始化返回数据
	addOnSetting := make(map[string]interface{})
	// 判断是否有自定义订单状态, 有的话优先使用
	orderHas := 0
	if order.CustomStatusTime != "" {
		var customStatusTime map[int]int
		_ = json.Unmarshal([]byte(order.CustomStatusTime), &customStatusTime)
		for k, v := range []int{1001, 1100, 2, 3, 4} {
			if customStatusTime[v] != 0 {
				orderHas = k
			}
		}
	}
	// 检查订单状态
	if orderHas == 0 {
		statusKeyArr := utils.GetStatusKey([]config.StatusNodeNumber{})
		statusSetStart := statusKeyArr[0]
		statusSetEnd := statusKeyArr[len(statusKeyArr)-1]
		// 检查此单号是否已经发货
		orderFulfillTime := 0
		orderStatus := 0
		if trackNumber != "" {
			orderFulfillTime, orderStatus = c.GetOrderFulfillTime(userId, trackNumber)
		}
		if orderFulfillTime == 0 {
			orderHas = int(statusSetStart)
		} else {
			orderHas = int(statusSetEnd)
		}
		customTrackStatus := order.CustomTrackStatus
		if customTrackStatus != 0 && customTrackStatus != config.OrderedNodeStatusNumber && customTrackStatus != config.OrderReadyStatusNumber {
			orderHas = int(customTrackStatus)
		}
		if orderStatus != 0 {
			orderHas = orderStatus
		}
	}
	var userAddOnSetting impl.OrderStatusSetting
	if err := json.Unmarshal([]byte(checkoutSetting.WidgetSetting), &userAddOnSetting); err != nil {
		logrus.Error("orderStatusSetting json parsing error ", err)
	}
	// 静态文本设置 start
	estimatedDeliveryTimeText := ""
	trackingNumberText := ""
	if userAddOnSetting.ExtraContents.EstimatedDeliveryTime != 0 {
		estimatedDeliveryTimeText = userAddOnSetting.Others.EstimatedDeliveryTime
	}
	if userAddOnSetting.ExtraContents.TrackingNumber != 0 {
		trackingNumberText = userAddOnSetting.Others.TrackingNumber
	}
	// 获取单号和对应的 track link 列表
	var trackNumberList []struct {
		TrackNumber string `json:"trackNumber"`
		Link        string `json:"link"`
	}
	trackLink := c.GetTrackLinkByOrder(user, order, trackUrl, false)
	for _, item := range numberList {
		link := c.GetTrackLinkByOrder(user, order, trackUrl, true)
		trackNumberList = append(trackNumberList, struct {
			TrackNumber string `json:"trackNumber"`
			Link        string `json:"link"`
		}{TrackNumber: item, Link: link})
	}
	addOnSetting = map[string]interface{}{
		"estimatedDeliveryTime": estimatedDeliveryTimeText,
		"trackingNumberText":    trackingNumberText,
		"trackingNumber":        trackNumberList,
		"buttonText":            userAddOnSetting.Others.TrackYourOrder,
		"trackLink":             trackLink,
	}
	if orderHas == 1001 {
		addOnSetting["status"] = userAddOnSetting.Status.Ordered
		addOnSetting["description"] = userAddOnSetting.Description.Ordered
	}
	if orderHas == 1100 || orderHas == 1 {
		addOnSetting["status"] = userAddOnSetting.Status.Fulfilled
		addOnSetting["description"] = userAddOnSetting.Description.Fulfilled
	}
	if orderHas >= 2 && orderHas <= 4 {
		addOnSetting["status"] = userAddOnSetting.Status.HasShippingUpdate
		addOnSetting["description"] = userAddOnSetting.Description.HasShippingUpdate
	}
	// 静态文本设置 end

	// 显示的 ETA start
	var shippingTimeOn string
	if trackNumber != "" {
		trackInfo := c.GetTrackInfoByNumber(userId, trackNumber)
		if trackInfo.ID != 0 && !trackInfo.ExpectedDeliveryTime.IsZero() {
			shippingTimeOn = trackInfo.ExpectedDeliveryTime.Format("01/02, 2006")
		}
	}
	if shippingTimeOn == "" {
		var orderTrack models.OrderTracks
		for _, track := range order.OrderTracks {
			if track.TrackNumber == trackNumber {
				orderTrack = track
				break
			}
		}
		eddSetting := entity.HandleEddSetting(userId)
		eddService := NewEddService()
		calculateEntity := eddService.GetCalculateEntity(eddSetting, TrackCalculateData{
			UserId:           orderTrack.UserId,
			FulfillmentId:    orderTrack.FulfillmentId,
			TrackNumber:      orderTrack.TrackNumber,
			Destination:      orderTrack.Destination,
			Courier:          orderTrack.Courier,
			OrderCreateTime:  orderTrack.OrderCreateTime,
			OrderFulfillTime: orderTrack.OrderFulfillTime,
		})
		dataShow := eddService.CalculateEddByOrderAndDestinationByEntities(userId,
			calculateEntity, eddSetting)
		if dataShow.StartShow != 0 && dataShow.EndShow != 0 {
			shippingTimeOn = fmt.Sprintf("%s - %s", time.Unix(dataShow.StartShow, 0).Format("01/02"), time.Unix(dataShow.EndShow, 0).Format("01/02, 2006"))
			if userId == 26237 {
				shippingTimeOn = fmt.Sprintf("%s - %s", time.Unix(dataShow.StartShow, 0).Format("02/01/2006"), time.Unix(dataShow.EndShow, 0).Format("02/01/2006"))
			}
		}
	}
	addOnSetting["shippingTimeCon"] = shippingTimeOn
	return addOnSetting
}

func NewCheckoutService() *CheckoutService {
	return &CheckoutService{
		TrackPageSettingRep: &impl.TrackPageSetting{},
		OrderRecordRep:      &impl.OrderRecord{},
		OrderTrackRep:       &impl.OrderTrack{},
	}
}
