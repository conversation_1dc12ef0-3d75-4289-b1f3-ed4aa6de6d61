package auth

import (
	"context"
	"fmt"

	authEntity "tmshopify/internal/domain/entity/auth"
	authRepo "tmshopify/internal/domain/repo/auth"
	"tmshopify/internal/providers"
)

// Service 认证服务
type Service struct {
	userInfoRepo  authRepo.UserInfoRepository
	userInfoCache authRepo.UserInfoCache
}

type ServiceOption struct {
	ClientID     string
	ClientSecret string
}

// NewService 创建认证服务
func NewService(repos *providers.Repositories) *Service {
	return &Service{
		userInfoRepo: repos.UserInfoRepo,
	}
}

// GetSessionByID 根据ID获取会话信息
func (u *Service) GetSessionByID(ctx context.Context, id int64) (session *authEntity.Session, err error) {
	return u.userInfoRepo.GetSessionByID(ctx, id)
}

// StoreSession 存储会话信息
func (u *Service) StoreSession(ctx context.Context, session *authEntity.Session) (err error) {
	return u.userInfoRepo.StoreSession(ctx, session)
}

// ReGrantUser 重新授权用户
func (u *Service) ReGrantUser(ctx context.Context, option ServiceOption, shop string, token string) (userGrant *authEntity.UserAuthInfoEntity, err error) {
	// 重新授权，更新或创建
	if userGrant, err = u.userInfoRepo.ReGrantUser(ctx, option.ClientID, option.ClientSecret, shop, token); err != nil {
		return nil, err
	}

	// 删除缓存
	err = u.userInfoCache.Delete(ctx, userGrant.UserInfo.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to delete user auth info: %w", err)
	}

	return userGrant, nil
}
