package graphql

import (
	"github.com/shopspring/decimal"
	"time"
)

const ProductByIdQuery string = "query productList($query: String) { products(first: 5, query:$query) { nodes { id title handle createdAt featuredMedia { preview { image { url } } } priceRangeV2 { minVariantPrice { amount } } } } }"

const ProductByCollectionQuery string = "query productList($id: ID!) { collection(id: $id) { products(first: 5, sortKey:TITLE) { nodes { id title handle createdAt featuredMedia { preview { image { url } } } priceRangeV2 { minVariantPrice { amount } } } } } }"

const ProductList string = "query { products(first: 5, sortKey:TITLE) { nodes { id title handle createdAt featuredMedia { preview { image { url } } } priceRangeV2 { minVariantPrice { amount } } } } }"

const ProductsByIdsQuery string = "query productList($ids: [ID!]!) { nodes(ids: $ids) { ... on Product { id title handle createdAt featuredMedia { preview { image { url } } } priceRangeV2 { minVariantPrice { amount } } } } }"

type Preview struct {
	Image `json:"image,omitempty"`
}

type Image struct {
	URL string `json:"url,omitempty"`
}

type FeaturedMedia struct {
	Preview `json:"preview,omitempty"`
}

type Product struct {
	ID            string        `json:"id"`
	Title         string        `json:"title"`
	Handle        string        `json:"handle"`
	CreatedAt     time.Time     `json:"createdAt"`
	FeaturedMedia FeaturedMedia `json:"featuredMedia"`
	PriceRange    struct {
		MinVariantPrice struct {
			Amount *decimal.Decimal `json:"amount"`
		} `json:"minVariantPrice"`
	} `json:"priceRangeV2"`
}

type Variant struct {
	ID string `json:"id"`
}

type ProductResult struct {
	Products struct {
		Nodes []Product `json:"nodes"`
	} `json:"products"`
}

type CollectionResult struct {
	Collection struct {
		Products struct {
			Nodes []Product `json:"nodes"`
		} `json:"products"`
	} `json:"collection"`
}

// ProductInfo 结构体定义，用于存储 extractProductFromFulfillmentItems 的返回值
type ProductInfo struct {
	ProductID     string `json:"product_id"`
	VariantID     string `json:"variant_id"`
	FulfillmentID string `json:"fulfillment_id"`
	LineItemID    string `json:"line_item_id"`
	Name          string `json:"name"`
	SKU           string `json:"sku"`
	Title         string `json:"title"`
	OrderID       string `json:"order_id"`
	Price         string `json:"price"`
	TotalDiscount string `json:"total_discount"`
	Image         string `json:"image"`
	Handle        string `json:"handle"`
	Quantity      int    `json:"quantity"`
}
