package request

var EmailLoginRequest = map[string]interface{}{
	"email":    "required,email",
	"password": "required,password",
	"code":     "required",
}
var LoginPasswdRequest = map[string]interface{}{
	"email":    "required,email",
	"password": "required,password",
}

var SendEmailRequest = map[string]interface{}{
	"email": "required,email",
}

var ChangePasswordRequest = map[string]interface{}{
	"confirm_password": "required,min=16,password",
	"password":         "required,min=16,password",
}

var CustomizePlanCreate = map[string]string{
	"user_id":      "required,number",
	"name":         "required,min=1",
	"plan":         "required,min=1",
	"price":        "required,number",
	"count":        "required,number",
	"version":      "required,len=6",
	"level":        "required,numeric,min=0",
	"support_type": "numeric,min=0",
	"plan_type":    "required,number",
}
var CustomizePlanUpdate = map[string]string{
	"user_id":      "required,number",
	"name":         "required,min=1",
	"price":        "required,number",
	"count":        "required,number",
	"level":        "required,numeric,min=0",
	"support_type": "numeric,min=0",
	"plan_type":    "required,number",
}
var ChangeSubscriptionExtraPrice = map[string]string{
	"user_id":     "required,number",
	"extra_price": "required,numeric",
}

var ChargePlanCreate = map[string]string{
	"user_id":                "required,number",
	"name":                   "required,min=1",
	"price":                  "required,number",
	"quota":                  "required,number",
	"version":                "required,len=6",
	"level":                  "required,numeric,min=0",
	"billing_cycle":          "required",
	"plan_type":              "required",
	"replace_system_plan_id": "required,number",
	"created_by":             "required,number",
}

var ChargePlanUpdate = map[string]string{
	"user_id":                "required,number",
	"name":                   "required,min=1",
	"price":                  "required,number",
	"quota":                  "required,number",
	"level":                  "required,numeric,min=0",
	"billing_cycle":          "required",
	"plan_type":              "required",
	"replace_system_plan_id": "required,number",
	"created_by":             "required,number",
}
