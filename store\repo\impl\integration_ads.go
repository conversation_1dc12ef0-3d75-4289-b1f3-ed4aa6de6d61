package impl

import (
	"github.com/sirupsen/logrus"
	"tmshopify/pkg/utils/paginate"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type IntegrationAds struct {
	Helper
}

func (i *IntegrationAds) GetIntegrationAdsList(param paginate.Param) ([]models.IntegrationAds, error) {
	var result []models.IntegrationAds
	err := database.DB.Scopes(paginate.ORMScope(&param)).Find(&result).Error
	return result, err
}

func (i *IntegrationAds) Create(m *models.IntegrationAds) error {
	logrus.Warning("test step 2")
	return database.DB.Create(m).Error
}

func (i *IntegrationAds) Update(m *models.IntegrationAds) error {
	return database.DB.Omit("created_at").Save(m).Error
}
