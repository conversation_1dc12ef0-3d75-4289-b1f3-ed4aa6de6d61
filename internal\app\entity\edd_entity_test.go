package entity

import (
	"encoding/json"
	"testing"
)

func TestEddRange_UnmarshalJSON(t *testing.T) {
	var eddZoneSetting EddZoneSetting
	str := "{\"switch\":false,\"cal_type\":0,\"edd_range\":[{\"zone_name\":\"All countries\",\"region\":[\"Africa\",\"Asia\",\"Europe\",\"North America\",\"Oceania\",\"South America\"],\"minRange\":\"10\",\"maxRange\":\"20\"}]}"
	err := json.Unmarshal([]byte(str), &eddZoneSetting)
	if err != nil {
		t.Fatal("UnmarshalJSON error: ", err.<PERSON>rror())
	}
}
