package models

type TrackPageProductClicks struct {
	UserId     uint   `gorm:"column:user_id;type:INT(11);comment:用户id;not null;" json:"user_id"`
	ClickNum   uint   `gorm:"column:click_num;type:INT(11);comment:当天点击次数;not null;default:0;" json:"click_num"`
	ProductId  string `gorm:"column:product_id;type:VARCHAR(255);comment:点击商品的id;not null;default:0;" json:"product_id"`
	CreateDate uint   `gorm:"column:create_date;type:INT(11);comment:当天的日期时间戳;not null;default:0;" json:"create_date"`
	Model
}

func (TrackPageProductClicks) TableName() string {
	return "track_page_product_clicks"
}
