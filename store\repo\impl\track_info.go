package impl

import (
	"errors"
	"gorm.io/gorm"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type TrackInfo struct {
	Helper
}

func (t *TrackInfo) Get(shop string, tracking string) (models.Users, error) {
	user := models.Users{}
	var userId int
	database.DB.Model(user).Where("store_name = ?", shop).Select("id").Scan(&userId)
	if userId == 0 {
		return user, errors.New("user not exist")
	}
	var orderTrack models.OrderTracks
	var trackInfo models.TrackInfos
	result := database.DB.Preload("OrderTracks", func(db *gorm.DB) *gorm.DB {
		return database.DB.Scopes(orderTrack.TableOfUser(orderTrack.TableName(), userId)).Where("track_number = ?", tracking).Order("created_at desc").First(&models.OrderTracks{})
	}).Preload("TrackInfos", func(db *gorm.DB) *gorm.DB {
		return database.DB.Scopes(trackInfo.TableOfUser(trackInfo.TableName(), userId)).Where("track_number = ?", tracking)
	}).Where("id = ?", userId).First(&user)
	return user, result.Error
}
