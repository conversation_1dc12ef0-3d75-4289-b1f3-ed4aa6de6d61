package config

import (
	"fmt"
	"time"

	"github.com/channelwill/athena/gredis"
	"github.com/gomodule/redigo/redis"
	goredis "github.com/redis/go-redis/v9"

	"tmshopify/store/database"
)

type RedisConfig struct {
	gredis.RedisConf
	Address     string
	Password    string
	MaxIdle     int
	Active      int
	IdleTimeout time.Duration
}

// NewRedis 创建redis实例
func NewRedis(name string) (*database.Redis, error) {
	redisConf := RedisConfig{}
	err := conf.ReadSection(name, &redisConf)
	// log.Println("redis conf:", redisConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to read config for %s section: %v", name, err)
	}

	Pool := &redis.Pool{
		Dial: func() (conn redis.Conn, e error) {
			c, err := redis.Dial("tcp", redisConf.Address)
			if err != nil {
				return nil, err
			}

			if redisConf.Password != "" {
				if _, err := c.Do("AUTH", redisConf.Password); err != nil {
					c.Close()
					return nil, err
				}
			}
			return c, err
		},
		TestOnBorrow: func(c redis.Conn, t time.Time) error {
			_, err := c.Do("PING")
			return err
		},
		MaxIdle:         redisConf.MaxIdle,
		MaxActive:       redisConf.Active,
		IdleTimeout:     redisConf.IdleTimeout,
		Wait:            false,
		MaxConnLifetime: 0,
	}

	redisClient := &database.Redis{
		Pool: Pool,
	}

	return redisClient, nil
}

// NewRedisClient 创建redis实例
func NewRedisClient(name string) (goredis.UniversalClient, error) {
	redisConf := gredis.RedisConf{}
	err := conf.ReadSection(name, &redisConf)
	// log.Println("redis conf:", redisConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to read config for %s section: %s", name, err)
	}

	return redisConf.InitClient(), nil
}
