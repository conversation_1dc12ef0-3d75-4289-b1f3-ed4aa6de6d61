package models

type ShopifyFlowTriggers struct {
	ShopDomain     string `gorm:"column:shop_domain;type:varchar(100);comment:shopify shop name;not null;" json:"shop_domain"`                                                     // shopify shop name
	TriggerId      string `gorm:"column:trigger_id;type:varchar(50);comment:shopify trigger unique id;not null;" json:"trigger_id"`                                                // shopify trigger unique id
	HasEnabledFlow int32  `gorm:"column:has_enabled_flow;type:tinyint UNSIGNED;comment:user trigger flow status 0: disable 1: enable;not null;default:0;" json:"has_enabled_flow"` // user trigger flow status 0: disable 1: enable
	Version        string `gorm:"column:version;type:varchar(8);comment:flow version in tms;not null;default:20240112;" json:"version"`                                            // flow version in tms
	Model
}
