package shopify

import (
	"fmt"
	"github.com/shopspring/decimal"
	"time"
)

type Order struct {
	ID                            int64            `json:"id"`
	Name                          string           `json:"name"`
	Email                         string           `json:"email"`
	CreatedAt                     *time.Time       `json:"created_at"`
	Company                       *Company         `json:"company"`
	ConfirmationNumber            string           `json:"confirmation_number"`
	UpdatedAt                     *time.Time       `json:"updated_at"`
	CancelledAt                   *time.Time       `json:"cancelled_at"`
	ClosedAt                      *time.Time       `json:"closed_at"`
	ProcessedAt                   *time.Time       `json:"processed_at"`
	Customer                      *Customer        `json:"customer"`
	BillingAddress                *Address         `json:"billing_address"`
	ShippingAddress               *Address         `json:"shipping_address"`
	Currency                      string           `json:"currency"`
	TotalOutstanding              *decimal.Decimal `json:"total_outstanding,omitempty"`
	TotalPrice                    *decimal.Decimal `json:"total_price"`
	CurrentTotalAdditionalFeesSet *AmountSet       `json:"current_total_additional_fees_set,omitempty"`
	CurrentTotalDiscountsSet      *AmountSet       `json:"current_total_discounts_set,omitempty"`
	CurrentTotalDutiesSet         *AmountSet       `json:"current_total_duties_set,omitempty"`
	CurrentTotalPriceSet          *AmountSet       `json:"current_total_price_set,omitempty"`
	CurrentSubtotalPriceSet       *AmountSet       `json:"current_subtotal_price_set,omitempty"`
	CurrentTotalTaxSet            *AmountSet       `json:"current_total_tax_set,omitempty"`
	DiscountApplications          []struct {
		Type             string `json:"type"`
		Title            string `json:"title,omitempty"`
		Description      string `json:"description,omitempty"`
		Value            string `json:"value"`
		ValueType        string `json:"value_type"`
		AllocationMethod string `json:"allocation_method"`
		TargetSelection  string `json:"target_selection"`
		TargetType       string `json:"target_type"`
		Code             string `json:"code,omitempty"`
	} `json:"discount_applications"`
	SubtotalPriceSet      *AmountSet       `json:"subtotal_price_set,omitempty"`
	TotalPriceSet         *AmountSet       `json:"total_price_set"`
	TotalShippingPriceSet *AmountSet       `json:"total_shipping_price_set,omitempty"`
	CurrentTotalPrice     *decimal.Decimal `json:"current_total_price"`
	SubtotalPrice         *decimal.Decimal `json:"subtotal_price"`
	CurrentSubtotalPrice  *decimal.Decimal `json:"current_subtotal_price"`
	TotalDiscounts        *decimal.Decimal `json:"total_discounts"`

	CurrentTotalDiscounts *decimal.Decimal `json:"current_total_discounts"`

	OriginalTotalAdditionalFeesSet *AmountSet `json:"original_total_additional_fees_set,omitempty"`
	OriginalTotalDutiesSet         *AmountSet `json:"original_total_duties_set,omitempty"`

	TotalLineItemsPrice    *decimal.Decimal     `json:"total_line_items_price"`
	TaxesIncluded          bool                 `json:"taxes_included"`
	TotalTax               *decimal.Decimal     `json:"total_tax"`
	TotalTaxSet            *AmountSet           `json:"total_tax_set,omitempty"`
	TotalLineItemsPriceSet *AmountSet           `json:"total_line_items_price_set,omitempty"`
	CurrentTotalTax        *decimal.Decimal     `json:"current_total_tax"`
	TotalTipReceived       *decimal.Decimal     `json:"total_tip_received"`
	TaxLines               []TaxLine            `json:"tax_lines"`
	TotalWeight            int                  `json:"total_weight"`
	FinancialStatus        orderFinancialStatus `json:"financial_status"`
	EstimatedTaxes         bool                 `json:"estimated_taxes"`
	PaymentTerms           struct {
		Amount           int    `json:"amount"`
		Currency         string `json:"currency"`
		PaymentTermsName string `json:"payment_terms_name"`
		PaymentTermsType string `json:"payment_terms_type"`
		DueInDays        int    `json:"due_in_days"`
		PaymentSchedules []struct {
			Amount                interface{} `json:"amount"`
			Currency              string      `json:"currency"`
			IssuedAt              time.Time   `json:"issued_at"`
			DueAt                 time.Time   `json:"due_at"`
			CompletedAt           string      `json:"completed_at"`
			ExpectedPaymentMethod string      `json:"expected_payment_method"`
		} `json:"payment_schedules"`
	} `json:"payment_terms,omitempty"`

	Fulfillments           []Fulfillment          `json:"fulfillments"`
	FulfillmentStatus      orderFulfillmentStatus `json:"fulfillment_status"`
	Token                  string                 `json:"token"`
	CartToken              string                 `json:"cart_token"`
	Number                 int                    `json:"number"`
	OrderNumber            int                    `json:"order_number"`
	Note                   string                 `json:"note"`
	Test                   bool                   `json:"test"`
	BrowserIp              string                 `json:"browser_ip"`
	BuyerAcceptsMarketing  bool                   `json:"buyer_accepts_marketing"`
	CancelReason           orderCancelReason      `json:"cancel_reason"`
	NoteAttributes         []NoteAttribute        `json:"note_attributes"`
	DiscountCodes          []DiscountCode         `json:"discount_codes"`
	LineItems              []LineItem             `json:"line_items"`
	ShippingLines          []ShippingLines        `json:"shipping_lines"`
	Transactions           []Transaction          `json:"transactions,omitempty"`
	AppID                  int                    `json:"app_id"`
	CustomerLocale         string                 `json:"customer_locale"`
	LandingSite            string                 `json:"landing_site"`
	ReferringSite          string                 `json:"referring_site"`
	SourceName             string                 `json:"source_name"`
	ClientDetails          *ClientDetails         `json:"client_details"`
	Tags                   string                 `json:"tags"`
	LocationId             int64                  `json:"location_id"`
	MerchantOfRecordAppId  int64                  `json:"merchant_of_record_app_id"`
	PaymentGatewayNames    []string               `json:"payment_gateway_names"`
	Refunds                []Refund               `json:"refunds"`
	UserId                 int64                  `json:"user_id"`
	OrderStatusUrl         string                 `json:"order_status_url"`
	Confirmed              bool                   `json:"confirmed"`
	CheckoutToken          string                 `json:"checkout_token"`
	Reference              string                 `json:"reference"`
	SourceIdentifier       string                 `json:"source_identifier"`
	SourceURL              string                 `json:"source_url"`
	DeviceID               int64                  `json:"device_id"`
	Phone                  string                 `json:"phone"`
	PoNumber               string                 `json:"po_number"`
	PresentmentCurrency    string                 `json:"presentment_currency"`
	LandingSiteRef         string                 `json:"landing_site_ref"`
	CheckoutID             int64                  `json:"checkout_id"`
	ContactEmail           string                 `json:"contact_email"`
	SendReceipt            bool                   `json:"send_receipt"`
	SendFulfillmentReceipt bool                   `json:"send_fulfillment_receipt"`
}

type Company struct {
	Id         int `json:"id"`
	LocationId int `json:"location_id"`
}
type Customer struct {
	Id                  int       `json:"id"`
	Email               string    `json:"email"`
	AcceptsMarketing    bool      `json:"accepts_marketing"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
	FirstName           string    `json:"first_name"`
	LastName            string    `json:"last_name"`
	State               string    `json:"state"`
	Note                *string   `json:"note"`
	VerifiedEmail       bool      `json:"verified_email"`
	MultipassIdentifier *string   `json:"multipass_identifier"`
	TaxExempt           bool      `json:"tax_exempt"`
	TaxExemptions       []string  `json:"tax_exemptions"`
	Phone               string    `json:"phone"`
	Tags                string    `json:"tags"`
	Currency            string    `json:"currency"`
	Addresses           *Address  `json:"addresses"`
	AdminGraphqlApiId   string    `json:"admin_graphql_api_id"`
	DefaultAddress      *Address  `json:"default_address"`
}
type Address struct {
	ID           int64   `json:"id,omitempty"`
	Address1     string  `json:"address1,omitempty"`
	Address2     string  `json:"address2,omitempty"`
	City         string  `json:"city,omitempty"`
	Company      string  `json:"company,omitempty"`
	Country      string  `json:"country,omitempty"`
	CountryCode  string  `json:"country_code,omitempty"`
	FirstName    string  `json:"first_name,omitempty"`
	LastName     string  `json:"last_name,omitempty"`
	Latitude     float64 `json:"latitude,omitempty"`
	Longitude    float64 `json:"longitude,omitempty"`
	Name         string  `json:"name,omitempty"`
	Phone        string  `json:"phone,omitempty"`
	Province     string  `json:"province,omitempty"`
	ProvinceCode string  `json:"province_code,omitempty"`
	Zip          string  `json:"zip,omitempty"`
}

type DiscountCode struct {
	Amount *decimal.Decimal `json:"amount"`
	Code   string           `json:"code"`
	Type   string           `json:"type"`
}

type LineItem struct {
	ID               int64 `json:"id"`
	AttributedStaffs []struct {
		Id       string `json:"id"`
		Quantity int    `json:"quantity"`
	} `json:"attributed_staffs"`

	FulfillableQuantity int              `json:"fulfillable_quantity"`
	FulfillmentService  string           `json:"fulfillment_service"`
	FulfillmentStatus   string           `json:"fulfillment_status"`
	Grams               int              `json:"grams"`
	Price               *decimal.Decimal `json:"price"`
	ProductId           int              `json:"product_id"`
	Quantity            int              `json:"quantity"`
	RequiresShipping    bool             `json:"requires_shipping"`
	Sku                 string           `json:"sku"`
	Title               string           `json:"title"`
	VariantId           int              `json:"variant_id"`
	VariantTitle        string           `json:"variant_title"`
	Vendor              string           `json:"vendor"`
	Name                string           `json:"name"`
	GiftCard            bool             `json:"gift_card"`
	PriceSet            AmountSet        `json:"price_set"`
	Properties          []struct {
		Name  string      `json:"name"`
		Value interface{} `json:"value"`
	} `json:"properties"`

	Taxable             bool                  `json:"taxable"`
	TaxLines            []TaxLine             `json:"tax_lines"`
	TotalDiscounts      *decimal.Decimal      `json:"total_discounts"`
	TotalDiscountSet    *AmountSet            `json:"total_discount_set"`
	DiscountAllocations []DiscountAllocations `json:"discount_allocations"`
	OriginLocation      Address               `json:"origin_location"`
	Duties              []struct {
		Id                   int64  `json:"id"`
		HarmonizedSystemCode string `json:"harmonized_system_code"`
		CountryCodeOfOrigin  string `json:"country_code_of_origin"`
		AmountSet
		TaxLines          []TaxLine `json:"tax_lines"`
		AdminGraphqlApiId string    `json:"admin_graphql_api_id"`
	} `json:"duties,omitempty"`
}

type DiscountAllocations struct {
	Amount                   *decimal.Decimal `json:"amount"`
	DiscountApplicationIndex int              `json:"discount_application_index"`
	AmountSet                *AmountSet       `json:"amount_set"`
}

type AmountSet struct {
	ShopMoney        AmountSetEntry `json:"shop_money"`
	PresentmentMoney AmountSetEntry `json:"presentment_money"`
}

type AmountSetEntry struct {
	Amount       *decimal.Decimal `json:"amount"`
	CurrencyCode string           `json:"currency_code"`
}
type LineItemProperty struct {
	Message string `json:"message"`
}

type NoteAttribute struct {
	Name  string      `json:"name"`
	Value interface{} `json:"value"`
}

// OrderResource Represents the result from the orders/X.json endpoint
type OrderResource struct {
	Order *Order `json:"order"`
}

// OrdersResource Represents the result from the orders.json endpoint
type OrdersResource struct {
	Orders []Order `json:"orders"`
}

type ShippingLines struct {
	ID                            int64            `json:"id,omitempty"`
	Title                         string           `json:"title,omitempty"`
	Price                         *decimal.Decimal `json:"price,omitempty"`
	PriceSet                      *AmountSet       `json:"price_set,omitempty"`
	DiscountedPrice               *decimal.Decimal `json:"discounted_price,omitempty"`
	DiscountedPriceSet            *AmountSet       `json:"discounted_price_set,omitempty"`
	Code                          string           `json:"code,omitempty"`
	Source                        string           `json:"source,omitempty"`
	Phone                         string           `json:"phone,omitempty"`
	RequestedFulfillmentServiceID string           `json:"requested_fulfillment_service_id,omitempty"`
	CarrierIdentifier             string           `json:"carrier_identifier,omitempty"`
	TaxLines                      []TaxLine        `json:"tax_lines,omitempty"`
	Handle                        string           `json:"handle,omitempty"`
}

type TaxLine struct {
	Title    string  `json:"title"`
	Price    string  `json:"price"`
	Rate     float64 `json:"rate"`
	PriceSet struct {
		ShopMoney struct {
			Amount       string `json:"amount"`
			CurrencyCode string `json:"currency_code"`
		} `json:"shop_money"`
		PresentmentMoney struct {
			Amount       string `json:"amount"`
			CurrencyCode string `json:"currency_code"`
		} `json:"presentment_money"`
	} `json:"price_set"`
	ChannelLiable bool `json:"channel_liable"`
}

type Transaction struct {
	ID            int64            `json:"id,omitempty"`
	OrderID       int64            `json:"order_id,omitempty"`
	Amount        *decimal.Decimal `json:"amount,omitempty"`
	Kind          string           `json:"kind,omitempty"`
	Gateway       string           `json:"gateway,omitempty"`
	Status        string           `json:"status,omitempty"`
	Message       string           `json:"message,omitempty"`
	CreatedAt     *time.Time       `json:"created_at,omitempty"`
	Test          bool             `json:"test,omitempty"`
	Authorization string           `json:"authorization,omitempty"`
	Currency      string           `json:"currency,omitempty"`
	LocationID    *int64           `json:"location_id,omitempty"`
	UserID        *int64           `json:"user_id,omitempty"`
	ParentID      *int64           `json:"parent_id,omitempty"`
	DeviceID      *int64           `json:"device_id,omitempty"`
	ErrorCode     string           `json:"error_code,omitempty"`
	SourceName    string           `json:"source_name,omitempty"`
	Source        string           `json:"source,omitempty"`
}

type ClientDetails struct {
	AcceptLanguage string `json:"accept_language"`
	BrowserHeight  int    `json:"browser_height"`
	BrowserIp      string `json:"browser_ip"`
	BrowserWidth   int    `json:"browser_width"`
	SessionHash    string `json:"session_hash"`
	UserAgent      string `json:"user_agent"`
}

type Refund struct {
	Id               int64             `json:"id,omitempty"`
	OrderId          int64             `json:"order_id,omitempty"`
	CreatedAt        *time.Time        `json:"created_at,omitempty"`
	Note             string            `json:"note,omitempty"`
	Restock          bool              `json:"restock,omitempty"`
	UserId           int64             `json:"user_id,omitempty"`
	RefundLineItems  []RefundLineItem  `json:"refund_line_items,omitempty"`
	Transactions     []Transaction     `json:"transactions,omitempty"`
	OrderAdjustments []OrderAdjustment `json:"order_adjustments,omitempty"`
}
type OrderAdjustmentType string
type OrderAdjustment struct {
	Id           int64               `json:"id"`
	OrderId      int64               `json:"order_id"`
	RefundId     int64               `json:"refund_id"`
	Amount       *decimal.Decimal    `json:"amount"`
	TaxAmount    *decimal.Decimal    `json:"tax_amount"`
	Kind         OrderAdjustmentType `json:"kind"`
	Reason       string              `json:"reason"`
	AmountSet    *AmountSet          `json:"amount_set"`
	TaxAmountSet *AmountSet          `json:"tax_amount_set"`
}

const (
	OrderAdjustmentTypeShippingRefund    OrderAdjustmentType = "shipping_refund"
	OrderAdjustmentTypeRefundDiscrepancy OrderAdjustmentType = "refund_discrepancy"
)

type RefundLineItem struct {
	Id         int64            `json:"id"`
	Quantity   int              `json:"quantity"`
	LineItemId int64            `json:"line_item_id"`
	LineItem   *LineItem        `json:"line_item"`
	Subtotal   *decimal.Decimal `json:"subtotal"`
	TotalTax   *decimal.Decimal `json:"total_tax"`
}

const ordersBasePath = "orders"
const ordersResourceName = "orders"

// OrderService is an interface for interfacing with the orders endpoints of
// the Shopify API.
// See: https://help.shopify.com/api/reference/order
type OrderService interface {
	List(interface{}) ([]Order, error)
	ListWithPagination(interface{}) ([]Order, *Pagination, error)
	Count(interface{}) (int, error)
	Get(int64, interface{}) (*Order, error)
	Create(Order) (*Order, error)
	Update(Order) (*Order, error)
	Cancel(int64, interface{}) (*Order, error)
	Close(int64) (*Order, error)
	Open(int64) (*Order, error)
	Delete(int64) error

	// FulfillmentsService used for Order resource to communicate with Fulfillments resource
	FulfillmentsService
}

// OrderServiceOp handles communication with the order related methods of the
// Shopify API.
type OrderServiceOp struct {
	client *Client
}

type orderStatus string

// https://shopify.dev/docs/api/admin-rest/2023-07/resources/order#get-orders?status=any
const (
	// OrderStatusOpen Show only open orders.
	OrderStatusOpen orderStatus = "open"

	// OrderStatusClosed Show only closed orders.
	OrderStatusClosed orderStatus = "closed"

	// OrderStatusCancelled Show only cancelled orders.
	OrderStatusCancelled orderStatus = "cancelled"

	// OrderStatusAny Show orders of any status, open, closed, cancellerd, or archived.
	OrderStatusAny orderStatus = "any"
)

type orderFulfillmentStatus string

// https://shopify.dev/docs/api/admin-rest/2023-07/resources/order#get-orders?status=any
const (
	// OrderFulfillmentStatusShipped Show orders that have been shipped.
	OrderFulfillmentStatusShipped orderFulfillmentStatus = "shipped"

	// OrderFulfillmentStatusPartial Show partially shipped orders.
	OrderFulfillmentStatusPartial orderFulfillmentStatus = "partial"

	// OrderFulfillmentStatusUnshipped Show orders that have not yet been shipped.
	OrderFulfillmentStatusUnshipped orderFulfillmentStatus = "unshipped"

	// OrderFulfillmentStatusAny Show orders of any fulfillment status.
	OrderFulfillmentStatusAny orderFulfillmentStatus = "any"

	// OrderFulfillmentStatusUnfulfilled Returns orders with fulfillment_status of null or partial.
	OrderFulfillmentStatusUnfulfilled orderFulfillmentStatus = "unfulfilled"

	// OrderFulfillmentStatusFulfilled "fulfilled" used to be an acceptable value? Was it deprecated? It isn't noted
	//in the Shopify docs at the provided URL, but it was used in tests and still
	//seems to function.
	OrderFulfillmentStatusFulfilled orderFulfillmentStatus = "fulfilled"
)

type orderFinancialStatus string

// https://shopify.dev/docs/api/admin-rest/2023-07/resources/order#get-orders?status=any
const (
	// OrderFinancialStatusAuthorized Show only authorized orders.
	OrderFinancialStatusAuthorized orderFinancialStatus = "authorized"

	// OrderFinancialStatusPending Show only pending orders.
	OrderFinancialStatusPending orderFinancialStatus = "pending"

	// OrderFinancialStatusPaid Show only paid orders.
	OrderFinancialStatusPaid orderFinancialStatus = "paid"

	// OrderFinancialStatusPartiallyPaid Show only partially paid orders.
	OrderFinancialStatusPartiallyPaid orderFinancialStatus = "partially_paid"

	// OrderFinancialStatusRefunded Show only refunded orders.
	OrderFinancialStatusRefunded orderFinancialStatus = "refunded"

	// OrderFinancialStatusVoided Show only voided orders.
	OrderFinancialStatusVoided orderFinancialStatus = "voided"

	// OrderFinancialStatusPartiallyRefunded Show only partially refunded orders.
	OrderFinancialStatusPartiallyRefunded orderFinancialStatus = "partially_refunded"

	// OrderFinancialStatusAny Show orders of any financial status.
	OrderFinancialStatusAny orderFinancialStatus = "any"

	// OrderFinancialStatusUnpaid Show authorized and partially paid orders.
	OrderFinancialStatusUnpaid orderFinancialStatus = "unpaid"
)

type orderCancelReason string

const (
	// OrderCancelReasonCustomer The customer canceled the order.
	OrderCancelReasonCustomer orderCancelReason = "customer"

	// OrderCancelReasonFraud The order was fraudulent.
	OrderCancelReasonFraud orderCancelReason = "fraud"

	// OrderCancelReasonInventory Items in the order were not in inventory.
	OrderCancelReasonInventory orderCancelReason = "inventory"

	// OrderCancelReasonDeclined The payment was declined.
	OrderCancelReasonDeclined orderCancelReason = "declined"

	// OrderCancelReasonOther Cancelled for some other reason.
	OrderCancelReasonOther orderCancelReason = "other"
)

// OrderCountOptions A struct for all available order count options
type OrderCountOptions struct {
	Page              int                    `url:"page,omitempty"`
	Limit             int                    `url:"limit,omitempty"`
	SinceID           int64                  `url:"since_id,omitempty"`
	CreatedAtMin      time.Time              `url:"created_at_min,omitempty"`
	CreatedAtMax      time.Time              `url:"created_at_max,omitempty"`
	UpdatedAtMin      time.Time              `url:"updated_at_min,omitempty"`
	UpdatedAtMax      time.Time              `url:"updated_at_max,omitempty"`
	Order             string                 `url:"order,omitempty"`
	Fields            string                 `url:"fields,omitempty"`
	Status            orderStatus            `url:"status,omitempty"`
	FinancialStatus   orderFinancialStatus   `url:"financial_status,omitempty"`
	FulfillmentStatus orderFulfillmentStatus `url:"fulfillment_status,omitempty"`
}

// OrderListOptions A struct for all available order list options.
// See: https://help.shopify.com/api/reference/order#index
type OrderListOptions struct {
	ListOptions
	Status            orderStatus            `url:"status,omitempty"`
	FinancialStatus   orderFinancialStatus   `url:"financial_status,omitempty"`
	FulfillmentStatus orderFulfillmentStatus `url:"fulfillment_status,omitempty"`
	ProcessedAtMin    time.Time              `url:"processed_at_min,omitempty"`
	ProcessedAtMax    time.Time              `url:"processed_at_max,omitempty"`
	Order             string                 `url:"order,omitempty"`
}

// OrderCancelOptions A struct of all available order cancel options.
// See: https://help.shopify.com/api/reference/order#index
type OrderCancelOptions struct {
	Amount   *decimal.Decimal `json:"amount,omitempty"`
	Currency string           `json:"currency,omitempty"`
	Restock  bool             `json:"restock,omitempty"`
	Reason   string           `json:"reason,omitempty"`
	Email    bool             `json:"email,omitempty"`
	Refund   *Refund          `json:"refund,omitempty"`
}

// List orders
func (s *OrderServiceOp) List(options interface{}) ([]Order, error) {
	orders, _, err := s.ListWithPagination(options)
	if err != nil {
		return nil, err
	}
	return orders, nil
}

func (s *OrderServiceOp) ListWithPagination(options interface{}) ([]Order, *Pagination, error) {
	path := fmt.Sprintf("%s.json", ordersBasePath)
	resource := new(OrdersResource)

	pagination, err := s.client.ListWithPagination(path, resource, options)
	if err != nil {
		return nil, nil, err
	}

	return resource.Orders, pagination, nil
}

// Count orders
func (s *OrderServiceOp) Count(options interface{}) (int, error) {
	path := fmt.Sprintf("%s/count.json", ordersBasePath)
	return s.client.Count(path, options)
}

// Get individual order
func (s *OrderServiceOp) Get(orderID int64, options interface{}) (*Order, error) {
	path := fmt.Sprintf("%s/%d.json", ordersBasePath, orderID)
	resource := new(OrderResource)
	err := s.client.Get(path, resource, options)
	return resource.Order, err
}

// Create order
func (s *OrderServiceOp) Create(order Order) (*Order, error) {
	path := fmt.Sprintf("%s.json", ordersBasePath)
	wrappedData := OrderResource{Order: &order}
	resource := new(OrderResource)
	err := s.client.Post(path, wrappedData, resource)
	return resource.Order, err
}

// Update order
func (s *OrderServiceOp) Update(order Order) (*Order, error) {
	path := fmt.Sprintf("%s/%d.json", ordersBasePath, order.ID)
	wrappedData := OrderResource{Order: &order}
	resource := new(OrderResource)
	err := s.client.Put(path, wrappedData, resource)
	return resource.Order, err
}

// Cancel order
func (s *OrderServiceOp) Cancel(orderID int64, options interface{}) (*Order, error) {
	path := fmt.Sprintf("%s/%d/cancel.json", ordersBasePath, orderID)
	resource := new(OrderResource)
	err := s.client.Post(path, options, resource)
	return resource.Order, err
}

// Close order
func (s *OrderServiceOp) Close(orderID int64) (*Order, error) {
	path := fmt.Sprintf("%s/%d/close.json", ordersBasePath, orderID)
	resource := new(OrderResource)
	err := s.client.Post(path, nil, resource)
	return resource.Order, err
}

// Open order
func (s *OrderServiceOp) Open(orderID int64) (*Order, error) {
	path := fmt.Sprintf("%s/%d/open.json", ordersBasePath, orderID)
	resource := new(OrderResource)
	err := s.client.Post(path, nil, resource)
	return resource.Order, err
}

// Delete order
func (s *OrderServiceOp) Delete(orderID int64) error {
	path := fmt.Sprintf("%s/%d.json", ordersBasePath, orderID)
	err := s.client.Delete(path)
	return err
}

// ListFulfillments List fulfillments for an order
func (s *OrderServiceOp) ListFulfillments(orderID int64, options interface{}) ([]Fulfillment, error) {
	fulfillmentService := &FulfillmentServiceOp{client: s.client, resource: ordersResourceName, resourceID: orderID}
	return fulfillmentService.List(options)
}

// CountFulfillments Count fulfillments for an order
func (s *OrderServiceOp) CountFulfillments(orderID int64, options interface{}) (int, error) {
	fulfillmentService := &FulfillmentServiceOp{client: s.client, resource: ordersResourceName, resourceID: orderID}
	return fulfillmentService.Count(options)
}

// GetFulfillment Get individual fulfillment for an order
func (s *OrderServiceOp) GetFulfillment(orderID int64, fulfillmentID int64, options interface{}) (*Fulfillment, error) {
	fulfillmentService := &FulfillmentServiceOp{client: s.client, resource: ordersResourceName, resourceID: orderID}
	return fulfillmentService.Get(fulfillmentID, options)
}

// CreateFulfillment Create a new fulfillment for an order
func (s *OrderServiceOp) CreateFulfillment(orderID int64, fulfillment Fulfillment) (*Fulfillment, error) {
	fulfillmentService := &FulfillmentServiceOp{client: s.client, resource: ordersResourceName, resourceID: orderID}
	return fulfillmentService.Create(fulfillment)
}

// UpdateFulfillment Update an existing fulfillment for an order
func (s *OrderServiceOp) UpdateFulfillment(orderID int64, fulfillment Fulfillment) (*Fulfillment, error) {
	fulfillmentService := &FulfillmentServiceOp{client: s.client, resource: ordersResourceName, resourceID: orderID}
	return fulfillmentService.Update(fulfillment)
}

// CompleteFulfillment Complete an existing fulfillment for an order
func (s *OrderServiceOp) CompleteFulfillment(orderID int64, fulfillmentID int64) (*Fulfillment, error) {
	fulfillmentService := &FulfillmentServiceOp{client: s.client, resource: ordersResourceName, resourceID: orderID}
	return fulfillmentService.Complete(fulfillmentID)
}

// TransitionFulfillment Transition an existing fulfillment for an order
func (s *OrderServiceOp) TransitionFulfillment(orderID int64, fulfillmentID int64) (*Fulfillment, error) {
	fulfillmentService := &FulfillmentServiceOp{client: s.client, resource: ordersResourceName, resourceID: orderID}
	return fulfillmentService.Transition(fulfillmentID)
}

// CancelFulfillment Cancel an existing fulfillment for an order
func (s *OrderServiceOp) CancelFulfillment(orderID int64, fulfillmentID int64) (*Fulfillment, error) {
	fulfillmentService := &FulfillmentServiceOp{client: s.client, resource: ordersResourceName, resourceID: orderID}
	return fulfillmentService.Cancel(fulfillmentID)
}
