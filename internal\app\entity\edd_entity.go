package entity

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/sirupsen/logrus"

	"tmshopify/store/database"
	"tmshopify/store/models"
	"tmshopify/store/models/order"
)

const (
	PreEdd int = iota
	Edd
)

type EddRange struct {
	MaxRange int           `json:"maxRange"`
	MinRange int           `json:"minRange"`
	Region   []interface{} `json:"region"`
	ZoneName string        `json:"zone_name"`
}

type Provinces struct {
	Name string `json:"name"`
	Code string `json:"code"`
}

type CountriesItem struct {
	Name      string      `json:"name"`
	Code      string      `json:"code"`
	Provinces []Provinces `json:"provinces"`
}

type Countries struct {
	Continent []string        `json:"continent"`
	Countries []CountriesItem `json:"countries"`
	Type      string          `json:"type"`
}

type EddZoneSetting struct {
	Switch   bool       `json:"switch"`
	CalType  int        `json:"cal_type"`
	EddRange []EddRange `json:"edd_range"`
}

type EddSettingEntityStruct struct {
	Switch        bool       `json:"switch"`
	CalType       int        `json:"cal_type"`
	DateFormat    int        `json:"date_format"`
	TimeFormat    int        `json:"time_format"`
	CutoffTime    string     `json:"cutoff_time"`
	ProgressTime  int        `json:"progress_time"`
	WorkDays      []string   `json:"work_days"`
	EddRange      []EddRange `json:"edd_range"`
	TimeZone      string     `json:"time_zone"`
	StartTime     int        `json:"start_time"`
	WorkOnWeekend int        `json:"work_on_weekend"`
	Countries     Countries  `json:"countries"`
	TransitMin    int        `json:"transit_min"`
	TransitMax    int        `json:"transit_max"`
	UsePre        int        `json:"use_pre"`
	AiEdd         int        `json:"ai_edd"`
	EnableDefault int        `json:"enable_default"`
	UpdateAt      time.Time  `json:"update_at"`
}

const AI_EDD_API = "http://panguapi.trackingmore.com/pangu-api/tr/aiEdd"
const AVAILABLE_COURIER_KEY = "AI_EDD_AVAILABLE_COURIERS"
const AVAILABLE_COUNTRY_KEY = "AI_EDD_AVAILABLE_COUNTRIES"

type AvailableDataSet struct {
	Name string `json:"name,omitempty"`
	Code string `json:"code,omitempty"`
	Id   int    `json:"id"`
}

type CalculateEntity struct {
	OrderCreateTime  int
	OrderFulfillTime int
	TrackNumber      string
	Destination      string
	OrderEstimate    order.Estimate
}

// UnmarshalJSON 自定义 JSON 反序列化方法
func (e *EddRange) UnmarshalJSON(data []byte) error {
	type Alias EddRange
	aux := &struct {
		MaxRange interface{} `json:"maxRange"`
		MinRange interface{} `json:"minRange"`
		*Alias
	}{
		Alias: (*Alias)(e),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	// 处理 MinRange
	switch v := aux.MinRange.(type) {
	case string:
		if val, err := strconv.Atoi(v); err == nil {
			e.MinRange = val
		} else {
			e.MinRange = 0
		}
	case float64:
		e.MinRange = int(v)
	case int:
		e.MinRange = v
	default:
		e.MinRange = 0
	}

	// 处理 MaxRange
	switch v := aux.MaxRange.(type) {
	case string:
		if val, err := strconv.Atoi(v); err == nil {
			e.MaxRange = val
		} else {
			e.MaxRange = 0
		}
	case float64:
		e.MaxRange = int(v)
	case int:
		e.MaxRange = v
	default:
		e.MaxRange = 0
	}

	return nil
}

func HandleEddSetting(userId int) EddSettingEntityStruct {
	var data []models.ShopifyEddSettings
	err := database.DB.Model(models.ShopifyEddSettings{}).Where("user_id = ?", userId).Find(&data).Error
	if err != nil {
		logrus.Error("ShopifyEddSettings sql err", err)
	}
	var eddSetting, preSetting EddSettingEntityStruct
	for _, item := range data {
		var workDays []string
		if err := json.Unmarshal([]byte(item.WorkDays), &workDays); err != nil {
			logrus.Errorf("EDD WorkDays json parsing error %s,user country is:%s, user id %d", err.Error(), item.WorkDays, userId)
		}
		var countries Countries
		if err := json.Unmarshal([]byte(item.Countries), &countries); item.Countries != "[]" && err != nil {
			logrus.Errorf("EDD Countries json parsing error %s,user country is:%s, user id %d", err.Error(), item.Countries, userId)
		}
		if item.EddType == Edd {
			var eddZoneSetting EddZoneSetting
			if err := json.Unmarshal([]byte(item.Zones), &eddZoneSetting); err != nil {
				logrus.Errorf("EddZoneSetting json parsing error %s,user setting is:%s, user id %d", err.Error(), item.Zones, userId)
			}
			eddSetting = EddSettingEntityStruct{
				CalType:       eddZoneSetting.CalType,
				Switch:        eddZoneSetting.Switch,
				DateFormat:    item.DateFormat,
				TimeFormat:    item.TimeFormat,
				CutoffTime:    item.OrderCutoff,
				ProgressTime:  item.ProgressTime,
				WorkDays:      workDays,
				EddRange:      eddZoneSetting.EddRange,
				TimeZone:      item.TimeZone,
				WorkOnWeekend: item.WorkOnWeekend,
				Countries:     countries,
				TransitMin:    item.TransitMin,
				TransitMax:    item.TransitMax,
				UsePre:        item.UsePre,
				AiEdd:         item.AiEdd,
				EnableDefault: item.EnableDefault,
				UpdateAt:      item.UpdatedAt,
			}
		} else if item.EddType == PreEdd {
			preSetting = EddSettingEntityStruct{
				DateFormat:    item.DateFormat,
				TimeFormat:    item.TimeFormat,
				CutoffTime:    item.OrderCutoff,
				ProgressTime:  item.ProgressTime,
				WorkDays:      workDays,
				TimeZone:      item.TimeZone,
				WorkOnWeekend: item.WorkOnWeekend,
				Countries:     countries,
				TransitMin:    item.TransitMin,
				TransitMax:    item.TransitMax,
				EnableDefault: item.EnableDefault,
			}
		}
	}
	// use pre = 1时, 将 pre edd 的全局设置覆盖 edd 的设置
	if eddSetting.UsePre > 1 {
		eddSetting.DateFormat = preSetting.DateFormat
		eddSetting.TimeFormat = preSetting.TimeFormat
		eddSetting.CutoffTime = preSetting.CutoffTime
		eddSetting.ProgressTime = preSetting.ProgressTime
		eddSetting.WorkDays = preSetting.WorkDays
		eddSetting.TimeZone = preSetting.TimeZone
		eddSetting.WorkOnWeekend = preSetting.WorkOnWeekend
		eddSetting.Countries = preSetting.Countries
		eddSetting.TransitMin = preSetting.TransitMin
		eddSetting.TransitMax = preSetting.TransitMax
		eddSetting.EnableDefault = preSetting.EnableDefault
		if eddSetting.UsePre == 2 {
			var customRules []models.ShopifyEddCustomRules
			err := database.DB.Model(models.ShopifyEddCustomRules{}).Where("user_id = ?", userId).Find(&customRules).Error
			var eddRange []EddRange
			if err == nil {
				if len(customRules) > 0 {
					for _, customRule := range customRules {
						// 收集国家代码
						var countryCodes []string
						var countries Countries
						var region []interface{}
						err := json.Unmarshal([]byte(customRule.Countries), &countries)
						if err != nil {
							continue
						}
						if len(countries.Countries) > 0 {
							for _, country := range countries.Countries {
								countryCodes = append(countryCodes, country.Code)
							}

							// 获取区域ID
							err = database.DB.Model(models.ShopifyCountries{}).Where("code in", countryCodes).Pluck("id", &region).Error
							if err != nil {
								continue
							}
						}
						// 添加大洲信息
						if len(countries.Continent) > 0 {
							for _, continent := range countries.Continent {
								region = append(region, continent)
							}
						}

						// 添加到eddRange
						eddRange = append(eddRange, EddRange{
							Region:   region,
							MinRange: int(customRule.MinDays), // 假设MinDays字段在zone结构中存在
							MaxRange: int(customRule.MaxDays), // 假设MaxDays字段在zone结构中存在
							ZoneName: customRule.Name,         // 假设Name字段在zone结构中存在
						})
					}
				}
			}

			// 设置eddRange
			eddSetting.EddRange = eddRange

		}
	}

	return eddSetting
}
