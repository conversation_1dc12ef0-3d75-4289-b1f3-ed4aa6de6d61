package shopify

import (
	"fmt"
	"github.com/shopspring/decimal"
	"time"
)

const productsBasePath = "products"

type Product struct {
	Id             int64     `json:"id"`
	Title          string    `json:"title"`
	Handle         string    `json:"handle"`
	PublishedAt    time.Time `json:"published_at"`
	PublishedScope string    `json:"published_scope,omitempty"`
	Status         string    `json:"status,omitempty"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at,omitempty"`
	Vendor         string    `json:"vendor"`
	ProductType    string    `json:"product_type,omitempty"`
	BodyHtml       string    `json:"body_html,omitempty"`
	Tags           string    `json:"tags,omitempty"`
	Variants       []Variant `json:"variants"`
	Images         []Image   `json:"images"`
	Image          Image     `json:"image"`
	Options        []struct {
		Name     string   `json:"name"`
		Position int      `json:"position"`
		Values   []string `json:"values"`
	} `json:"options"`
}

type RecommendProduct struct {
	Product
	Tags                 []string `json:"tags"`
	Images               []string `json:"images"`
	Description          string   `json:"description"`
	FeaturedImage        string   `json:"featured_image"`
	Url                  string   `json:"url"`
	Type                 string   `json:"type,omitempty"`
	Price                int64    `json:"price"`
	PriceMin             int64    `json:"price_min"`
	PriceMax             int64    `json:"price_max"`
	Available            bool     `json:"available"`
	PriceVaries          bool     `json:"price_varies"`
	CompareAtPrice       int64    `json:"compare_at_price"`
	CompareAtPriceMin    int64    `json:"compare_at_price_min"`
	CompareAtPriceMax    int64    `json:"compare_at_price_max"`
	CompareAtPriceVaries bool     `json:"compare_at_price_varies"`
}

type Variant struct {
	ID                   int64            `json:"id,omitempty"`
	ProductID            int64            `json:"product_id,omitempty"`
	Title                string           `json:"title,omitempty"`
	Sku                  string           `json:"sku,omitempty"`
	Position             int              `json:"position,omitempty"`
	Grams                int              `json:"grams,omitempty"`
	InventoryPolicy      string           `json:"inventory_policy,omitempty"`
	Price                *decimal.Decimal `json:"price,omitempty"`
	CompareAtPrice       *decimal.Decimal `json:"compare_at_price,omitempty"`
	FulfillmentService   string           `json:"fulfillment_service,omitempty"`
	InventoryManagement  string           `json:"inventory_management,omitempty"`
	InventoryItemId      int64            `json:"inventory_item_id,omitempty"`
	Option1              string           `json:"option1,omitempty"`
	Option2              string           `json:"option2,omitempty"`
	Option3              string           `json:"option3,omitempty"`
	CreatedAt            *time.Time       `json:"created_at,omitempty"`
	UpdatedAt            *time.Time       `json:"updated_at,omitempty"`
	Taxable              bool             `json:"taxable,omitempty"`
	TaxCode              string           `json:"tax_code,omitempty"`
	Barcode              string           `json:"barcode,omitempty"`
	ImageID              int64            `json:"image_id,omitempty"`
	InventoryQuantity    int              `json:"inventory_quantity,omitempty"`
	Weight               *decimal.Decimal `json:"weight,omitempty"`
	WeightUnit           string           `json:"weight_unit,omitempty"`
	OldInventoryQuantity int              `json:"old_inventory_quantity,omitempty"`
	RequireShipping      bool             `json:"requires_shipping"`
	AdminGraphqlAPIID    string           `json:"admin_graphql_api_id,omitempty"`
}
type PresentmentPrice struct {
	Price          AmountSetEntry `json:"price"`
	CompareAtPrice AmountSetEntry `json:"compare_at_price"`
}
type RecommendVariant struct {
	Variant
	PublicTitle   *string `json:"public_title"`
	Available     bool    `json:"available"`
	FeaturedImage *string `json:"featured_image"`
	QuantityRule  struct {
		Min       *int `json:"min"`
		Max       *int `json:"max"`
		Increment *int `json:"increment"`
	} `json:"quantity_rule"`
	QuantityPriceBreaks []interface{} `json:"quantity_price_breaks"`
	Media               []struct {
		Alt          *string `json:"alt"`
		Id           int64   `json:"id"`
		Position     int     `json:"position"`
		PreviewImage struct {
			AspectRatio float64 `json:"aspect_ratio"`
			Height      int     `json:"height"`
			Width       int     `json:"width"`
			Src         string  `json:"src"`
		} `json:"preview_image"`
		AspectRatio float64 `json:"aspect_ratio"`
		Height      int     `json:"height"`
		MediaType   string  `json:"media_type"`
		Src         string  `json:"src"`
		Width       int     `json:"width"`
	} `json:"media"`
}
type ProductService interface {
	List(interface{}) ([]Product, error)
	ListWithPagination(interface{}) ([]Product, *Pagination, error)
	Get(productID int64, options interface{}) (*Product, error)
	GetRecommend(productID int64, limit int) ([]RecommendProduct, error)
}

type ProductServiceOp struct {
	client *Client
}

// ProductResource Represents the result from the products/X.json endpoint
type ProductResource struct {
	Product *Product `json:"product"`
}

// ProductsResource Represents the result from the products.json endpoint
type ProductsResource struct {
	Products []Product `json:"products"`
}

// RecommendProductOption 请求推荐产品列表参数
type RecommendProductOption struct {
	ProductID int64 `url:"product_id"`
	Limit     int   `url:"limit,omitempty"`
}

// ProductStatus represents a Shopify product status.
type ProductStatus string

// https://shopify.dev/docs/api/admin-rest/2023-07/resources/product#resource-object
const (
	ProductStatusActive   ProductStatus = "active"
	ProductStatusArchived ProductStatus = "archived"
	ProductStatusDraft    ProductStatus = "draft"
)

type ProductListOptions struct {
	ListOptions
	CollectionID          int64           `url:"collection_id,omitempty"`
	ProductType           string          `url:"product_type,omitempty"`
	Vendor                string          `url:"vendor,omitempty"`
	Handle                string          `url:"handle,omitempty"`
	PublishedAtMin        time.Time       `url:"published_at_min,omitempty"`
	PublishedAtMax        time.Time       `url:"published_at_max,omitempty"`
	PublishedStatus       string          `url:"published_status,omitempty"`
	PresentmentCurrencies string          `url:"presentment_currencies,omitempty"`
	Status                []ProductStatus `url:"status,omitempty,comma"`
}

// RecommendProductsResource Represents the result from the products.json endpoint
type RecommendProductsResource struct {
	Products []RecommendProduct `json:"products"`
}

// Get individual product
func (s *ProductServiceOp) Get(productID int64, options interface{}) (*Product, error) {
	path := fmt.Sprintf("%s/%d.json", productsBasePath, productID)
	resource := new(ProductResource)
	err := s.client.Get(path, resource, options)
	return resource.Product, err
}

// GetRecommend  recommend product list
func (s *ProductServiceOp) GetRecommend(productID int64, limit int) ([]RecommendProduct, error) {
	path := fmt.Sprintf("recommendations/%s.json", productsBasePath)
	resource := new(RecommendProductsResource)
	err := s.client.Get(path, resource,
		RecommendProductOption{
			ProductID: productID,
			Limit:     limit,
		})
	return resource.Products, err
}

// List products
func (s *ProductServiceOp) List(options interface{}) ([]Product, error) {
	products, _, err := s.ListWithPagination(options)
	if err != nil {
		return nil, err
	}
	return products, nil
}

// ListWithPagination lists products and return pagination to retrieve next/previous results.
func (s *ProductServiceOp) ListWithPagination(options interface{}) ([]Product, *Pagination, error) {
	path := fmt.Sprintf("%s.json", productsBasePath)
	resource := new(ProductsResource)

	pagination, err := s.client.ListWithPagination(path, resource, options)
	if err != nil {
		return nil, nil, err
	}

	return resource.Products, pagination, nil
}
