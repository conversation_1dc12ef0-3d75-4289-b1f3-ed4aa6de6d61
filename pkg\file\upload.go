package file

import (
	"bytes"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"io"
	"math/rand"
	"mime/multipart"
	"net/http"
	"os"
	"path"
	"strconv"
	"time"
	"tmshopify/config"
)

type Upload struct {
	Path string
}

type UploadResultData struct {
	FileUrl string `json:"fileUrl"`
	Cipher  string `json:"cipher"`
}

type UploadResult struct {
	Code int              `json:"code"`
	Data UploadResultData `json:"data"`
	Msg  string           `json:"msg"`
}

func (o *Upload) PostFile(filename string) (*UploadResult, error) {
	bodyBuf := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuf)
	// 关键的一步操作
	fileWriter, err := bodyWriter.CreateFormFile("file", filename)
	if err != nil {
		logrus.Errorf("error writing to buffer:%s", err.Error())
		return nil, err
	}

	// 打开文件句柄操作
	fh, err := os.Open(filename)
	if err != nil {
		logrus.Errorf("error opening file:%s", err.Error())
		return nil, err
	}
	defer fh.Close()

	// iocopy
	_, err = io.Copy(fileWriter, fh)
	if err != nil {
		logrus.Errorf("error iocopy file:%s", err.Error())
		return nil, err
	}
	err = bodyWriter.WriteField("appId", config.TrackConfig.Upload.Sign)
	if err != nil {
		logrus.Errorf("error write body:%s", err.Error())
		return nil, err
	}
	contentType := bodyWriter.FormDataContentType()
	bodyWriter.Close()

	resp, err := http.Post(config.TrackConfig.Upload.Server, contentType, bodyBuf)
	if err != nil {
		logrus.Errorf("upload file fail:%s", err.Error())
		return nil, err
	}
	defer resp.Body.Close()
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logrus.Errorf("response body read fail:%s", err.Error())
		return nil, err
	}
	logrus.Warning(string(respBody))
	var result UploadResult
	err = json.Unmarshal(respBody, &result)
	if err != nil {
		logrus.Errorf("Unmarshal body  fail:%s", err.Error())
		return nil, err
	}
	return &result, nil
}

func (o *Upload) SaveFile(file *multipart.FileHeader, c *gin.Context) (string, error) {
	t := time.Now()
	date := t.Format("20060102")
	pathTmp := o.Path + "/" + date + "/"
	fileName := strconv.FormatInt(time.Now().Unix(), 10) + strconv.Itoa(rand.Intn(999999-100000)+100000) + path.Ext(file.Filename)
	filePath := pathTmp + fileName
	if isDirExists(pathTmp) {
		logrus.Warning("目录存在")
	} else {
		err := os.MkdirAll(pathTmp, 0777)
		if err != nil {
			logrus.Error(err)
			return filePath, err
		}
	}
	//文件名
	saveErr := c.SaveUploadedFile(file, filePath)
	if saveErr != nil {
		logrus.Error(saveErr)
		return filePath, saveErr
	}
	return filePath, nil
}

func (o *Upload) RemoveLocalFile(name string) error {
	return os.Remove(name)
}

func isDirExists(filename string) bool {
	_, err := os.Stat(filename)
	if os.IsNotExist(err) {
		return false
	}
	return true
}

func NewUpload(path string) *Upload {
	return &Upload{
		Path: path,
	}
}
