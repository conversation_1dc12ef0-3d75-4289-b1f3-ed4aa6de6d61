package service

import (
	"github.com/dromara/carbon/v2"
	"time"
	"tmshopify/pkg/domain"
	"tmshopify/pkg/utils/paginate"
	"tmshopify/server/response"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type Integration struct {
	integrationAppRep      *impl.IntegrationApp
	integrationCategoryRep *impl.IntegrationCategory
	userIntegrationRep     *impl.UserIntegration
	promotionClickLogRep   *impl.PromotionClickLog
	integrationAdsRep      *impl.IntegrationAds
}

type dashboardClickData struct {
	Name  string `json:"name"`
	Value int    `json:"value"`
}
type integrationClickData struct {
	Name   string `json:"name"`
	Clicks int    `json:"clicks"`
	Source string `json:"source"`
}

func (i *Integration) AppQuery(search *impl.IntegrationSearchParams) (*response.SuccessResponse, error) {

	items, total, err := i.integrationAppRep.Paginate(search, &search.Param)

	return &response.SuccessResponse{
		Data: map[string]interface{}{
			"items":    items,
			"page":     search.Param.Page,
			"total":    total,
			"pageSize": search.Param.PageSize,
		},
	}, err
}

func (i *Integration) GetCategoryList() (*response.SuccessResponse, error) {
	result, err := i.integrationCategoryRep.Get()
	if err != nil {
		return &response.SuccessResponse{}, err
	}
	return &response.SuccessResponse{
		Data: result,
	}, err
}

func (i *Integration) DeleteApp(id string) (models.IntegrationApps, error) {
	var integrationApp models.IntegrationApps
	err := i.integrationAppRep.DeleteById(integrationApp, id)
	return integrationApp, err
}

func (i *Integration) Update(m *models.IntegrationApps) error {
	return i.integrationAppRep.Update(m)
}

func (i *Integration) StoreApp(m models.IntegrationApps) error {

	return i.integrationAppRep.Create(&m)
}

func (i *Integration) IsUniqueAppName(name string, id int) bool {
	return i.integrationAppRep.IsUniqueAppByNameAndId(name, id)
}
func (i *Integration) IsUniqueAppSettingLink(settingLink string, id int) bool {
	return i.integrationAppRep.IsUniqueAppBySettingLinkAndId(settingLink, id)
}

func (i *Integration) ChangeAppStatus(id string, status int) error {
	return i.integrationAppRep.UpdateByMap(models.IntegrationApps{}, id, map[string]interface{}{
		"status": status,
	})
}

func (i *Integration) UpdateAppSort(id string, sort int) error {
	return i.integrationAppRep.UpdateByMap(models.IntegrationApps{}, id, map[string]interface{}{
		"sort": sort,
	})
}

func (i *Integration) CreateCategory(name string) error {
	return i.integrationCategoryRep.Create(&models.IntegrationCategories{
		Name: name,
	})
}

func (i *Integration) UpdateCategory(id string, name string) error {
	return i.integrationCategoryRep.Update(id, map[string]interface{}{"name": name})
}

func (i *Integration) DeleteCategory(id string) (models.IntegrationCategories, error) {
	var integrationCategories models.IntegrationCategories
	err := i.integrationCategoryRep.DeleteById(integrationCategories, id)
	if err == nil {
		err = i.integrationAppRep.UpdatedWhenCategoryDeleted(id)
	}
	return integrationCategories, err
}

func (i *Integration) IsUniqueCategoryName(name string, id string) bool {
	return i.integrationCategoryRep.IsUniqueCategoryByNameAndId(name, id)
}

func (i *Integration) GetPromotionClickData(startTime, endTime *carbon.Carbon) (*response.SuccessResponse, error) {
	clickLogs, err := i.promotionClickLogRep.GetPromotionClickLogByDate(startTime, endTime)
	var dashboardClickLog []dashboardClickData
	var integrationClickLog []integrationClickData
	for _, log := range clickLogs {
		if log.Source == 1 {
			dashboardClickLog = append(dashboardClickLog, dashboardClickData{
				Name:  log.AppName,
				Value: log.Clicks,
			})
		} else {
			source := domain.SourceList[log.Source]
			integrationClickLog = append(integrationClickLog, integrationClickData{
				Name:   log.AppName,
				Clicks: log.Clicks,
				Source: source,
			})
		}
	}
	if dashboardClickLog == nil {
		dashboardClickLog = []dashboardClickData{}
	}
	if integrationClickLog == nil {
		integrationClickLog = []integrationClickData{}
	}

	return &response.SuccessResponse{
		Data: map[string]interface{}{
			"dashboard_click_pie":     dashboardClickLog,
			"integration_click_table": integrationClickLog,
		},
	}, err
}

func (i *Integration) GetIntegrationAdsList(param paginate.Param) []interface{} {
	result, _ := i.integrationAdsRep.GetIntegrationAdsList(param)
	if result == nil {
		result = []models.IntegrationAds{}
	}
	data := make([]interface{}, 0)
	for _, item := range result {
		var draftAt string
		if item.DraftAt.Valid {
			draftAt = item.DraftAt.Time.Format(time.DateTime)
		}
		data = append(data, map[string]interface{}{
			"id":            item.ID,
			"title":         item.Title,
			"description":   item.Description,
			"image":         item.Image,
			"bg_color":      item.BgColor,
			"referral_link": item.ReferralLink,
			"style":         item.Style,
			"sort":          item.Sort,
			"show_at":       item.ShowAt.Format(time.DateTime),
			"draft_at":      draftAt,
			"created_at":    item.CreatedAt,
			"updated_at":    item.UpdatedAt,
		})
	}
	return data
}

func (i *Integration) CreateIntegrationAds(m *models.IntegrationAds) error {
	return i.integrationAdsRep.Create(m)
}

func (i *Integration) UpdateIntegrationAds(m *models.IntegrationAds) error {
	return i.integrationAdsRep.Update(m)
}

func (i *Integration) UpdateAdSort(id string, sort int) error {
	return i.integrationAdsRep.UpdateByMap(models.IntegrationAds{}, id, map[string]interface{}{
		"sort": sort,
	})
}

func NewIntegration() *Integration {
	return &Integration{
		integrationAppRep:      &impl.IntegrationApp{},
		integrationCategoryRep: &impl.IntegrationCategory{},
		userIntegrationRep:     &impl.UserIntegration{},
		promotionClickLogRep:   &impl.PromotionClickLog{},
		integrationAdsRep:      &impl.IntegrationAds{},
	}
}
