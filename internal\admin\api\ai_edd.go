package api

import (
	"encoding/json"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"

	"tmshopify/internal/admin/request"
	"tmshopify/server/response"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type AiEdd struct {
}

const (
	countriesCacheKey = "AI_EDD_AVAILABLE_COUNTRIES"
	couriersCacheKey  = "AI_EDD_AVAILABLE_COURIERS"
)

type dataSet struct {
	Name string `json:"name,omitempty"`
	Code string `json:"code,omitempty"`
	Id   int    `json:"id"`
}

func (a *AiEdd) GetCountries(c *gin.Context) {
	data, _ := database.RS.Get(countriesCacheKey)

	var counties []dataSet

	err := json.Unmarshal(data, &counties)
	if err != nil || string(data) == "" || len(counties) == 0 {
		response.Success(c, &response.SuccessResponse{
			Data: map[string]interface{}{},
		})
		return
	}
	response.Success(c, &response.SuccessResponse{
		Data: counties,
	})
}

type dataType string

const (
	country dataType = "country"
	courier dataType = "courier"
)

func (a *AiEdd) DeleteCountry(c *gin.Context) {
	// 获取路由参数 id
	countryId := c.Param("id")
	// 将字符串转换为整数
	id, err := strconv.Atoi(countryId)
	if err != nil {
		response.Fail(c, &response.FailResponse{
			Code:    http.StatusBadRequest,
			Message: "无效的ID参数",
		})
		return
	}
	cacheKey := countriesCacheKey
	a.deleteCountryOrCourierById(c, id, cacheKey)
}

func (a *AiEdd) StoreCountries(c *gin.Context) {
	cacheKey := countriesCacheKey
	a.storeCourierOrCountry(c, country, cacheKey)
}

func (a *AiEdd) GetCouriers(c *gin.Context) {
	data, _ := database.RS.Get(couriersCacheKey)

	var counties []dataSet

	err := json.Unmarshal(data, &counties)
	if err != nil || string(data) == "" || len(counties) == 0 {
		response.Success(c, &response.SuccessResponse{
			Data: map[string]interface{}{},
		})
		return
	}
	response.Success(c, &response.SuccessResponse{
		Data: counties,
	})
}

func (a *AiEdd) StoreCouriers(c *gin.Context) {
	cacheKey := couriersCacheKey
	a.storeCourierOrCountry(c, courier, cacheKey)
}

func (a *AiEdd) DeleteCourier(c *gin.Context) {
	// 获取路由参数 id
	courierId := c.Param("id")
	// 将字符串转换为整数
	id, err := strconv.Atoi(courierId)
	if err != nil {
		response.Fail(c, &response.FailResponse{
			Code:    http.StatusBadRequest,
			Message: "无效的ID参数",
		})
		return
	}
	cacheKey := couriersCacheKey
	a.deleteCountryOrCourierById(c, id, cacheKey)
}

func (a *AiEdd) storeCourierOrCountry(c *gin.Context, dataType dataType, cacheKey string) {

	data, _ := database.RS.Get(cacheKey)
	var couriersOrCountries []dataSet
	if string(data) == "" {
		couriersOrCountries = []dataSet{}
	} else {
		err := json.Unmarshal(data, &couriersOrCountries)
		if err != nil {
			response.Fail(c, &response.FailResponse{
				Code:    http.StatusServiceUnavailable,
				Message: "无法解析原数据",
			})
			return
		}
	}
	var m dataSet
	if err := c.ShouldBindJSON(&m); err != nil {
		response.UnprocessableEntity(c, err.Error())
		return
	}

	var validate = validator.New()
	validate.RegisterStructValidationMapRules(request.UpdateDataSet, dataSet{})
	validate.RegisterStructValidationMapRules(request.UpdateDataSet, models.Model{})
	if err := validate.Struct(m); err != nil {
		response.UnprocessableEntity(c, err.Error())
		return
	}
	switch dataType {
	case country:
		err := database.DB.Model(&models.ShopifyCountries{}).First(&m, m.Id).Error
		if err != nil {
			response.NotFound(c, err.Error())
			return
		}
	case courier:
		var courierM models.Couriers
		err := database.DB.Model(&models.Couriers{}).First(&courierM, m.Id).Error
		if err != nil {
			response.NotFound(c, err.Error())
			return
		}
		m.Name = courierM.CompanyName
		m.Code = courierM.CompanyCode
	default:
		response.NotFound(c, "Unhandle data type")
		return
	}
	// 找到对应的国家记录进行更新
	found := false
	if len(couriersOrCountries) > 0 {
		for i := range couriersOrCountries {
			if couriersOrCountries[i].Id == m.Id {
				// 更新现有记录
				couriersOrCountries[i] = m
				found = true
				break
			}
		}
	}

	// 如果没找到对应记录，则添加新记录
	if !found {
		couriersOrCountries = append(couriersOrCountries, m)
	}

	// 将更新后的数据转换为JSON并写回Redis缓存
	updatedData, err := json.Marshal(couriersOrCountries)
	if err != nil {
		response.Fail(c, &response.FailResponse{
			Code:    http.StatusInternalServerError,
			Message: "序列化数据失败",
		})
		return
	}

	// 写入Redis缓存
	err = database.RS.Set(cacheKey, string(updatedData))
	if err != nil {
		response.Fail(c, &response.FailResponse{
			Code:    http.StatusInternalServerError,
			Message: "更新缓存失败",
		})
		return
	}

	// 成功响应
	response.Success(c, &response.SuccessResponse{
		Data: m,
	})
}

func (a *AiEdd) deleteCountryOrCourierById(c *gin.Context, id int, key string) {
	data, _ := database.RS.Get(key)
	var counties []dataSet
	if string(data) != "" {
		err := json.Unmarshal(data, &counties)
		if err != nil {
			response.Fail(c, &response.FailResponse{
				Code:    http.StatusServiceUnavailable,
				Message: "无法解析原数据",
			})
			return
		}
	}

	// 创建一个新切片，排除指定ID的对象
	var newCounties []dataSet
	found := false

	for _, country := range counties {
		if country.Id != id {
			newCounties = append(newCounties, country)
		} else {
			found = true
		}
	}

	// 如果没有找到指定ID的国家
	if !found {
		response.Fail(c, &response.FailResponse{
			Code:    http.StatusNotFound,
			Message: "未找到指定ID的数据",
		})
		return
	}

	// 将更新后的数据转换为JSON并写回Redis缓存
	updatedData, err := json.Marshal(newCounties)
	if err != nil {
		response.Fail(c, &response.FailResponse{
			Code:    http.StatusInternalServerError,
			Message: "序列化数据失败",
		})
		return
	}

	// 写入Redis缓存
	err = database.RS.Set(key, string(updatedData))
	if err != nil {
		response.Fail(c, &response.FailResponse{
			Code:    http.StatusInternalServerError,
			Message: "更新缓存失败",
		})
		return
	}

	// 成功响应
	response.Success(c, &response.SuccessResponse{
		Message: "已成功删除",
	})
}
