package models

type Setting string

const (
	UserTimeZone Setting = "user_time_zone" //用户时区设置
	Lang         Setting = "lang"           //用户语言设置
)

type UserSettings struct {
	UserId int    `gorm:"column:user_id;type:int(11);NOT NULL" json:"user_id"`
	Key    string `gorm:"column:key;type:varchar(255);comment:设置项名;NOT NULL" json:"key"`
	Value  string `gorm:"column:value;type:varchar(255);comment:设置项值;NOT NULL" json:"value"`
	Model
}

func (u UserSettings) TableName() string {
	return "user_settings"
}
