package main

import (
	"github.com/sirupsen/logrus"
	"testing"
	"tmshopify/pkg/jobs"
	"tmshopify/pkg/shopify/graphql"
	"tmshopify/store/logger"
	"tmshopify/store/models"
)

func TestJob(t *testing.T) {
	logger.InitLogger("./logs/app")
	logrus.Warn("test start")
	job := jobs.BatchSaveProductJob{BatchSaveProductJobStruct: jobs.BatchSaveProductJobStruct{Products: []graphql.ProductInfo{}, User: models.Users{}}}
	job.Dispatch()
	logrus.Warn("test end")
}
