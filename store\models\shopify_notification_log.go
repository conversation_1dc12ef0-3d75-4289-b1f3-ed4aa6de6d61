package models

type ShopifyNotificationLog struct {
	UserId                 int    `gorm:"column:user_id;type:int(11);comment:user id;NOT NULL" json:"user_id"`
	TrackNumber            string `gorm:"column:track_number;type:varchar(50);comment:单号;NOT NULL" form:"track_number" json:"track_number"`
	OrderId                int    `gorm:"column:order_id;type:varchar(20);comment:单号id;NOT NULL" form:"order_id" json:"order_id"`
	FulfillmentId          int    `gorm:"column:fulfillment_id;type:varchar(20);comment:fulfillment_id;NOT NULL" form:"fulfillment_id" json:"fulfillment_id"`
	SendTimes              string `gorm:"column:send_times;type:int(10);default:0;comment:该状态发送次数;NOT NULL" form:"send_times" json:"send_times"`
	FulfillmentEventStatus string `gorm:"column:fulfillment_event_status;type:varchar(20);default:'';comment:fulfillment_event_status;NOT NULL" form:"fulfillment_event_status" json:"fulfillment_event_status"`
	SendFailTimes          int    `gorm:"column:send_fail_times;type:int(10);default:0;comment:该状态发送失败次数;NOT NULL" form:"send_fail_times" json:"send_fail_times"`
	LastFailReason         string `gorm:"column:last_fail_reason;type:text;comment:最后一次失败原因;NOT NULL" form:"last_fail_reason" json:"last_fail_reason"`
	Model
}

func (ShopifyNotificationLog) TableName() string {
	return "shopify_notification_logs"
}
