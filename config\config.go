package config

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

type Config struct {
	GinMode          string        `yaml:"gin_mode"`
	Addr             string        `yaml:"addr"`
	AppAddr          string        `yaml:"app_addr"`
	WebhookAddr      string        `yaml:"webhook_addr"`
	DSN              string        `yaml:"dsn"`
	SlowSql          int           `yaml:"slow_sql"`
	AppEnv           string        `yaml:"app_env"`
	MaxIdleConn      int           `yaml:"max_idle_conn"`
	JwtSecret        string        `yaml:"jwt_secret"`
	JwtExpireDay     int           `yaml:"jwt_expire_day"`
	RedisHost        string        `yaml:"redis_host"`
	RedisPassword    string        `yaml:"redis_password"`
	RedisMaxIdle     int           `yaml:"redis_max_idle"`
	RedisPrefix      string        `yaml:"redis_prefix"`
	RedisActive      int           `yaml:"redis_active"`
	RedisIdleTimeout time.Duration `yaml:"redis_idle_timeout"`
	SQLDebug         bool          `yaml:"sql_debug"`
	ShopifyApiKey    string        `yaml:"shopify_api_key"`
	ShopifyApiSecret string        `yaml:"shopify_api_secret"`
	MailgunDomain    string        `yaml:"mailgun_domain"`
	MailgunApiKey    string        `yaml:"mailgun_api_key"`
	MailgunEndpoint  string        `yaml:"mailgun_endpoint"`
	MailFromName     string        `yaml:"mail_from_name"`
	MailFromAddress  string        `yaml:"mail_from_address"`
	DebugCode        string        `yaml:"debug_code"`
	ServerDomain     string        `yaml:"server_domain"`
	TestShop         string        `yaml:"test_shop" default:"tuyutian.myshopify.com"`
	TrackTest        bool          `yaml:"track_test" default:"false"`
}

var config *Config

func Load(path string) error {
	result, err := os.ReadFile(path)
	if err != nil {
		return fmt.Errorf("failed to read file: %w", err)
	}
	err = yaml.Unmarshal(result, &config)
	if err != nil {
		return fmt.Errorf("failed to unmarshal YAML: %w", err)
	}
	return nil
}

func Get() *Config {
	return config
}
