package impl

import (
	"encoding/json"
	"errors"
	"github.com/gomodule/redigo/redis"
	"strconv"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type UserSetting struct {
}

func (s *UserSetting) SetUserValue(userId int, key models.Setting, value interface{}) bool {
	if key == "" {
		return false
	}
	var redisValue string
	if _, ok := value.(bool); ok {
		if value.(bool) {
			redisValue = "1"
		} else {
			redisValue = "0"
		}
	} else if _, ok = value.(int); ok {
		redisValue = strconv.Itoa(value.(int))
	} else if _, ok = value.(string); ok {
		redisValue = value.(string)
	} else {
		jsonValue, _ := json.Marshal(value)
		redisValue = string(jsonValue)
	}

	_ = database.RS.HSet("UserSetting_"+strconv.Itoa(userId), string(key), redisValue)

	return true
}

func (s *UserSetting) GetValueByUserKey(userId int, key models.Setting) string {
	redisKey := string(key)
	value, err := database.RS.HGet("UserSetting"+strconv.Itoa(userId), redisKey)
	if !errors.Is(err, redis.ErrNil) {
		return value
	}
	var res struct {
		Value string
	}
	err = database.DB.Where("user_id = ? AND key = ?", userId, key).Select("value").Scan(&res).Error
	if err == nil {
		s.SetUserValue(userId, key, res.Value)
	}
	return res.Value
}
