package models

type TrackPageClicks struct {
	UserId     int  `gorm:"column:user_id;type:INT(11);comment:用户id;not null;" json:"user_id"`                   // 用户id
	ClickTime  int  `gorm:"column:click_time;type:INT(11);comment:每月点击次数;not null;default:0;" json:"click_time"` // 每月点击次数
	CreateDate uint `gorm:"column:create_date;type:INT(11);comment:当前月份;not null;default:0;" json:"create_date"` // 当前月份
	Model
}

func (TrackPageClicks) TableName() string {
	return "track_page_clicks"
}
