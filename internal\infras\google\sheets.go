package google

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/channelwill/athena/ratelimit"
	"github.com/redis/go-redis/v9"
	"google.golang.org/api/sheets/v4"

	"tmshopify/internal/domain/repo/google"
)

var _ google.SheetsRepository = (*googleSheetsImpl)(nil)

type googleSheetsImpl struct {
	limiter *ratelimit.TokenBucketLimiter
	srv     *sheets.Service
}

// NewSheetsRepository 创建谷歌表格资源实例
func NewSheetsRepository(redisClient redis.UniversalClient, srv *sheets.Service) google.SheetsRepository {
	// GoogleSheet api 限制调用频率为 60/min
	return &googleSheetsImpl{limiter: ratelimit.New(redisClient, 1, 1), srv: srv}
}

// Recreate 重建工作簿
func (g *googleSheetsImpl) Recreate(ctx context.Context, spreadsheetID string, sheetName string) error {
	// 先获取Spreadsheet的元数据
	spreadsheet, err := g.srv.Spreadsheets.Get(spreadsheetID).Do()
	if err != nil {
		return err
	}

	var sheetID int64
	for _, sheet := range spreadsheet.Sheets {
		if sheet.Properties.Title == sheetName {
			sheetID = sheet.Properties.SheetId
			break
		}
	}

	requests := []*sheets.Request{}

	// 如果找到现有的Sheet，创建删除请求
	if sheetID != 0 {
		requests = append(requests, &sheets.Request{
			DeleteSheet: &sheets.DeleteSheetRequest{
				SheetId: sheetID,
			},
		})
	}

	// 创建新Sheet的请求（不管原来是否存在，都创建）
	requests = append(requests, &sheets.Request{
		AddSheet: &sheets.AddSheetRequest{
			Properties: &sheets.SheetProperties{
				Title: sheetName,
			},
		},
	})

	// 发送批量更新请求
	batchUpdateReq := &sheets.BatchUpdateSpreadsheetRequest{
		Requests: requests,
	}

	// 获取令牌
	if err := <-g.wait(ctx, spreadsheetID); err != nil {
		return err
	}

	_, err = g.srv.Spreadsheets.BatchUpdate(spreadsheetID, batchUpdateReq).Context(ctx).Do()
	return err
}

// Write 往 GoogleSheets 写入数据
// 应一次写入多行, 避免频繁调用 api
func (g *googleSheetsImpl) Write(ctx context.Context, spreadsheetID string, sheetName string, index int, rows ...[]interface{}) error {
	// 写入数据
	vr := &sheets.ValueRange{}
	vr.Values = rows

	// 获取令牌
	if err := <-g.wait(ctx, spreadsheetID); err != nil {
		return err
	}

	// 设置写入范围（整个表头和数据需要写入的范围，这里从A1开始）
	_, err := g.srv.Spreadsheets.Values.
		Update(spreadsheetID, fmt.Sprintf("%s!A%d", sheetName, index), vr).
		ValueInputOption("USER_ENTERED").
		Context(ctx).
		Do()
	return err
}

func (g *googleSheetsImpl) wait(ctx context.Context, id string) chan error {
	var (
		c = make(chan error)
		// 默认 3s 超时
		timer = time.NewTimer(3 * time.Second)
		// 定时获取令牌
		ticker = time.NewTicker(100 * time.Millisecond)
	)

	for {
		select {
		case <-ctx.Done():
			c <- errors.New("ctx done")
			close(c)
			return c
		case <-timer.C:
			c <- errors.New("timeout")
			close(c)
			return c
		default:
			ok, err := g.limiter.TryAcquire(ctx, fmt.Sprintf("googleSheet:%s", id))
			// 未知错误
			if err != nil && !errors.Is(err, ratelimit.ErrAcquireFailed) {
				c <- err
				close(c)
				return c
			}

			// 令牌获取成功
			if ok {
				close(c)
				return c
			}

			// 下次轮询
			<-ticker.C
		}
	}
}
