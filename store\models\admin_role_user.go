package models

import (
	"time"
)

type AdminRoleUsers struct {
	RoleId    int64     `gorm:"column:role_id;type:bigint(20);NOT NULL" json:"role_id"`
	UserId    int64     `gorm:"column:user_id;type:bigint(20);NOT NULL" json:"user_id"`
	CreatedAt time.Time ` json:"created_at" gorm:"autoCreateTime" `
	UpdatedAt time.Time ` json:"updated_at" gorm:"autoUpdateTime" `
}

func (AdminRoleUsers) TableName() string {
	return "admin_role_users"
}
