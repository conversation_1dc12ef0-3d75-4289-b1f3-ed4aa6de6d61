# 禁用 CGO 以避免 GLIBC 版本依赖
.ONESHELL:
export CGO_ENABLED=0 GOOS=linux
# 定义默认 Go 命令
GO_CMD ?= go

# 检查是否传入了 -v 参数来指定版本
ifdef GOVERSION
	GO_CMD = go$(GOVERSION)
endif

# generate runs `go generate` to build the dynamically generated
# source files, except the protobuf stubs which are built instead with
# "make protobuf".
#rm -rf $(CURDIR)/cmd $(CURDIR)/pkg $(CURDIR)/internal $(CURDIR)/server $(CURDIR)/store $(CURDIR)/vendor $(CURDIR)/test
# 检查指定的 Go 版本是否存在
check-go-version:
ifdef GOVERSION
	@command -v go$(GOVERSION) > /dev/null 2>&1 || { echo "go$(GOVERSION) 未找到，请先安装"; exit 1; }
else
	@command -v go > /dev/null 2>&1 || { echo "go 未找到，请先安装"; exit 1; }
endif

# 将 check-go-version 添加为依赖
test: check-go-version
	$(GO_CMD) version


all:setup build-admin build-webhook build-app build-commands clean

setup:
	@echo "install dependency"
	@$(GO_CMD) mod tidy
build-admin:
	CGO_ENABLED=0 $(GO_CMD) build -o admin $(CURDIR)/cmd/admin/main.go
	@echo "build admin success"
build-webhook:
	CGO_ENABLED=0 $(GO_CMD) build -o webhook $(CURDIR)/cmd/webhook/main.go
	@echo "build webhook success"
build-commands:
	CGO_ENABLED=0 $(GO_CMD) build -o commands $(CURDIR)/cmd/job/main.go
	@echo "build commands success"
build-app:
	CGO_ENABLED=0 $(GO_CMD) build -o app $(CURDIR)/cmd/app/main.go
	@echo "build app success"
clean:
	@rm -rf $(CURDIR)/internal $(CURDIR)/pkg $(CURDIR)/server $(CURDIR)/store $(CURDIR)/test $(CURDIR)/tmp $(CURDIR)/.git
	@echo "clear success"
.PHONY: build-admin build-webhook build-commands build-app clean setup all
