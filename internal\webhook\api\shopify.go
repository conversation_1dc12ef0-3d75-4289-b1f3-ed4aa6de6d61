package api

import (
	"fmt"
	"strings"
	"time"
	"tmshopify/config"
	"tmshopify/internal/webhook/service"
	"tmshopify/pkg/shopify/webhook"
	"tmshopify/pkg/utils/helper"
	"tmshopify/server/response"
	"tmshopify/store/database"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type Shopify struct {
}

func (s *Shopify) Webhook(ctx *gin.Context) {
	topic := ctx.Request.Header.Get(webhook.XShopifyTopic)
	topic = strings.ToUpper(strings.Replace(topic, "/", "_", -1))
	var result bool
	switch topic {
	case webhook.AppUninstalled:
		result = service.NewShop().AppUninstalled(ctx)
	case webhook.OrdersCreate, webhook.OrdersUpdated:
		result = service.NewOrder().OrderCreate(ctx)
	case webhook.ShopUpdate:
		result = service.NewShop().ShopUpdate(ctx)
	case webhook.ShopRedact:
		result = service.NewShop().ShopRedact(ctx)
	case webhook.CustomersDataRequest:
	case webhook.CustomersRedact:
		result = service.NewShop().CustomersRedact(ctx)
	case webhook.AppSubscriptionsUpdate:
		result = service.NewSubscription().SubscriptionUpdate(ctx)

	}
	logrus.Debugf("handle result %d", &result)

	response.Success(ctx, &response.SuccessResponse{
		Code:    200,
		Status:  "200",
		Message: "success",
	})
}

func (s *Shopify) Test(ctx *gin.Context) {
	logrus.Warn("logrus Warn")
	err := database.RS.SetWithExpire(fmt.Sprintf("%s%d", config.CacheKey.ShopifyFlowTriggerEnabled, 1), "1", time.Second*3600*8)
	logrus.Warn(err)
	response.Success(ctx, &response.SuccessResponse{
		Code:    200,
		Status:  "200",
		Message: "success",
	})
}

func (s *Shopify) FlowWebhook(ctx *gin.Context) {
	var data webhook.FlowWebhook
	if err := ctx.ShouldBind(&data); err != nil {
		logrus.Warn("the body should be shopify shop, error:" + err.Error())
		helper.CallWilding(err.Error())
	}
	err := service.NewFlowTriggerService().SaveTrigger(data)
	if err != nil {
		logrus.Warn("save trigger fail:"+err.Error(), data)
	}
	response.Success(ctx, &response.SuccessResponse{
		Code:    200,
		Message: "success",
	})
}
