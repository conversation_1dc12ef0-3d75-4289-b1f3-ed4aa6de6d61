package orders

import (
	"context"

	"tmshopify/pkg/shopify"
	"tmshopify/store/models"
)

// OrderRepository 订单资源
type OrderRepository interface {
	GetOrderView(ctx context.Context, id int, cols ...string) (*models.OrderViewSet, error)
	GetOrderTracks(ctx context.Context, userID int, cursor int, limit int, cols ...string) ([]*models.OrderTracks, error)
	GetOrderTrackInfo(ctx context.Context, userID int, trackNumber string, courier string, cols ...string) (models.TrackInfos, error)
	GetOrderProductsByOrderID(ctx context.Context, userID int, orderID string, cols ...string) ([]models.OrderProduct, error)
	GetOrderProductsByFulfillmentID(ctx context.Context, userID int, fullID string, cols ...string) ([]models.OrderProduct, error)
}

// ShopifyOrderRepository shopify订单数据仓库
type ShopifyOrderRepository interface {
	// GetOrderData 获取订单数据
	GetOrderData(ctx context.Context, query string) ([]shopify.Order, error)
}

// OrderCountRepository 订单数量仓库
type OrderCountRepository interface {
	// GetCount 获取订单数量
	GetCount(ctx context.Context, userID int64) (int64, int64, error)
}
