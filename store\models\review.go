package models

import "database/sql"

type Reviews struct {
	UserId      uint64 `gorm:"column:user_id;type:bigint(20) unsigned;NOT NULL" json:"user_id"`
	Courier     string `gorm:"column:courier;type:varchar(100);comment:快递简码;NOT NULL" json:"courier"`
	CourierName string `gorm:"column:courier_name;type:varchar(100);comment:快递名称;NOT NULL" json:"courier_name"`
	Star        uint   `gorm:"column:star;type:tinyint(3) unsigned;default:5;comment:评分;NOT NULL" json:"star"`
	Content     string `gorm:"column:content;type:text;comment:内容;NOT NULL" json:"content"`
	TrackNumber string `gorm:"column:track_number;type:varchar(255);comment:标签;NOT NULL" json:"track_number"`
	Model
	Tags []Tags `gorm:"many2many:review_tag;foreignKey:ID;joinForeignKey:ReviewId;References:ID;joinReferences:TagId" json:"tags,omitempty"`
}
type Tags struct {
	Name            string `gorm:"column:name;type:varchar(100);comment:标签;NOT NULL" json:"name"`
	TagType         uint   `gorm:"column:tag_type;type:tinyint(3) unsigned;default:0;comment:标签类别;NOT NULL" json:"tag_type"`
	TranslationCode string `gorm:"column:translation_code;type:varchar(100);comment:记录translations表的code值;NOT NULL" json:"translation_code"`
	Model
}
type ReviewTag struct {
	ReviewId  int          `gorm:"column:review_id;type:int(11);comment:评论id;NOT NULL" json:"review_id"`
	UserId    int          `gorm:"column:user_id;type:int(11);comment:用户 id 用来统计;NOT NULL" json:"user_id"`
	TagId     int          `gorm:"column:tag_id;type:int(11);comment:标签id;NOT NULL" json:"tag_id"`
	CreatedAt sql.NullTime `gorm:"column:created_at;type:timestamp" json:"created_at"`
	UpdatedAt sql.NullTime `gorm:"column:updated_at;type:timestamp" json:"updated_at"`
}

func (m *ReviewTag) TableName() string {
	return "review_tag"
}
