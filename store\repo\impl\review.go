package impl

import (
	"strconv"
	"time"
	"tmshopify/config"
	"tmshopify/pkg/utils/helper"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type Review struct {
}

func (r *Review) GetReviewWithTags(userId int, condition CourierTrack) (models.Reviews, error) {
	var review models.Reviews
	err := database.DB.Model(&models.Reviews{}).Preload("Tags").Where("user_id=? and courier=? and track_number=?", userId, condition.Courier, condition.TrackNumber).First(&review).Error
	return review, err
}

func (r *Review) GetTranslationAll(theme models.Theme) map[string][]map[string]string {
	// 初始化一个字符串切片来存放键名
	var keys []string
	var tags []models.Tags
	// 遍历外部 map
	switch theme {
	case models.ThemeModern:
		for _, innerMap := range config.ModernTranslation {
			// 遍历内部 map，获取键名并添加到切片中
			for key := range innerMap {
				keys = append(keys, key)
			}
		}
	default:
		for _, innerMap := range config.ClassicTranslation {
			// 遍历内部 map，获取键名并添加到切片中
			for key := range innerMap {
				keys = append(keys, key)
			}
		}
	}
	database.DB.Model(&models.Tags{}).Select("tag_type,translation_code").Where("lang in ?", keys).Find(&tags)
	// 从 tags 结果中获取 translation code 列作为切片
	var tagTranslationCodes []string
	for _, tag := range tags {
		tagTranslationCodes = append(tagTranslationCodes, tag.TranslationCode)
	}

	// 查询 Translation 表中特定条件的数据
	var translations []models.Translations
	database.DB.Where("code IN ?", tagTranslationCodes).Find(&translations)

	// 创建一个多维数组来存储结果
	translationMap := make(map[string][]map[string]string)
	for _, v := range translations {
		if contains(keys, v.Lang) {
			tagType := getTagType(v.Code, tags)
			translationMap[v.Lang] = append(translationMap[v.Lang], map[string]string{
				"tag_type": strconv.Itoa(int(tagType)),
				"value":    v.Value,
			})
		}
	}
	return translationMap
}

// 判断字符串切片是否包含特定字符串
func contains(s []string, e string) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

// 根据 code 获取对应的 tag type
func getTagType(code string, tags []models.Tags) uint {
	for _, tag := range tags {
		if tag.TranslationCode == code {
			return tag.TagType
		}
	}
	return 0
}
func (r *Review) GetTagTranslationByLang(lang string, theme models.Theme) []map[string]interface{} {
	// 初始化一个字符串切片来存放键名
	var langList []string
	var tags []models.Tags
	// 遍历外部 map
	switch theme {
	case models.ThemeModern:
		for langKey := range config.ModernTranslation {
			langList = append(langList, langKey)
		}
	default:
		for langKey := range config.ClassicTranslation {
			langList = append(langList, langKey)
		}
	}
	// 创建一个数组来存储结果
	var finalTranslations []map[string]interface{}
	// 判断特定语言是否在 lang_list 中，如果不在或者是 'en'，则返回全部数据
	if !contains(langList, lang) || lang == "en" {
		var tags []models.Tags
		database.DB.Find(&tags)
		for _, tag := range tags {
			finalTranslations = append(finalTranslations, map[string]interface{}{
				"id":       tag.ID,
				"name":     tag.Name,
				"tag_type": tag.TagType,
			})
		}
		return finalTranslations
	}
	// 查询 Tag 表中的特定列
	database.DB.Model(&models.Tags{}).Select("id", "tag_type", "translation_code").Find(&tags)
	// 从 tags 结果中获取 translation code 列作为切片
	var tagTranslationCodes []string
	for _, tag := range tags {
		tagTranslationCodes = append(tagTranslationCodes, tag.TranslationCode)
	}

	// 查询 Translation 表中特定条件的数据
	var translations []models.Translations
	database.DB.Where("code IN ?", tagTranslationCodes).Where("lang = ?", lang).Find(&translations)

	for _, tag := range tags {
		for _, translation := range translations {
			if tag.TranslationCode == translation.Code {
				finalTranslations = append(finalTranslations, map[string]interface{}{
					"id":       tag.ID,
					"name":     translation.Value,
					"tag_type": tag.TagType,
				})
			}
		}
	}
	return finalTranslations
}

type CourierTrack struct {
	Courier     string `json:"courier" binding:"required"`
	TrackNumber string `json:"track_number" binding:"required"`
}
type ReviewPostData struct {
	Star        int    `json:"star" binding:"required"`
	Courier     string `json:"courier" binding:"required"`
	Content     string `json:"content" binding:"required"`
	TrackNumber string `json:"track_number" binding:"required"`
	CourierName string `json:"courier_name" binding:"required"`
	Tags        []int  `json:"tag" binding:"required"`
}

func (r *Review) UpdateOrCreate(userId int, post ReviewPostData) (models.Reviews, error) {
	var review = models.Reviews{}
	condition := map[string]interface{}{
		"user_id":      uint64(userId),
		"courier":      post.Courier,
		"track_number": post.TrackNumber,
	}
	attributes := map[string]interface{}{
		"star":         uint(post.Star),
		"content":      post.Content,
		"courier_name": post.CourierName,
		"updated_at":   time.Now().Format(time.DateTime),
	}
	// 尝试查找记录
	database.DB.Where(condition).First(&review)
	// 如果找到了记录，则更新它
	if review.ID > 0 {
		if err := database.DB.Model(&review).Updates(attributes).Error; err != nil {
			return review, err
		}
		review.Star = uint(post.Star)
		review.Content = post.Content
		review.CourierName = post.CourierName
		return review, nil
	} else {
		// 如果未找到记录，则创建新的记录
		createData := helper.MergeMaps(condition, attributes)
		createData["created_at"] = time.Now().Format(time.DateTime)
		if err := database.DB.Model(&review).Create(createData).Error; err != nil {
			return review, err
		}
		database.DB.Model(review).Where(createData).First(&review)
		return review, nil
	}
}

func (r *Review) SaveReviewTag(review models.Reviews, tagId int) error {
	var reviewTag models.ReviewTag
	return database.DB.Model(&models.ReviewTag{}).FirstOrCreate(&reviewTag, models.ReviewTag{UserId: int(review.UserId), ReviewId: review.ID, TagId: tagId}).Error
}
