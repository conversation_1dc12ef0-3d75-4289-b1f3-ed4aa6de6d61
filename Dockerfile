FROM golang:1.23-alpine AS builder
LABEL author="Yasir Tu"

# 设置环境变量
ENV LANG="en_US.UTF-8"
# 默认为生产环境
ENV APP_ENV=production
# 接收构建参数
ARG GITHUB_TOKEN

# 安装依赖
RUN apk add --no-cache git make

# 配置 Git
RUN git config --global url."https://${GITHUB_TOKEN}@github.com/".insteadOf "https://github.com/"

# 配置 Go 环境
ENV CGO_ENABLED=0 GOOS=linux GOARCH=amd64 GOPRIVATE='github.com/channelwill/*'

# 设置工作目录
WORKDIR /app

# 复制 go.mod 和 go.sum
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 安装 gops
RUN go install github.com/google/gops@latest

RUN make all

# 精简镜像阶段
FROM alpine:3.18

ENV TZ=UTC LANG="en_US.UTF-8"

# 安装运行时依赖
RUN apk update && apk upgrade \
    && apk --no-cache add net-tools tzdata ca-certificates bash curl \
    && cp /usr/share/zoneinfo/${TZ} /etc/localtime \
    && echo $TZ > /etc/timezone && apk del tzdata \
    && rm -rf /var/cache/apk/* /tmp/* /var/tmp/* /root/.cache

# 创建工作目录
WORKDIR /app

# 复制构建产物
# 从 builder 阶段复制 gops
COPY --from=builder /go/bin/gops /usr/local/bin/gops
# 从 builder 阶段复制编译好的应用
COPY --from=builder /app/admin /app/
COPY --from=builder /app/webhook /app/
COPY --from=builder /app/app /app/
COPY ./config/config-test.yaml /app/config/config-test.yaml
# 为 gops 创建目录
RUN mkdir -p /tmp/.cache

# 设置环境变量，允许 gops 连接
ENV GOPS_CONFIG_DIR=/tmp/.cache

# 复制启动脚本
COPY scripts/entrypoint.sh /app/entrypoint.sh
COPY scripts/heathcheck.sh /bin/heathcheck.sh
RUN chmod +x /app/entrypoint.sh

ENTRYPOINT ["/app/entrypoint.sh"]