package impl

import (
	"tmshopify/pkg/utils/paginate"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type ShopifyNotificationLog struct {
	Helper
}

func (s *ShopifyNotificationLog) count() {
}

func (s *ShopifyNotificationLog) GetShopifyNotificationLog(paginateParam *paginate.Param, userId int, trackNumber string) ([]models.ShopifyNotificationLog, int64, error) {
	var result []models.ShopifyNotificationLog
	count := int64(1000)

	data := database.DB.Model(&models.ShopifyNotificationLog{}).
		Order("created_at DESC").
		Scopes(paginate.ORMScope(paginateParam))
	if userId != 0 {
		data.Where("user_id = ?", userId)
	}
	if trackNumber != "" {
		data.Where("track_number = ?", trackNumber)
	}
	data.Find(&result)
	return result, count, data.Error
}
