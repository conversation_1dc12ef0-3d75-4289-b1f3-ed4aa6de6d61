package config

import (
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type mysqlConfig struct {
	Dsn         string
	MaxIdleConn int
	MaxOpenConn int
	MaxLifetime time.Duration
}

type SqlWriter struct {
}

func (m SqlWriter) Printf(format string, v ...interface{}) {
	logstash := fmt.Sprintf(format, v...)
	zap.L().Warn(logstash)
}

// NewDB 根据配置文件配置的名字获取DB句柄
func NewDB(name string) (*gorm.DB, error) {
	dbConfig := mysqlConfig{}
	err := conf.ReadSection(name, &dbConfig)
	// log.Printf("db conf:v%\n", dbConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to read config for %s section: %v", name, err)
	}

	log := SqlWriter{}

	dsn := dbConfig.Dsn
	// 建立mysql连接
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		DisableForeignKeyConstraintWhenMigrating: true,
		Logger: logger.New(log, logger.Config{
			// 设置日志级别，只有Warn以上才会打印sql
			LogLevel: logger.Warn,
		}),
	})

	if err != nil {
		return nil, fmt.Errorf("failed to connect to mysql: %v", err)
	}

	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get sqlDB: %v", err)
	}
	sqlDB.SetMaxIdleConns(dbConfig.MaxIdleConn)
	sqlDB.SetMaxOpenConns(dbConfig.MaxOpenConn)
	sqlDB.SetConnMaxLifetime(dbConfig.MaxLifetime)

	return db, nil
}
