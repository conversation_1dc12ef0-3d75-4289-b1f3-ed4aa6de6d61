package service

import (
	"fmt"
	"math"
	"strconv"
	"time"
	"tmshopify/pkg/utils/paginate"
	"tmshopify/store/repo/impl"
)

const (
	NoComparison int = iota
	PreviousPeriod
	PreviousMonth
	PreviousQuarter
	PreviousYear
	Custom
)

type PaidStatistic struct {
	paidStatisticsRepo *impl.PaidStatistic
}

type PaidConversionRateStatistic struct {
	paidConversionRateStatisticsRepo *impl.PaidConversionRateStatistic
}

type UserStatistic struct {
	userStatisticsRepo *impl.User
}

type DataUserRenewStatistic struct {
	dataUserRenewStatisticRepo *impl.DataUserRenewDetail
}

func (p *PaidStatistic) GetPaidSubscribersChart(startStr string, endStr string, cType int, iCompared int, comparedStartStr string, comparedEndStr string) ([]string, []int, []int) {
	shanghaiZone, _ := time.LoadLocation("Asia/Shanghai")
	startTime, _ := time.ParseInLocation(time.DateTime, startStr+" 00:00:00", shanghaiZone)
	endTime, _ := time.ParseInLocation(time.DateTime, endStr+" 23:59:59", shanghaiZone)

	if cType == 1 {
		//allTime
		minData, _ := p.paidStatisticsRepo.FirstByMinId() //获取有数据的最早时间
		startTime = minData.CreateDate
		endTime = time.Now()
	}

	var (
		diff              = int(endTime.Sub(startTime).Hours() / 24) //相差天数
		comparedStartTime time.Time
		comparedEndTime   time.Time
	)
	switch iCompared {
	case 1: //上个周期
		comparedStartTime = startTime.AddDate(0, 0, -(diff + 1))
		comparedEndTime = endTime.AddDate(0, 0, -(diff + 1))
	case 2: //上个月
		comparedStartTime = startTime.AddDate(0, -1, 0)
		comparedEndTime = endTime.AddDate(0, -1, 0)
	case 3: //上个季度
		comparedStartTime = startTime.AddDate(0, -3, 0)
		comparedEndTime = endTime.AddDate(0, -3, 0)
	case 4: //上一年
		comparedStartTime = startTime.AddDate(-1, 0, 0)
		comparedEndTime = endTime.AddDate(-1, 0, 0)
	case 5: //自定义时间
		comparedStartTime, _ = time.ParseInLocation(time.DateTime, comparedStartStr+" 00:00:00", shanghaiZone)
		comparedEndTime, _ = time.ParseInLocation(time.DateTime, comparedEndStr+" 23:59:59", shanghaiZone)
		comparedDiff := int(comparedEndTime.Sub(comparedStartTime).Hours() / 24) //对比时间的天数差
		if comparedDiff != diff {
			comparedEnd := comparedStartTime.AddDate(0, 0, diff)
			comparedEndTime = time.Date(comparedEnd.Year(), comparedEnd.Month(), comparedEnd.Day(), 23, 59, 59, 0, comparedEnd.Location())
		}
	}
	countData, err := p.paidStatisticsRepo.GetTotalPaid(startTime, endTime)
	var (
		date          []string
		comparedDate  []string
		count         []int
		comparedCount []int
		t             time.Time
	)
	if err == nil {
		for t = startTime; t.Before(endTime); t = t.AddDate(0, 0, 1) {
			date = append(date, fmt.Sprintf("%s %d, %d", t.Month().String()[:3], t.Day(), t.Year()))
			dCount := 0 //每天的数量
			startDate := t.Format(time.DateOnly) + " 00:00:00"
			endDate := t.Format(time.DateOnly) + " 23:59:59" //天的最后一天
			//把时间字符串格式化成相同的时间类型
			start, _ := time.Parse(time.DateTime, startDate)
			end, _ := time.Parse(time.DateTime, endDate)
			for _, data := range countData {
				target := data.Date + " 12:00:00"
				t, _ := time.Parse(time.DateTime, target)
				if start.Before(t) && t.Before(end) {
					dCount += data.Count
				}
			}
			count = append(count, dCount)
		}
		if iCompared != 0 {
			comparedCountData, _ := p.paidStatisticsRepo.GetTotalPaid(comparedStartTime, comparedEndTime)
			for t = comparedStartTime; t.Before(comparedEndTime); t = t.AddDate(0, 0, 1) {
				comparedDate = append(comparedDate, fmt.Sprintf("%s %d, %d", t.Month().String()[:3], t.Day(), t.Year()))
				dComparedCount := 0 //对比值
				startDate := t.Format(time.DateOnly) + " 00:00:00"
				endDate := t.Format(time.DateOnly) + " 23:59:59" //天的最后一天
				//把时间字符串格式化成相同的时间类型
				start, _ := time.Parse(time.DateTime, startDate)
				end, _ := time.Parse(time.DateTime, endDate)
				for _, data := range comparedCountData {
					target := data.Date + " 12:00:00"
					t, _ := time.Parse(time.DateTime, target)
					if start.Before(t) && t.Before(end) {
						dComparedCount += data.Count
					}
				}
				comparedCount = append(comparedCount, dComparedCount)
			}
		}

		//有对比需要处理横坐标出现两个时间
		if comparedDate != nil {
			for i := 0; i < len(date); i++ {
				date[i] = date[i] + "_" + comparedDate[i]
			}
		}
	}
	return date, count, comparedCount
}

func (p *PaidStatistic) GetPaidData(startStr string, endStr string, cType int, iCompared int, comparedStartStr string, comparedEndStr string, pType int) ([]string, []float64, []float64) {
	shanghaiZone, _ := time.LoadLocation("Asia/Shanghai")
	startTime, _ := time.ParseInLocation(time.DateTime, startStr+" 00:00:00", shanghaiZone)
	endTime, _ := time.ParseInLocation(time.DateTime, endStr+" 23:59:59", shanghaiZone)

	if cType == 1 {
		//allTime
		minData, _ := p.paidStatisticsRepo.FirstByMinId() //获取有数据的最早时间
		startTime = minData.CreateDate
		endTime = time.Now()
	}
	var (
		diff              = int(endTime.Sub(startTime).Hours() / 24) //相差天数
		comparedStartTime time.Time
		comparedEndTime   time.Time
	)
	switch iCompared {
	case 1: //上个周期
		comparedStartTime = startTime.AddDate(0, 0, -(diff + 1))
		comparedEndTime = endTime.AddDate(0, 0, -(diff + 1))
	case 2: //上个月
		comparedStartTime = startTime.AddDate(0, -1, 0)
		comparedEndTime = endTime.AddDate(0, -1, 0)
	case 3: //上个季度
		comparedStartTime = startTime.AddDate(0, -3, 0)
		comparedEndTime = endTime.AddDate(0, -3, 0)
	case 4: //上一年
		comparedStartTime = startTime.AddDate(-1, 0, 0)
		comparedEndTime = endTime.AddDate(-1, 0, 0)
	case 5: //自定义时间
		comparedStartTime, _ = time.ParseInLocation(time.DateTime, comparedStartStr+" 00:00:00", shanghaiZone)
		comparedEndTime, _ = time.ParseInLocation(time.DateTime, comparedEndStr+" 23:59:59", shanghaiZone)
		comparedDiff := int(comparedEndTime.Sub(comparedStartTime).Hours() / 24) //对比时间的天数差
		if comparedDiff != diff {
			comparedEnd := comparedStartTime.AddDate(0, 0, diff)
			comparedEndTime = time.Date(comparedEnd.Year(), comparedEnd.Month(), comparedEnd.Day(), 23, 59, 59, 0, comparedEnd.Location())
		}
	}
	priceData, err := p.paidStatisticsRepo.GetTotalPrice(startTime, endTime)
	var (
		date          []string
		comparedDate  []string
		price         []float64
		comparedPrice []float64
		t             time.Time
	)
	if err == nil {
		for t = startTime; t.Before(endTime); t = t.AddDate(0, 0, 1) {
			var dPrice float64 //每天的数量
			date = append(date, fmt.Sprintf("%s %d, %d", t.Month().String()[:3], t.Day(), t.Year()))
			startDate := t.Format(time.DateOnly) + " 00:00:00"
			endDate := t.Format(time.DateOnly) + " 23:59:59" //天的最后一天
			//把时间字符串格式化成相同的时间类型
			start, _ := time.Parse(time.DateTime, startDate)
			end, _ := time.Parse(time.DateTime, endDate)
			for _, data := range priceData {
				target := data.Date + " 12:00:00"
				t, _ := time.Parse(time.DateTime, target)
				if start.Before(t) && t.Before(end) {
					if pType == 1 {
						//总金额
						dPrice += data.Price
					} else {
						//客单价
						if data.Total != 0 {
							dPrice += math.Round(data.Price/float64(data.Total)*100) / 100
						}
					}
				}
			}
			price = append(price, math.Round(dPrice*100)/100)
		}
		if iCompared != 0 {
			comparedPriceData, _ := p.paidStatisticsRepo.GetTotalPrice(comparedStartTime, comparedEndTime)
			for t = comparedStartTime; t.Before(comparedEndTime); t = t.AddDate(0, 0, 1) {
				var dComparedPrice float64 //对比值
				comparedDate = append(comparedDate, fmt.Sprintf("%s %d, %d", t.Month().String()[:3], t.Day(), t.Year()))
				startDate := t.Format(time.DateOnly) + " 00:00:00"
				endDate := t.Format(time.DateOnly) + " 23:59:59" //天的最后一天
				//把时间字符串格式化成相同的时间类型
				start, _ := time.Parse(time.DateTime, startDate)
				end, _ := time.Parse(time.DateTime, endDate)
				for _, data := range comparedPriceData {
					target := data.Date + " 12:00:00"
					t, _ := time.Parse(time.DateTime, target)
					if start.Before(t) && t.Before(end) {
						if pType == 1 {
							//总金额
							dComparedPrice += data.Price
						} else {
							//客单价
							if data.Total != 0 {
								dComparedPrice += math.Round(data.Price/float64(data.Total)*100) / 100
							}
						}
					}
				}
				comparedPrice = append(comparedPrice, math.Round(dComparedPrice*100)/100)
			}
		}

		//有对比需要处理横坐标出现两个时间
		if comparedDate != nil {
			for i := 0; i < len(date); i++ {
				date[i] = date[i] + "_" + comparedDate[i]
			}
		}
	}
	return date, price, comparedPrice
}

func (p *PaidStatistic) GetSubscriberUserData(startStr string, endStr string, cType int) ([]string, []int, []int, []int, int) {
	shanghaiZone, _ := time.LoadLocation("Asia/Shanghai")
	startTime, _ := time.ParseInLocation(time.DateTime, startStr+" 00:00:00", shanghaiZone)
	endTime, _ := time.ParseInLocation(time.DateTime, endStr+" 23:59:59", shanghaiZone)

	if cType == 1 {
		//allTime
		minData, _ := p.paidStatisticsRepo.FirstByMinId() //获取有数据的最早时间
		startTime = minData.CreateDate
		endTime = time.Now()
	}
	var (
		date         []string
		newPaid      []int
		resume       []int
		cancel       []int
		totalNewPaid int
		totalResume  int
		totalCancel  int
		t            time.Time
	)
	countData, err := p.paidStatisticsRepo.GetSubscriberUserTotalUser(startTime, endTime)
	if err == nil {
		for t = startTime; t.Before(endTime); t = t.AddDate(0, 0, 1) {
			date = append(date, fmt.Sprintf("%s %d, %d", t.Month().String()[:3], t.Day(), t.Year()))
			dNewPaid := 0 //新增付费用户数
			dResume := 0  //恢复付费用户数
			dCancel := 0  //取消付费用户数
			startDate := t.Format(time.DateOnly) + " 00:00:00"
			endDate := t.Format(time.DateOnly) + " 23:59:59" //天的最后一天
			//把时间字符串格式化成相同的时间类型
			start, _ := time.Parse(time.DateTime, startDate)
			end, _ := time.Parse(time.DateTime, endDate)
			for _, data := range countData {
				target := data.Date + " 12:00:00"
				t, _ := time.Parse(time.DateTime, target)
				if start.Before(t) && t.Before(end) {
					dNewPaid += data.NewPaid
					dResume += data.Resume
					dCancel += data.Cancel
				}
			}
			newPaid = append(newPaid, dNewPaid)
			resume = append(resume, dResume)
			cancel = append(cancel, dCancel)
			totalNewPaid += dNewPaid //选择周期的新增付费用户数总和
			totalResume += dResume   //选择周期的恢复付费用户数总和
			totalCancel += dCancel   //选择周期的取消付费用户数总和
		}

	}
	totalIncrease := totalNewPaid + totalResume - totalCancel
	return date, newPaid, resume, cancel, totalIncrease
}

func (p *PaidConversionRateStatistic) GetSubscriberConversionRateData() ([]string, []float64) {
	endTime := time.Now()
	startTime := endTime.AddDate(0, -12, 0) //获取12个月前时间
	countData, err := p.paidConversionRateStatisticsRepo.GetSubscriberConversionRate(startTime, endTime)
	var (
		date []string
		rate []float64
		t    time.Time
	)
	if err == nil {
		for t = startTime; t.Before(endTime); t = t.AddDate(0, 1, 0) {
			date = append(date, fmt.Sprintf("%s, %d", t.Month().String()[:3], t.Year()))
			var dRate float64
			endDate := (t.AddDate(0, 1, -t.Day())).Format(time.DateOnly) + " 23:59:59" //月的最后一天
			start := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, time.UTC)
			end, _ := time.Parse(time.DateTime, endDate)
			for _, data := range countData {
				target := data.Date + " 12:00:00"
				t, _ := time.Parse(time.DateTime, target)
				if start.Before(t) && t.Before(end) || t == start || t == end {
					if data.MonthNewInstall != 0 {
						dRate += float64(data.MonthNewPaid) / float64(data.MonthNewInstall)
					}
				}
			}
			fRate, _ := strconv.ParseFloat(fmt.Sprintf("%.4f", dRate), 64)
			rate = append(rate, fRate)
		}
	}
	return date, rate
}

func NewPaidStatistic() *PaidStatistic {
	return &PaidStatistic{
		paidStatisticsRepo: &impl.PaidStatistic{},
	}
}

func NewPaidConversionRateStatistic() *PaidConversionRateStatistic {
	return &PaidConversionRateStatistic{
		paidConversionRateStatisticsRepo: &impl.PaidConversionRateStatistic{},
	}
}

func NewUserStatistic() *UserStatistic {
	return &UserStatistic{
		userStatisticsRepo: &impl.User{},
	}
}

func NewDataUserRenewStatistic() *DataUserRenewStatistic {
	return &DataUserRenewStatistic{
		dataUserRenewStatisticRepo: &impl.DataUserRenewDetail{},
	}
}

func (p *PaidStatistic) GetMerchantsData(startStr string, endStr string, cType int, iCompared int, comparedStartStr string, comparedEndStr string) ([]string, []int, []int) {
	shanghaiZone, _ := time.LoadLocation("Asia/Shanghai")
	startTime, _ := time.ParseInLocation(time.DateTime, startStr+" 00:00:00", shanghaiZone)
	endTime, _ := time.ParseInLocation(time.DateTime, endStr+" 23:59:59", shanghaiZone)

	if cType == 1 {
		//allTime
		minData, _ := p.paidStatisticsRepo.FirstByMinId() //获取有数据的最早时间
		startTime = minData.CreateDate
		endTime = time.Now()
	}
	var (
		diff              = int(endTime.Sub(startTime).Hours() / 24) //相差天数
		date              []string
		comparedDate      []string
		count             []int
		comparedCount     []int
		t                 time.Time
		comparedStartTime time.Time
		comparedEndTime   time.Time
	)

	switch iCompared {
	case PreviousPeriod: //上个周期
		comparedStartTime = startTime.AddDate(0, 0, -(diff + 1))
		comparedEndTime = endTime.AddDate(0, 0, -(diff + 1))
	case PreviousMonth: //上个月
		comparedStartTime = startTime.AddDate(0, -1, 0)
		comparedEndTime = endTime.AddDate(0, -1, 0)
	case PreviousQuarter: //上个季度
		comparedStartTime = startTime.AddDate(0, -3, 0)
		comparedEndTime = endTime.AddDate(0, -3, 0)
	case PreviousYear: //上一年
		comparedStartTime = startTime.AddDate(-1, 0, 0)
		comparedEndTime = endTime.AddDate(-1, 0, 0)
	case Custom: //自定义时间
		comparedStartTime, _ = time.ParseInLocation(time.DateTime, comparedStartStr+" 00:00:00", shanghaiZone)
		comparedEndTime, _ = time.ParseInLocation(time.DateTime, comparedEndStr+" 23:59:59", shanghaiZone)
		comparedDiff := int(comparedEndTime.Sub(comparedStartTime).Hours() / 24) //对比时间的天数差
		if comparedDiff != diff {
			comparedEnd := comparedStartTime.AddDate(0, 0, diff)
			comparedEndTime = time.Date(comparedEnd.Year(), comparedEnd.Month(), comparedEnd.Day(), 23, 59, 59, 0, comparedEnd.Location())
		}
	}

	countData, err := p.paidStatisticsRepo.GetTotalUser(startTime, endTime)
	if err == nil {
		for t = startTime; t.Before(endTime); t = t.AddDate(0, 0, 1) {
			date = append(date, fmt.Sprintf("%s %d, %d", t.Month().String()[:3], t.Day(), t.Year()))
			//每天的用户量
			dCount := 0
			startDate := t.Format(time.DateOnly) + " 00:00:00"
			endDate := t.Format(time.DateOnly) + " 23:59:59" //天的最后一天
			//把时间字符串格式化成相同的时间类型
			start, _ := time.Parse(time.DateTime, startDate)
			end, _ := time.Parse(time.DateTime, endDate)
			for _, data := range countData {
				target := data.Date + " 12:00:00"
				t, _ := time.Parse(time.DateTime, target)
				if start.Before(t) && t.Before(end) {
					dCount += data.Install
				}
			}
			count = append(count, dCount)
		}

		if iCompared != NoComparison {
			comparedCountData, _ := p.paidStatisticsRepo.GetTotalUser(comparedStartTime, comparedEndTime)
			for t = comparedStartTime; t.Before(comparedEndTime); t = t.AddDate(0, 0, 1) {
				comparedDate = append(comparedDate, fmt.Sprintf("%s %d, %d", t.Month().String()[:3], t.Day(), t.Year()))
				//每天的用户量
				dCount := 0
				startDate := t.Format(time.DateOnly) + " 00:00:00"
				endDate := t.Format(time.DateOnly) + " 23:59:59" //天的最后一天
				//把时间字符串格式化成相同的时间类型
				start, _ := time.Parse(time.DateTime, startDate)
				end, _ := time.Parse(time.DateTime, endDate)
				for _, data := range comparedCountData {
					target := data.Date + " 12:00:00"
					t, _ := time.Parse(time.DateTime, target)
					if start.Before(t) && t.Before(end) {
						dCount += data.Install
					}
				}
				comparedCount = append(comparedCount, dCount)
			}
		}

	}
	//有对比需要处理横坐标出现两个时间
	if comparedDate != nil {
		for i := 0; i < len(date); i++ {
			date[i] = date[i] + "_" + comparedDate[i]
		}
	}
	return date, count, comparedCount
}

func (p *PaidStatistic) GetUserGrowthData(startStr string, endStr string, cType int) ([]string, []int, []int, []int, int) {
	shanghaiZone, _ := time.LoadLocation("Asia/Shanghai")
	startTime, _ := time.ParseInLocation(time.DateTime, startStr+" 00:00:00", shanghaiZone)
	endTime, _ := time.ParseInLocation(time.DateTime, endStr+" 23:59:59", shanghaiZone)

	if cType == 1 {
		//allTime
		minData, _ := p.paidStatisticsRepo.FirstByMinId() //获取有数据的最早时间
		startTime = minData.CreateDate
		endTime = time.Now()
	}
	var (
		date       []string
		count      []int
		install    []int
		unInstall  []int
		totalCount int
		t          time.Time
	)

	countData, err := p.paidStatisticsRepo.GetUserGrowthData(startTime, endTime)
	if err == nil {
		for t = startTime; t.Before(endTime); t = t.AddDate(0, 0, 1) {
			date = append(date, fmt.Sprintf("%s %d, %d", t.Month().String()[:3], t.Day(), t.Year()))
			dCount := 0
			newCount := 0   //每天的新增
			dInstall := 0   //每天的安装数
			dUnInstall := 0 //每天的卸载数
			startDate := t.Format(time.DateOnly) + " 00:00:00"
			endDate := t.Format(time.DateOnly) + " 23:59:59" //天的最后一天
			//把时间字符串格式化成相同的时间类型
			start, _ := time.Parse(time.DateTime, startDate)
			end, _ := time.Parse(time.DateTime, endDate)
			for _, data := range countData {
				target := data.Date + " 12:00:00"
				t, _ := time.Parse(time.DateTime, target)
				if start.Before(t) && t.Before(end) {
					newCount = data.Install - data.UnInstall
					dCount += newCount
					dInstall += data.Install
					dUnInstall += data.UnInstall
				}
			}
			count = append(count, dCount)
			install = append(install, dInstall)
			unInstall = append(unInstall, dUnInstall)
			totalCount += dCount //选择周期的净增用户数总和
		}
	}
	return date, count, install, unInstall, totalCount
}

// CountryUsersDistribution 获取国家地区用户分布图表数据
func (u *UserStatistic) CountryUsersDistribution(startStr string, endStr string, cType int, param *paginate.Param) ([]struct {
	Key                      int
	Country                  string
	CountryPayUserNum        int
	CountryUserNum           int
	CountryPriceTotal        float32
	CountryUserProportion    float32
	CountryPriceProportion   float32
	CountryPayUserProportion float32
}, int64) {
	shanghaiZone, _ := time.LoadLocation("Asia/Shanghai")
	startTime, _ := time.ParseInLocation(time.DateTime, startStr+" 00:00:00", shanghaiZone)
	endTime, _ := time.ParseInLocation(time.DateTime, endStr+" 23:59:59", shanghaiZone)

	if cType == 1 {
		minData, _ := u.userStatisticsRepo.FirstByMinId() //获取有数据的最早时间
		startTime = minData.CreatedAt
		endTime = time.Now()
	}
	countryUsersData, countryNum, userNum, _ := u.userStatisticsRepo.CountryUsersDistribution(param, startTime, endTime)
	//付费总数据获取
	payUsersData, _ := u.userStatisticsRepo.PayUsersData(startTime, endTime)
	for k, value := range countryUsersData {
		countryUsersData[k].Key += (param.Page-1)*param.PageSize + k + 1
		if float32(userNum) > 0 {
			countryUsersData[k].CountryUserProportion = float32(value.CountryUserNum) / float32(userNum) * 100
		}
		if payUsersData.PriceTotal > 0 {
			countryUsersData[k].CountryPriceProportion = value.CountryPriceTotal / payUsersData.PriceTotal * 100
		}
		if float32(payUsersData.PayUserNum) > 0 {
			countryUsersData[k].CountryPayUserProportion = float32(value.CountryPayUserNum) / float32(payUsersData.PayUserNum) * 100
		}
	}
	return countryUsersData, countryNum
}

func (p *DataUserRenewStatistic) GetUserRenewSubscriberData(startTime time.Time) []string {
	var (
		t          time.Time
		arr        []string
		startIds   []int
		startValue int64
	)
	currentTime := time.Now()
	endTime := time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, currentTime.Location()).Add(-time.Second) // 结束时间
	arr = append(arr, fmt.Sprintf("%s %d", startTime.Month().String()[:3], startTime.Year()))
	startDate := startTime.Format(time.DateOnly) + " 00:00:00"
	endDate := (startTime.AddDate(0, 1, -startTime.Day())).Format(time.DateOnly) + " 23:59:59" //第一月的最后一天
	startIds, _ = p.dataUserRenewStatisticRepo.GetUserPaidIds(startDate, endDate)              //获取初始值的ID集合
	startValue = int64(len(startIds))
	arr = append(arr, strconv.FormatInt(startValue, 10))

	//获取接下来12个月的数据
	startTime = startTime.AddDate(0, 1, 0)
	renewData, err := p.dataUserRenewStatisticRepo.GetUserRenewCount(startTime, endTime, startIds)
	result := make(map[string]int64)
	if err == nil {
		for _, value := range renewData {
			if value != nil {
				date := value["date"].(string)
				count := value["count"].(int64)
				result[date] = count
			}
		}
	}
	for t = startTime; t.Before(endTime); t = t.AddDate(0, 1, 0) {
		var num int
		endDate = (t.AddDate(0, 1, -t.Day())).Format(time.DateOnly) //月的最后一天
		for date, count := range result {
			if endDate == date {
				arr = append(arr, strconv.FormatFloat(math.Round(float64(count)/float64(startValue)*100), 'f', -1, 64))
				num = 1
				break
			}
		}
		if num == 0 {
			arr = append(arr, "0")
		}
	}
	return arr
}
