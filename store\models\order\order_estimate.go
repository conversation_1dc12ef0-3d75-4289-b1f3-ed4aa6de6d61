package order

import (
	"database/sql"
	"time"
)

type Estimate struct {
	FulfillmentId          string       `gorm:"column:fulfillment_id;type:char(20);" json:"fulfillment_id"`
	TrackNumber            string       `gorm:"column:track_number;type:varchar(50);" json:"track_number"`
	UserId                 int32        `gorm:"column:user_id;type:int;" json:"user_id"`
	CarrierCode            string       `gorm:"column:carrier_code;type:varchar(50);" json:"carrier_code"`
	OriginCountry          string       `gorm:"column:origin_country;type:varchar(10);" json:"origin_country"`
	OriginState            string       `gorm:"column:origin_state;type:varchar(50);" json:"origin_state"`
	OriginCity             string       `gorm:"column:origin_city;type:varchar(50);" json:"origin_city"`
	OriginPostalCode       string       `gorm:"column:origin_postal_code;type:varchar(30);" json:"origin_postal_code"`
	DestinationCountry     string       `gorm:"column:destination_country;type:varchar(10);" json:"destination_country"`
	DestinationState       string       `gorm:"column:destination_state;type:varchar(50);" json:"destination_state"`
	DestinationCity        string       `gorm:"column:destination_city;type:varchar(50);" json:"destination_city"`
	DestinationPostalCode  string       `gorm:"column:destination_postal_code;type:varchar(30);" json:"destination_postal_code"`
	Weight                 float64      `gorm:"column:weight;type:decimal(10, 2);comment:重量（单位kg）;" json:"weight"`            // 重量（单位kg）
	ServiceCode            string       `gorm:"column:service_code;type:varchar(50);comment:物流商具体线路或服务;" json:"service_code"` // 物流商具体线路或服务
	PackageCount           int32        `gorm:"column:package_count;type:int;comment:包裹总数;" json:"package_count"`             // 包裹总数
	PickupTime             sql.NullTime `gorm:"column:pickup_time;type:timestamp;comment:物流商揽收时间;" json:"pickup_time"`        // 物流商揽收时间
	LastTime               sql.NullTime `gorm:"column:last_time;type:timestamp;comment:用作对比数据;" json:"last_time"`             // 用作对比数据
	OriginRawLocation      string       `gorm:"column:origin_raw_location;type:text;comment:发件地address1,2;" json:"origin_raw_location"`
	DestinationRawLocation string       `gorm:"column:destination_raw_location;type:text;comment:收件地address1,2;" json:"destination_raw_location"`

	ID        string    `gorm:"primaryKey;" json:"id,omitempty" `
	CreatedAt time.Time ` json:"created_at,omitempty" gorm:"autoCreateTime" `
	UpdatedAt time.Time ` json:"updated_at,omitempty" gorm:"autoUpdateTime" `
}

func (Estimate) TableName() string {
	return "order_estimates"
}
