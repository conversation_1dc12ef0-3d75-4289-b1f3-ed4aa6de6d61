package entity

import (
	"encoding/json"
	"strings"

	"tmshopify/config"
	"tmshopify/store/models"
)

type TrackSeo struct {
	SeoTitle string `json:"seo_title"`
	SeoDesc  string `json:"seo_desc"`
}

type ShippingTime struct {
	CalType    int  `json:"cal_type"`
	Switch     bool `json:"switch"`
	DateFormat int  `json:"date_format"`
	TimeFormat int  `json:"time_format"`
}

type HideSetting struct {
	HideType    int         `json:"hide_type"`
	ReplaceType int         `json:"replace_type"`
	Origin      interface{} `json:"origin"`
	Replace     string      `json:"replace"`
	Sort        int         `json:"sort"`
}

type TrackSettingEntityStruct struct {
	TrackLink     []string        `json:"track_link"`
	UserId        int             `json:"user_id"`
	HideSetting   []HideSetting   `json:"hide_setting"`
	TrackUrl      string          `json:"track_url"`
	ApiTrackUrl   string          `json:"api_track_url"`
	ApiUrl        string          `json:"api_url"`
	TrackSeo      TrackSeo        `json:"track_seo"`
	ShippingTime  config.Estimate `json:"shipping_time"`
	ShowCopyRight bool            `json:"show_copy_right"`
}

const (
	EstimateCalOrder int = iota
	EstimateCalFulfill
)

func HandleSetting(userSetting models.TrackPageSettings) TrackSettingEntityStruct {
	trackLink := strings.TrimSpace(userSetting.TrackLink)
	if trackLink == "" {
		trackLink = "apps/trackingmore"
	}
	trackLinkArr := strings.Split(trackLink, "/")
	trackUrl := "/" + strings.Join(trackLinkArr, "/")
	apiUrl := "https://" + config.Get().ServerDomain + "/api/v2/track-page/recommend-product"
	apiTrackUrl := "https://" + config.Get().ServerDomain + "/api/v2/track-page/back-data"
	var eddSetting config.Estimate
	_ = json.Unmarshal([]byte(userSetting.EstimatedDeliveryTime), &eddSetting)
	trackSeo := TrackSeo{
		userSetting.SeoTitle,
		userSetting.SeoDesc,
	}
	var hideSetting []HideSetting
	_ = json.Unmarshal([]byte(userSetting.HideSetting), &hideSetting)

	showCopyRight := userSetting.ShowCopyRight == 1
	return TrackSettingEntityStruct{
		TrackLink:     trackLinkArr,
		UserId:        userSetting.UserId,
		HideSetting:   hideSetting,
		TrackUrl:      trackUrl,
		ApiTrackUrl:   apiTrackUrl,
		ApiUrl:        apiUrl,
		TrackSeo:      trackSeo,
		ShippingTime:  eddSetting,
		ShowCopyRight: showCopyRight,
	}
}
