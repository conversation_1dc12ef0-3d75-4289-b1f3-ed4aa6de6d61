package config

var OrderStatusSetting = map[string]map[string]interface{}{
	// 0关1开
	"card": {
		"extra_contents": map[string]int{
			"estimated_delivery_time": 1,
			"tracking_number":         1,
		},
		"track_button_color": "#008060",
		"status": map[string]string{
			"ordered":             "Ordered",
			"fulfilled":           "Order ready",
			"has_shipping_update": "Shipped",
		},
		"description": map[string]string{
			"ordered":             "Thank you for your purchase! Track your order to find more details.",
			"fulfilled":           "Your order is ready. Track your order to find more details.",
			"has_shipping_update": "Your order is shipped. Track your order to find more details.",
		},
		"others": map[string]string{
			"estimated_delivery_time": "Estimated delivery time",
			"track_your_order":        "Track your order",
			"tracking_number":         "Tracking number",
		},
	},
	"link": {
		"track_link_text":  "Click here to get shipping updates",
		"track_link_color": "#1878b9",
	},
	"widget": {
		"extra_contents": map[string]int{
			"estimated_delivery_time": 1,
			"tracking_number":         1,
		},
		"status": map[string]string{
			"ordered":             "Ordered",
			"fulfilled":           "Order ready",
			"has_shipping_update": "Shipped",
		},
		"description": map[string]string{
			"ordered":             "Thank you for your purchase! Track your order to find more details.",
			"fulfilled":           "Your order is ready. Track your order to find more details.",
			"has_shipping_update": "Your order is shipped. Track your order to find more details.",
		},
		"others": map[string]string{
			"estimated_delivery_time": "Estimated delivery time",
			"track_your_order":        "Track your order",
			"tracking_number":         "Tracking number",
		},
	},
}
