package api

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"

	"tmshopify/internal/admin/request"
	"tmshopify/internal/admin/service"
	"tmshopify/pkg/domain"
	"tmshopify/pkg/utils/paginate"
	"tmshopify/server/response"
	"tmshopify/store/models"
)

type User struct{}

func (u *User) ChargePlans(ctx *gin.Context) {
	userId := ctx.Query("user_id")
	isDelete := ctx.Query("is_delete")
	customizePlans := service.NewUserService().GetChargePlans(userId, isDelete)
	response.Success(ctx, &response.SuccessResponse{
		Data: customizePlans,
	})
}

func (u *User) CreateChargePlan(ctx *gin.Context) {
	var createPlan models.ChargePlans
	err := ctx.ShouldBind(&createPlan)
	if err != nil {
		response.UnprocessableEntity(ctx, err)
		return
	}
	var validate = validator.New()
	validate.RegisterStructValidationMapRules(request.ChargePlanCreate, models.ChargePlans{})
	if err := validate.Struct(createPlan); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}
	res := service.NewUserService().CreatePlan(createPlan)
	if res != nil {
		response.BadRequest(ctx, res.Error())
		return
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: res,
	})
}
func (u *User) SearchUser(ctx *gin.Context) {
	keyword := ctx.Query("keyword")
	data := service.NewUserService().SearchUser(keyword)
	response.Success(ctx, &response.SuccessResponse{
		Data: data,
	})
}

func (u *User) UpdateChargePlan(ctx *gin.Context) {
	var updatePlan models.ChargePlans
	if err := ctx.ShouldBind(&updatePlan); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	if updatePlan.ID <= 0 {
		response.BadRequest(ctx, "Error params")
		return
	}

	var validate = validator.New()
	validate.RegisterStructValidationMapRules(request.ChargePlanUpdate, models.ChargePlans{})
	if err := validate.Struct(updatePlan); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	res := service.NewUserService().UpdatePlan(updatePlan)
	if res != nil {
		response.BadRequest(ctx, res.Error())
		return
	}
	response.Success(ctx, &response.SuccessResponse{
		Message: "Update success",
	})
}
func (u *User) ChangePlanStatus(ctx *gin.Context) {
	planType := models.PlanType(ctx.Param("plan_type"))
	id := ctx.Param("id")
	if id == "" {
		response.BadRequest(ctx, "Error params")
		return
	}
	res := service.NewUserService().ChangePlanStatus(id, ctx.Param("status"), ctx.Param("user_id"), planType)
	if res != nil {
		response.BadRequest(ctx, res.Error())
		return
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: res,
	})
}

func (u *User) VersionList(ctx *gin.Context) {
	var versionList []struct {
		Label string `json:"label"`
		Value string `json:"value"`
	}

	for _, version := range domain.AllVersions {
		versionList = append(versionList, struct {
			Label string `json:"label"`
			Value string `json:"value"`
		}{Label: string(version), Value: string(version)})
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: versionList,
	})
}

func (u *User) GetSubscriptionList(ctx *gin.Context) {
	userId := strings.TrimSpace(ctx.Query("userId"))
	status := strings.TrimSpace(ctx.Query("status"))
	var param paginate.Param
	page := ctx.Query("page")
	pageSize := ctx.Query("pageSize")
	param.Page, _ = strconv.Atoi(page)
	param.PageSize, _ = strconv.Atoi(pageSize)
	if param.Page <= 0 {
		param.Page = 1
	}

	if param.PageSize <= 0 {
		param.PageSize = 15
	}
	data := service.NewUserService().GetSubscriptionList(userId, status, param)
	response.Success(ctx, &response.SuccessResponse{
		Data: gin.H{
			"items":    data,
			"page":     param.Page,
			"pageSize": param.PageSize,
			"total":    1000,
		},
	})
}

func (u *User) ChangeSubscriptionExtraPrice(ctx *gin.Context) {
	var updateData struct {
		UserId     int     `json:"userId"`
		ExtraPrice float32 `json:"extraPrice"`
	}
	if err := ctx.ShouldBind(&updateData); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	var validate = validator.New()
	validate.RegisterStructValidationMapRules(request.ChangeSubscriptionExtraPrice, models.DataSubscriptionDetails{})
	if err := validate.Struct(updateData); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	if updateData.ExtraPrice <= 0 {
		response.UnprocessableEntity(ctx, "Extra price must be greater than 0")
		return
	}
	err := service.NewUserService().ChangeSubscriptionExtraPrice(updateData.UserId, updateData.ExtraPrice)
	if err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: true,
	})
}
