package api

import (
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"github.com/dromara/carbon/v2"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"

	"tmshopify/internal/admin/request"
	"tmshopify/internal/admin/service"
	"tmshopify/pkg/utils/ginparam"
	"tmshopify/pkg/utils/paginate"
	"tmshopify/server/response"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type Integration struct {
}

func (i *Integration) AppList(ctx *gin.Context) {
	var searchParam impl.IntegrationSearchParams
	_ = ctx.ShouldBind(&searchParam)

	result, err := service.NewIntegration().AppQuery(&searchParam)
	if err != nil {
		response.Fail(ctx, err)
		return
	}

	response.Success(ctx, result)
}

func (i *Integration) CategoryList(ctx *gin.Context) {
	result, err := service.NewIntegration().GetCategoryList()
	if err != nil {
		response.Fail(ctx, err)
		return
	}
	response.Success(ctx, result)
}

func (i *Integration) CreateApp(c *gin.Context) {
	var m models.IntegrationApps
	if err := c.ShouldBindJSON(&m); err != nil {
		response.UnprocessableEntity(c, err.Error())
		return
	}
	var validate = validator.New()
	validate.RegisterStructValidationMapRules(request.CreateIntegrationApp, models.IntegrationApps{})
	if err := validate.Struct(m); err != nil {
		response.UnprocessableEntity(c, err.Error())
		return
	}
	settingLink := m.SettingLink
	if len(settingLink) > 0 {
		isUniqueLink := service.NewIntegration().IsUniqueAppSettingLink(settingLink, 0)
		if !isUniqueLink {
			response.BadRequest(c, "设置链接已经存在请联系研发调整")
			return
		}
	}
	err := service.NewIntegration().StoreApp(m)
	if err != nil {
		response.Fail(c, err)
		return
	}
	response.Created(c)
}

func (i *Integration) UpdateApp(c *gin.Context) {
	var appData models.IntegrationApps
	if err := c.ShouldBind(&appData); err != nil {
		response.UnprocessableEntity(c, err.Error())
		return
	}
	var validate = validator.New()

	validate.RegisterStructValidationMapRules(request.UpdateIntegrationApp, models.IntegrationApps{})
	logrus.Warn("test step 0")

	if err := validate.Struct(appData); err != nil {
		response.UnprocessableEntity(c, err.Error())
		return
	}
	settingLink := appData.SettingLink
	if len(settingLink) > 0 {
		isUniqueLink := service.NewIntegration().IsUniqueAppSettingLink(settingLink, appData.ID)
		if !isUniqueLink {
			response.BadRequest(c, "设置链接已经存在请联系研发调整")
			return
		}
	}
	logrus.Warn("test step 2")
	err := service.NewIntegration().Update(&appData)
	if err != nil {
		response.Fail(c, err)
		return
	}

	response.NotContent(c)
}

func (i *Integration) DeleteApp(c *gin.Context) {
	ID := c.Param("id")
	_, err := service.NewIntegration().DeleteApp(ID)
	if err != nil {
		response.InternalServerError(c, err.Error())
		return
	}
	response.Success(c, &response.SuccessResponse{
		Data: ID,
	})
}

func (i *Integration) ChangeAppStatus(c *gin.Context) {
	status, _ := strconv.Atoi(c.Param("status"))
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil || id <= 0 {
		response.BadRequest(c, "Request params is error")
		return
	}
	if status < 0 || status > 2 {
		response.BadRequest(c, "Request params is error")
		return
	}

	if err := service.NewIntegration().ChangeAppStatus(strconv.Itoa(id), status); err != nil {
		response.InternalServerError(c, err.Error())
		return
	}
	response.Success(c, &response.SuccessResponse{
		Data: id,
	})
}

func (i *Integration) UpdateAppSort(c *gin.Context) {
	sort, _ := strconv.Atoi(c.Param("sort"))
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil || id <= 0 {
		response.BadRequest(c, "Request params is error")
		return
	}
	if sort < 0 {
		response.BadRequest(c, "Request params is error")
		return
	}

	if err := service.NewIntegration().UpdateAppSort(strconv.Itoa(id), sort); err != nil {
		response.InternalServerError(c, err.Error())
		return
	}
	response.Success(c, &response.SuccessResponse{
		Data: id,
	})
}
func (i *Integration) UpdateAdSort(c *gin.Context) {
	sort, _ := strconv.Atoi(c.Param("sort"))
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil || id <= 0 {
		response.BadRequest(c, "Request params is error")
		return
	}
	if sort < 0 {
		response.BadRequest(c, "Request params is error")
		return
	}

	if err := service.NewIntegration().UpdateAdSort(strconv.Itoa(id), sort); err != nil {
		response.InternalServerError(c, err.Error())
		return
	}
	response.Success(c, &response.SuccessResponse{
		Data: id,
	})
}

func (i *Integration) CreateCategory(c *gin.Context) {
	input, _ := ginparam.RequestInputs(c)
	name := input["name"].(string)
	if len(name) <= 0 {
		response.BadRequest(c, "Request params is error")
		return
	}
	isUnique := service.NewIntegration().IsUniqueCategoryName(name, "")
	if !isUnique {
		response.BadRequest(c, "该分类名称已存在")
		return
	}
	err := service.NewIntegration().CreateCategory(name)
	if err != nil {
		response.Fail(c, err)
		return
	}

	response.Created(c)
}

func (i *Integration) UpdateCategory(c *gin.Context) {
	input, _ := ginparam.RequestInputs(c)
	name := input["name"].(string)
	if len(name) <= 0 {
		response.BadRequest(c, "Request params is error")
		return
	}
	id := fmt.Sprintf("%v", input["id"])
	isUnique := service.NewIntegration().IsUniqueCategoryName(name, id)
	if !isUnique {
		response.BadRequest(c, "该分类名称已存在")
		return
	}
	err := service.NewIntegration().UpdateCategory(id, name)
	if err != nil {
		response.Fail(c, err)
		return
	}

	response.NotContent(c)
}

func (i *Integration) DeleteCategory(c *gin.Context) {
	ID := c.Param("id")
	_, err := service.NewIntegration().DeleteCategory(ID)
	if err != nil {
		response.InternalServerError(c, err.Error())
		return
	}
	response.Success(c, &response.SuccessResponse{
		Data: ID,
	})
}

func (i *Integration) GetPromotionClickData(c *gin.Context) {
	input, _ := ginparam.RequestInputs(c)
	startStr := input["start_time"].(string)
	endStr := input["end_time"].(string)
	startTime := carbon.Parse(startStr)
	endTime := carbon.Parse(endStr)
	if endTime.Lt(startTime) {
		response.Forbidden(c, "Wrong time range")
		return
	}
	result, err := service.NewIntegration().GetPromotionClickData(startTime, endTime)
	if err != nil {
		response.Fail(c, err)
		return
	}
	response.Success(c, result)
}

func (i *Integration) GetAdsList(c *gin.Context) {
	var param paginate.Param
	page := c.Query("page")
	pageSize := c.Query("pageSize")
	param.Page, _ = strconv.Atoi(page)
	param.PageSize, _ = strconv.Atoi(pageSize)
	if param.Page <= 0 {
		param.Page = 1
	}

	if param.PageSize <= 0 {
		param.PageSize = 15
	}

	data := service.NewIntegration().GetIntegrationAdsList(param)
	response.Success(c, &response.SuccessResponse{
		Data: data,
	})
}

func (i *Integration) CreateAds(c *gin.Context) {
	var m models.IntegrationAds
	input, _ := ginparam.RequestInputs(c)
	title := input["title"].(string)
	image := input["image"].(string)
	description := input["description"].(string)
	var bgColor string
	if bgColorStr, ok := input["bg_color"].(string); ok {
		bgColor = bgColorStr
	} else {
		bgColor = ""
	}
	referralLink := input["referral_link"].(string)
	style := int(input["style"].(float64))
	showAt := input["show_at"].(string)
	sort := int(input["sort"].(float64))
	draftAt := input["draft_at"].(string)
	var showTime time.Time
	var draftTime sql.NullTime
	showTime, err := time.Parse("2006-01-02 15:04:05", showAt)
	if len(draftAt) != 0 {
		draftTime.Time, err = time.Parse("2006-01-02 15:04:05", draftAt)
		draftTime.Valid = true
	}
	if err != nil {
		response.UnprocessableEntity(c, err.Error())
		return
	}
	m = models.IntegrationAds{
		Image:        image,
		Title:        title,
		Description:  description,
		BgColor:      bgColor,
		ReferralLink: referralLink,
		Style:        style,
		Sort:         sort,
		ShowAt:       showTime,
		DraftAt:      draftTime,
	}
	var validate = validator.New()
	validate.RegisterStructValidationMapRules(request.CreateIntegrationAds, models.IntegrationAds{})
	if err := validate.Struct(m); err != nil {
		response.UnprocessableEntity(c, err.Error())
		return
	}
	logrus.Warning("test step 0")
	err = service.NewIntegration().CreateIntegrationAds(&m)
	logrus.Warning("test step 1")
	if err != nil {
		response.Fail(c, err)
		return
	}
	response.Created(c)
}

func (i *Integration) UpdateAds(c *gin.Context) {
	var m models.IntegrationAds
	input, _ := ginparam.RequestInputs(c)
	id := int(input["id"].(float64))
	title := input["title"].(string)
	image := input["image"].(string)
	sort := int(input["sort"].(float64))
	description := input["description"].(string)
	var bgColor string
	if bgColorStr, ok := input["bg_color"].(string); ok {
		bgColor = bgColorStr
	} else {
		bgColor = ""
	}
	referralLink := input["referral_link"].(string)
	style := int(input["style"].(float64))
	showAt := input["show_at"].(string)
	draftAt := input["draft_at"].(string)
	var showTime time.Time
	var draftTime sql.NullTime
	showTime, err := time.Parse("2006-01-02 15:04:05", showAt)
	if len(draftAt) != 0 {
		draftTime.Time, err = time.Parse("2006-01-02 15:04:05", draftAt)
		draftTime.Valid = true
	}
	if err != nil {
		response.UnprocessableEntity(c, err.Error())
		return
	}
	m = models.IntegrationAds{
		Image:        image,
		Title:        title,
		Description:  description,
		BgColor:      bgColor,
		ReferralLink: referralLink,
		Style:        style,
		ShowAt:       showTime,
		DraftAt:      draftTime,
		Sort:         sort,
		Model:        models.Model{ID: id},
	}
	var validate = validator.New()
	validate.RegisterStructValidationMapRules(request.UpdateIntegrationAds, models.IntegrationAds{})
	if err := validate.Struct(m); err != nil {
		response.UnprocessableEntity(c, err.Error())
		return
	}
	err = service.NewIntegration().UpdateIntegrationAds(&m)
	if err != nil {
		response.Fail(c, err)
		return
	}
	response.NotContent(c)
}
