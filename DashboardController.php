<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\BaseAuthController;
use App\Models\User\UserSetting;
use App\Repositories\Common\CountryRepository;
use App\Repositories\Integration\IntegrationAppRepository;
use App\Repositories\Statistics\StatisticalDailyLogRepository;
use App\Repositories\Tracking\Courier\CourierRepository;
use App\Repositories\User\UserSettingRepository;
use App\Services\Impl\AnalyticsService;
use App\Services\Impl\UserService;
use App\Utils\Redis;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Validator;

class DashboardController extends BaseAuthController
{
    private AnalyticsService $analyticsService;

    private IntegrationAppRepository $integrationAppRep;

    private StatisticalDailyLogRepository $staticsDailyLog;

    private UserService $userService;

    private UserSettingRepository $userSetting;

    public function __construct(
        UserService $userService,
        UserSettingRepository $userSetting,
        StatisticalDailyLogRepository $staticsDailyLog,
        IntegrationAppRepository $integrationAppRep,
        AnalyticsService $analyticsService,
    ) {
        $this->userService = $userService;
        $this->userSetting = $userSetting;
        $this->staticsDailyLog = $staticsDailyLog;
        $this->integrationAppRep = $integrationAppRep;
        $this->analyticsService = $analyticsService;
    }

    /**
     * 获取时效数据
     *
     * @param  Request  $request  请求
     *
     * @api delivery_performance
     */
    public function deliveryPerformance(Request $request): JsonResponse
    {
        $user = $this->checkAuthGuard();
        $start = $request->input('start');
        $end = $request->input('end');
        /** @var \Illuminate\Validation\Validator $validator */
        $validator = Validator::make(['start' => $start, 'end' => $end], [
            'start' => 'date',
            'end' => 'date',
        ]);
        if ($validator->fails()) {
            $this->errorBadRequest('Wrong time format');
        }
        $type = (int) $request->input('type');
        if (empty($type) || ($type !== 1 && $type !== 2)) {
            $this->errorBadRequest();
        }
        $start = Carbon::make($start);
        if ($start->getTimestamp() < strtotime('2022-03-30')) {
            $start = Carbon::make('2022-03-30');
        }
        if (empty($end)) {
            $end = Carbon::now();
        } else {
            $end = Carbon::make($end);
        }
        if ($end < $start) {
            $this->errorBadRequest('Time range illegal!');
        }
        $statics = $this->staticsDailyLog->where([
            ['create_date', '>=', $start->getTimestamp()], ['create_date', '<=', $end->getTimestamp()],
            'user_id' => $user->id,
        ])->get();
        $data = [
            // [delivery days,exception,nums,keywords]
            'chartData' => [
                'poorData' => [],
                'goodData' => [],
                'mediumData' => [],
            ],
        ];
        $data['total'] = 0;
        $data['min'] = 0;
        $data['max'] = 0;
        $data['xIntervalMin'] = 0;
        $data['xIntervalMax'] = 0;
        $data['yIntervalMin'] = 0;
        $data['yIntervalMax'] = 0;
        if ($statics->isEmpty()) {
            return $this->success($data);
        }
        $statistics = [];
        foreach ($statics as $item) {
            $countries = json_decode($item->country_statistics, 1);
            $couriers = json_decode($item->courier_statistics, 1);
            if ($type === 2) {
                $statistics = $this->handleStatistics($statistics, $countries);
            }
            if ($type === 1) {
                $statistics = $this->handleStatistics($statistics, $couriers);
            }
        }
        if (! empty($statistics)) {
            $totalList = array_column($statistics, 'count');
            $data['min'] = min($totalList);
            $data['max'] = max($totalList);
            $data['total'] = array_sum($totalList);
        }
        foreach ($statistics as $key => $v) {
            $exception_rate = 0;
            if (! empty($v['total'])) {
                $exception_rate = $v['exception_num'] / $v['total'];
            }
            $statistics[$key]['delivery_day'] = $v['day'] / $v['count'];
            $statistics[$key]['exception_rate'] = round($exception_rate * 1000, 1);
        }
        $deliveryColumn = array_column($statistics, 'delivery_day');
        $minDeliveryDay = ! empty($deliveryColumn) ? min($deliveryColumn) : 0;
        $maxDeliveryDay = ! empty($deliveryColumn) ? max($deliveryColumn) : 0;
        $exceptionRateColumn = array_column($statistics, 'exception_rate');
        $minExceptionRate = ! empty($exceptionRateColumn) ? min($exceptionRateColumn) : 0;
        $maxExceptionRate = ! empty($exceptionRateColumn) ? max($exceptionRateColumn) : 0;
        $xMin = null;
        $xMax = 0;
        $yMin = null;
        $yMax = 0;

        foreach ($statistics as $key => $v) {
            $xV = $v['delivery_day'];
            if ($xV > $xMax) {
                $xMax = $xV;
            }
            if (is_null($xMin) || $xV < $xMin) {
                $xMin = $xV;
            }
            $yV = $v['exception_rate'];
            if ($yV > $yMax) {
                $yMax = $yV;
            }
            if (is_null($yMin) || $yV < $yMin) {
                $yMin = $yV;
            }
            $x = $y = 0;
            if ($maxDeliveryDay > 0 && $minDeliveryDay !== $maxDeliveryDay) {
                $x = ($v['delivery_day'] - $minDeliveryDay) / ($maxDeliveryDay - $minDeliveryDay);
            }
            if ($maxExceptionRate > 0 && $minExceptionRate !== $maxExceptionRate) {
                $y = ($v['exception_rate'] - $minExceptionRate) / ($maxExceptionRate - $minExceptionRate);
            }
            $rate = ($x ** 2) + ($y ** 2);
            $name = $key;
            if ($type === 2) {
                $name = app(CountryRepository::class)->getCountryNameByCode($key) ?? $key;
            }
            if ($type === 1) {
                $name = app(CourierRepository::class)->getCompanyInfoByCodeAndLang($key)['company_name'] ?? $key;
            }
            $symbolSize = ($v['count'] / 1000);
            if ($symbolSize > 50) {
                $symbolSize = 50;
            }
            if ($symbolSize < 10) {
                $symbolSize = 10;
            }
            if ($rate <= (0.33 ** 2)) {
                $data['chartData']['goodData'][] = [
                    'value' => [
                        round($v['delivery_day'], 1),
                        $v['exception_rate'],
                        $v['count'],
                        $name,
                    ],
                    'symbolSize' => $symbolSize,
                ];

                continue;
            }
            if ($rate <= (0.8 ** 2)) {
                $data['chartData']['mediumData'][] = [
                    'value' => [
                        round($v['delivery_day'], 1),
                        $v['exception_rate'],
                        $v['count'],
                        $name,
                    ],
                    'symbolSize' => $symbolSize,
                ];

                continue;
            }
            if ($rate > (0.8 ** 2)) {
                $data['chartData']['poorData'][] = [
                    'value' => [
                        round($v['delivery_day'], 1),
                        $v['exception_rate'],
                        $v['count'],
                        $name,
                    ],
                    'symbolSize' => $symbolSize,
                ];
            }
        }

        $data['xIntervalMin'] = $xMin;
        $data['xIntervalMax'] = $xMax;
        $data['yIntervalMin'] = $yMin;
        $data['yIntervalMax'] = $yMax;

        return $this->success($data);
    }

    /**
     * 此函数接受一个统计数据数组和一个dailyLogs 数组。
     * 它循环遍历dailyLogs 数组并将dailyLogs 数据添加到statistics 数组中。
     * 它返回更新后的统计数组
     *
     * @param  array  $statistics  统计数组。
     * @param  array  $dailyLogs  一系列每日日志
     * @return array 统计数组
     */
    public function handleStatistics($statistics, $dailyLogs): array
    {
        if (! empty($dailyLogs)) {
            foreach ($dailyLogs as $tag => $arr) {
                //运输商数据累加
                if (! empty($arr['day'])) {
                    if (! empty($statistics[$tag])) {
                        $statistics[$tag]['day'] += $arr['day'];
                        $statistics[$tag]['count'] += $arr['count'];
                        $statistics[$tag]['exception_num'] += $arr['exception'] ?? 0;
                        $statistics[$tag]['delivered_num'] += $arr['delivered'] ?? 0;
                        $count = $statistics[$tag]['exception_num'] + $statistics[$tag]['delivered_num'];
                        $statistics[$tag]['total'] += $count;
                    } else {
                        $statistics[$tag]['day'] = $arr['day'];
                        $statistics[$tag]['count'] = $arr['count'];
                        $statistics[$tag]['exception_num'] = $arr['exception'] ?? 0;
                        $statistics[$tag]['delivered_num'] = $arr['delivered'] ?? 0;
                        $statistics[$tag]['total'] = $statistics[$tag]['exception_num'] + $statistics[$tag]['delivered_num'];
                    }
                }
            }
        }

        return $statistics;
    }

    /**
     * 获取dashboard页面数据
     *
     * @return JsonResponse JSON 对象的形式返回。
     *
     * @api order_data
     */
    public function pageData(): JsonResponse
    {
        $user = $this->checkAuthGuard();
        $data['coupon_data'] = $this->userService->getCouponSellsData($user);
        $data['picked_for_you'] = $this->integrationAppRep->getTopAppList();
        $settingArr = $this->userSetting->getAllSettingByKeys($user->id, [
            UserSetting::$dashboard_trends_settings,
            UserSetting::$special_status_settings,
            UserSetting::$account_manager_banner,
            UserSetting::$order_sync_banner,
            UserSetting::$multilingual_tracking_banner,
            UserSetting::$post_purchase_edd_banner,
            UserSetting::$pre_sales_plugin_banner,
            UserSetting::$merchant_notification_banner,
            UserSetting::$logistics_analysis_banner,
            UserSetting::$aggregation_subscription_banner,
            UserSetting::$integrate_apps_banner,
        ]);
        $data['dashboard_trends_settings'] = ! empty($settingArr[UserSetting::$dashboard_trends_settings]) ? json_decode($settingArr[UserSetting::$dashboard_trends_settings], true) : ['tracking_status', 'shipments_lookups', 'total_exceptions'];
        $data['special_status_settings'] = ! empty($settingArr[UserSetting::$special_status_settings]) ? json_decode($settingArr[UserSetting::$special_status_settings], true) : ['information_delayed' => 3, 'pick_up' => 15, 'transport_too_long' => 3];
        $data['help_card'] = [
            UserSetting::$account_manager_banner => empty($settingArr[UserSetting::$account_manager_banner]),
            UserSetting::$order_sync_banner => empty($settingArr[UserSetting::$order_sync_banner]),
            UserSetting::$multilingual_tracking_banner => empty($settingArr[UserSetting::$multilingual_tracking_banner]),
            UserSetting::$post_purchase_edd_banner => empty($settingArr[UserSetting::$post_purchase_edd_banner]),
            UserSetting::$pre_sales_plugin_banner => empty($settingArr[UserSetting::$pre_sales_plugin_banner]),
            UserSetting::$merchant_notification_banner => empty($settingArr[UserSetting::$merchant_notification_banner]),
            UserSetting::$logistics_analysis_banner => empty($settingArr[UserSetting::$logistics_analysis_banner]),
            UserSetting::$aggregation_subscription_banner => empty($settingArr[UserSetting::$aggregation_subscription_banner]),
            UserSetting::$integrate_apps_banner => empty($settingArr[UserSetting::$integrate_apps_banner]),
        ];

        return $this->success($data);
    }

    /**dashboard柱状图数据
     * @param Request $request
     * @return JsonResource|JsonResponse
     * @api dashboard/time_distribution
     */
    public function transitTimeDistribution(Request $request)
    {
        $user = $this->checkAuthGuard();
        $start = $request->input('start');
        $end = $request->input('end');
        // 要查询的是1:运输商还是2:目的国
        $type = (int) $request->input('type');
        // 要查询的运输时效分布的 运输商名称/目的国名称
        $key = $request->input('key');
        if (empty($type) || ($type !== 1 && $type !== 2) || empty($start) || empty($key)) {
            $this->errorBadRequest();
        }
        $start = Carbon::make($start);

        if (empty($end)) {
            $end = Carbon::now();
        } else {
            $end = Carbon::make($end);
        }
        if ($end < $start) {
            $this->errorBadRequest('Time range illegal!');
        }
        $statics = [];
        if ($type === 2) {
            $key = app(CountryRepository::class)->where('en_name', $key)->value('two_code') ?? $key;
            $statics = $this->staticsDailyLog->where([
                ['create_date', '>=', $start->getTimestamp()], ['create_date', '<=', $end->getTimestamp()],
                'user_id' => $user->id,
            ])->pluck('country_distribution');
        }
        if ($type === 1) {
            $key = app(CourierRepository::class)->where('company_name', $key)->value('company_code') ?? $key;
            $statics = $this->staticsDailyLog->where([
                ['create_date', '>=', $start->getTimestamp()], ['create_date', '<=', $end->getTimestamp()],
                'user_id' => $user->id,
            ])->pluck('courier_distribution');
        }
        $data = [
            'item1' => 0,
            'item2' => 0,
            'item3' => 0,
            'item4' => 0,
            'item5' => 0,
            'item6' => 0,
            'item7' => 0,
            'item8' => 0,
        ];
        if (count($statics) !== 0) {
            foreach ($statics as $value) {
                if (empty($value)) {
                    continue;
                }
                $distribution = json_decode($value, true);
                if (! array_key_exists($key, $distribution)) {
                    continue;
                }
                foreach ($distribution[$key] as $k => $v) {
                    $data[$k] += $v;
                }
            }
        }

        $chart_data = [
            $data['item1'],
            $data['item2'],
            $data['item3'],
            $data['item4'],
            $data['item5'],
            $data['item6'],
            $data['item7'],
            $data['item8'],
        ];

        return $this->success($chart_data);
    }

    /** 获取 dashboard 趋势图数据
     * @return JsonResponse|JsonResource
     */
    public function getTrendChartData()
    {
        $user = $this->checkAuthGuard();
        $status_list = $this->userService->getUserStatusList($user->id);
        if ($status_list['order_count'] === 0) {
            return $this->success(null);
        }
        if ($status_list['order_count'] > 0 && $user->created_at->diffInDays(Carbon::now()) < 2) {
            return $this->success(null);
        }
        $endDate = ! empty($params['end']) ? Carbon::parse($params['end'])->addDay() : Carbon::createFromTime()->subDay();
        $startDate = ! empty($params['start']) ? Carbon::parse($params['start']) : $endDate->copy()->addDays(-30);
        $data = $this->analyticsService->getTrendChartData($user, $startDate, $endDate);
        $data['shipments_lookups_chart'] = $this->analyticsService->getShipmentsLookupsData($user);

        $data['status_list']['data'] = $status_list['status_list'];
        $data['status_list']['order_count'] = $status_list['order_count'];

        return $this->success($data);
    }

    /** 更新 dashboard 设置
     * @return JsonResponse|JsonResource
     */
    public function updateDashboardSetting(Request $request)
    {
        $user = $this->checkAuthGuard();
        $params = $request->all();
        if (empty($params)) {
            $this->errorBadRequest();
        }
        // 更新趋势图设置
        if (isset($params['dashboard_trends_settings'])) {
            $validator = Validator::make($params, [
                'dashboard_trends_settings' => 'array',
            ]);
            if ($validator->fails()) {
                $this->errorBadRequest();
            }
            $this->userSetting->setUserValue($user->id, UserSetting::$dashboard_trends_settings, json_encode($params['dashboard_trends_settings']));
        }
        // 更新异常状态图表设置
        if (isset($params['special_status_settings'])) {
            $validator = Validator::make($params, [
                'special_status_settings' => 'array',
                'special_status_settings.information_delayed' => 'required|numeric',
                'special_status_settings.pick_up' => 'required|numeric',
                'special_status_settings.transport_too_long' => 'required|numeric',
            ]);
            if ($validator->fails()) {
                $this->errorBadRequest();
            }
            $this->userSetting->setUserValue($user->id, UserSetting::$special_status_settings, json_encode($params['special_status_settings']));
            // 清除数据缓存
            Redis::del(config('cache_key.dashboardSpecialStatus').$user->id);
        }

        return $this->success(true);
    }

    /** 获取 dashboard 特殊状态看板数据
     * @return JsonResponse|JsonResource
     */
    public function getSpecialStatusData()
    {
        $user = $this->checkAuthGuard();
        $data = $this->analyticsService->getSpecialStatusData($user);

        return $this->success($data);
    }
}
