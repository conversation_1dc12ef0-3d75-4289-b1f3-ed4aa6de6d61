package impl

import (
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type BuriedPoint struct {
	Helper
}

func (b *BuriedPoint) GetBuriedPointData() ([]map[string]interface{}, error) {
	var column []map[string]interface{}
	result := database.DB.Model(&models.BuriedPoint{}).
		Select("name,sum(trigger_number) as number").
		Group("name").
		Order("number desc").
		Scan(&column)
	return column, result.Error
}
