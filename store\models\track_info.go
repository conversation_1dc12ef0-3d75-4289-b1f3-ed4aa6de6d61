package models

import (
	"time"
)

type TrackInfos struct {
	Partition
	TrackNumber          string        `gorm:"column:track_number;type:char(100);comment:运单号;NOT NULL" json:"track_number"`
	Courier              string        `gorm:"column:courier;type:char(100);comment:运输商公司简码;NOT NULL" json:"courier"`
	AlterCourier         string        `gorm:"column:alter_courier;type:char(100);comment:修改前运输商公司简码;NOT NULL" json:"alter_courier"`
	OriginalCountry      string        `gorm:"column:original_country;type:char(100);comment:发件国国家名称;NOT NULL" json:"original_country"`
	DestinationCountry   string        `gorm:"column:destination_country;type:char(100);comment:目的国国家名称;NOT NULL" json:"destination_country"`
	OriginInfo           string        `gorm:"column:origin_info;type:text;comment:发件国信息" json:"origin_info"`
	DestinationInfo      string        `gorm:"column:destination_info;type:text;comment:收件国信息" json:"destination_info"`
	SubStatus            string        `gorm:"column:sub_status;type:char(32);comment:查询子状态;NOT NULL" json:"sub_status"`
	LastDate             uint          `gorm:"column:last_date;type:int(10) unsigned;default:0;comment:最后一条物流时间;NOT NULL" json:"last_date"`
	ExpectedDeliveryTime time.Time     `gorm:"column:expected_delivery_time;type:timestamp;comment:预计到达时间" json:"expected_delivery_time"`
	LastEvent            string        `gorm:"column:last_event;type:varchar(500);comment:物流最后一条信息;NOT NULL" json:"last_event"`
	TransitTime          uint          `gorm:"column:transit_time;type:int(10) unsigned;default:0;comment:运输时间(天);NOT NULL" json:"transit_time"`
	StayTime             uint          `gorm:"column:stay_time;type:int(10) unsigned;default:0;comment:总时间(天);NOT NULL" json:"stay_time"`
	TrackStatus          uint          `gorm:"column:track_status;type:tinyint(3) unsigned;default:0;comment:最新状态值;NOT NULL" json:"track_status"`
	AlterTrackStatus     uint          `gorm:"column:alter_track_status;type:tinyint(3) unsigned;default:0;comment:自定义状态值;NOT NULL" json:"alter_track_status"`
	TrackCreateStatus    uint          `gorm:"column:track_create_status;type:tinyint(3) unsigned;default:0;comment:0未创建，1已创建，2创建失败;NOT NULL" json:"track_create_status"`
	IsDelete             uint          `gorm:"column:is_delete;type:tinyint(3) unsigned;default:0;comment:是否已删除;NOT NULL" json:"is_delete"`
	HasException         uint          `gorm:"column:has_exception;type:tinyint(3) unsigned;default:0;comment:异常时间;NOT NULL" json:"has_exception"`
	SignedBy             string        `gorm:"column:signed_by;type:varchar(255);NOT NULL" json:"signed_by"`
	OrderTracks          []OrderTracks `gorm:"foreignKey:TrackId" json:"order_tracks"`
	Model
}

func (TrackInfos) TableName() string {
	return "track_infos"
}
