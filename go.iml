<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="Go" enabled="true" />
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/cmd" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/internal" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/pkg" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/scripts" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/server" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/store" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/test" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/config" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/.idea" />
      <excludeFolder url="file://$MODULE_DIR$/tmp" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>