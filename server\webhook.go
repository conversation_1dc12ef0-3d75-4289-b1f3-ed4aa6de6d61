package server

import (
	"github.com/gin-gonic/gin"
	"tmshopify/internal/webhook/api"
	"tmshopify/server/middleware"
)

func InitWebhookRouter(router *gin.Engine) *gin.Engine {

	err := router.SetTrustedProxies([]string{"192.168.20.55"})
	if err != nil {
		return nil
	}

	v1 := router.Group("/api/v1")
	{
		// shopify webhook 路由组
		webhookGroup := v1.Group("/webhook")
		{
			shopify := new(api.Shopify)
			webhookGroup.POST("/test", shopify.Test)
			webhookGroup.POST("/shopify", middleware.ShopifyWebhook(), shopify.Webhook)
			webhookGroup.POST("/update", middleware.ShopifyWebhook(), shopify.Webhook)
			webhookGroup.POST("/create", middleware.ShopifyWebhook(), shopify.Webhook)
			webhookGroup.POST("/uninstall", middleware.ShopifyWebhook(), shopify.Webhook)
			trackingMore := new(api.TrackingMore)
			webhookGroup.POST("/tracking-more", trackingMore.Webhook)
		}

	}
	v2 := router.Group("/api/v2")
	{
		// shopify webhook 路由组
		webhookGroupV2 := v2.Group("/webhook")
		{
			shopify := new(api.Shopify)
			webhookGroupV2.GET("/test", shopify.Test)
			webhookGroupV2.POST("/shopify-flow", middleware.ShopifyFlowWebhook(), shopify.FlowWebhook)
		}

	}
	return router

}
