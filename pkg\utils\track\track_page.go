package track

import (
	"fmt"
	"strings"
	"time"
	"tmshopify/config"
	"unicode"
	"unicode/utf8"
)

type StatusInfo struct {
	Key    string
	Name   string
	Status config.StatusNodeNumber
}

func HandleTimeFormatBySet(timestamp int64, date, timeFmt int, isHiddenYear, isHiddenTime bool) string {
	var format []string
	// 默认值
	if date == 0 && timeFmt == 0 {
		date = 1
		if isHiddenYear || isHiddenTime {
			timeFmt = 0
		} else {
			timeFmt = 1
		}
	} else {
		date++
		timeFmt++
	}

	// 处理日期格式
	switch date {
	case 1:
		if isHiddenYear {
			format = append(format, "Jan 02")
		} else {
			format = append(format, "Jan 02, 2006")
		}
	case 2:
		format = append(format, "Jan 02")
	case 3:
		day := time.Unix(timestamp, 0).Day()

		var ordinal string
		switch day % 10 {
		case 1:
			ordinal = "st"
		case 2:
			ordinal = "nd"
		case 3:
			ordinal = "rd"
		default:
			ordinal = "th"
		}

		if day >= 11 && day <= 13 {
			ordinal = "th"
		}

		if isHiddenYear {
			format = append(format, fmt.Sprintf("Jan 02%s", ordinal))
		} else {
			format = append(format, fmt.Sprintf("Jan 02%s 2006", ordinal))
		}
	case 4:
		if isHiddenYear {
			format = append(format, "1/2")
		} else {
			format = append(format, "1/2/2006")
		}
	case 5:
		if isHiddenYear {
			format = append(format, "2/1")
		} else {
			format = append(format, "2/1/2006")
		}
	case 6:
		if isHiddenYear {
			format = append(format, "01.02")
		} else {
			format = append(format, "01.02.2006")
		}
	case 7:
		if isHiddenYear {
			format = append(format, "01-02")
		} else {
			format = append(format, "2006-01-02")
		}
	default:
	}

	// 处理时间格式
	switch timeFmt {
	case 1:
		format = append(format, "03:04 PM")
	case 2:
		format = append(format, "15:04")
	case 3:
		format = append(format, "")
	default:
	}
	formatStr := ""
	if len(format) > 0 {
		formatStr = fmt.Sprintf("%s %s", format[0], format[1])
	}
	return time.Unix(timestamp, 0).Format(formatStr)
}

func GetStatusSummary(translation map[string]string, status config.StatusNodeNumber) string {
	arr := []StatusInfo{
		{"ordered", translation["ordered"], 1001},
		{"order_ready", translation["order_ready"], 1100},
		{"transit", translation["transit"], 2},
		{"pickup", translation["pickup"], 3},
		{"delivered", translation["delivered"], 4},
	}
	for _, item := range arr {
		if status == item.Status {
			return item.Name
		}
	}
	return ""
}

func GetCheckpointStatus(status config.StatusNodeNumber) string {
	arr := map[config.StatusNodeNumber]string{
		2:    "transit",
		3:    "pickup",
		4:    "delivered",
		1001: "blank",
		1100: "blank",
	}
	if val, ok := arr[status]; ok {
		return val
	}
	return "blank"
}

// CapitalizeLetter 将字符串的首字母转为大写
func CapitalizeLetter(s string) string {
	if s == "" {
		return s
	}

	// Decode the first rune and its size
	firstRune, size := utf8.DecodeRuneInString(s)
	if firstRune == utf8.RuneError {
		return s // Handle invalid UTF-8 character
	}

	// Capitalize the first letter
	result := string(unicode.ToTitle(firstRune))

	// Convert the rest of the string to lowercase
	for _, r := range s[size:] {
		result += string(unicode.ToLower(r))
	}

	return result
}

func ConvertTimeZone(timeZone string) string {
	if strings.Contains(timeZone, "-") {
		return "Etc/GMT" + strings.ReplaceAll(timeZone, "-", "+")
	} else if strings.Contains(timeZone, "+") {
		return "Etc/GMT" + timeZone

	} else if timeZone == "5.5" {
		return "Asia/Kolkata"

	} else {
		return "Etc/GMT-" + timeZone
	}
}
