package order

import (
	"context"

	"gorm.io/gorm"

	repo "tmshopify/internal/domain/repo/orders"
	"tmshopify/pkg/shopify"
)

var _ repo.ShopifyOrderRepository = (*ShopifyOrderRepoImpl)(nil)

type ShopifyOrderRepoImpl struct {
	db *gorm.DB
}

func (s ShopifyOrderRepoImpl) GetOrderData(ctx context.Context, query string) ([]shopify.Order, error) {
	// TODO implement me
	panic("implement me")
}

func NewUserWebhookRepo(db *gorm.DB) repo.ShopifyOrderRepository {
	return &ShopifyOrderRepoImpl{
		db,
	}
}
