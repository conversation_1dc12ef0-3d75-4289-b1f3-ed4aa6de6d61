package models

type Checkout struct {
	UserId             int    `gorm:"column:user_id;type:INT(11);comment:user id;not null;" json:"user_id"`
	Shop               string `gorm:"column:shop;type:CHAR(255);comment:shopify shop name;not null;" json:"shop"`
	TrackLinkOn        int    `gorm:"column:track_link_on;type:TINYINT(3) UNSIGNED;comment:link off;not null;default:0;" json:"track_link_on"`
	ReplaceNumberOn    int    `gorm:"column:replace_number_on;type:TINYINT(3) UNSIGNED;comment:replace off;not null;default:0;" json:"replace_number_on"`
	TrackLinkText      string `gorm:"column:track_link_text;type:CHAR(255);comment:custom text;not null;" json:"track_link_text"`
	OrderStatusSetting string `gorm:"column:order_status_setting;type:TEXT(65535);default:NULL COLLATE utf8mb4_unicode_ci;" json:"order_status_setting"`
	TrackLinkColor     string `gorm:"column:track_link_color;type:CHAR(255);not null;" json:"track_link_color"`
	AddOnType          int    `gorm:"column:add_on_type;type:TINYINT(3) UNSIGNED;comment:1为Card,2为Plain button;not null;default:2;" json:"add_on_type"`
	ReplaceWith        int    `gorm:"column:replace_with;type:TINYINT(3) UNSIGNED;comment:推送给shopify的链接参数(1:tracking number,2:order number and email,3:order number and phone number);not null;default:1;" json:"replace_with"`
	SyncPastOrder      int    `gorm:"column:sync_past_order;type:TINYINT(3) UNSIGNED;comment:是否同步历史订单(0:否,1:是);not null;default:0;" json:"sync_past_order"`
	WidgetSetting      string `gorm:"column:widget_setting;type:TEXT(65535);default:NULL COLLATE utf8mb4_unicode_ci;" json:"widget_setting"`
	Model
}

func (Checkout) TableName() string {
	return "checkouts"
}
