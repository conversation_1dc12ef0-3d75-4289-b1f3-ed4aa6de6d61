package impl

import (
	"tmshopify/pkg/utils/paginate"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type SubscriptionDetailRepository interface {
	GetSubscriptionList(userId int, status string, param paginate.Param) []models.DataSubscriptionDetails
	ChangeSubscriptionExtraPrice(userId int, extraPrice float32) error
}

type SubscriptionDetailRepositoryImpl struct {
	Helper
}

func (s *SubscriptionDetailRepositoryImpl) GetSubscriptionList(userId int, status string, param paginate.Param) []models.DataSubscriptionDetails {
	var result []models.DataSubscriptionDetails
	query := database.DB.Model(&models.DataSubscriptionDetails{})
	if userId != 0 {
		query = query.Where("user_id = ?", userId)
	}

	if status != "" {
		query = query.Where("status = ?", status)
	}
	query = query.Scopes(paginate.ORMScope(&param))
	query.Find(&result)
	return result
}

func (s *SubscriptionDetailRepositoryImpl) ChangeSubscriptionExtraPrice(userId int, extraPrice float32) error {
	err := database.DB.Model(&models.DataSubscriptionDetails{}).Where("user_id = ?", userId).Update("extra_price", extraPrice).Error
	return err
}
