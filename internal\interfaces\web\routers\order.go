package routers

import (
	"github.com/gin-gonic/gin"

	"tmshopify/internal/application/order"
	"tmshopify/internal/infras/config"
	"tmshopify/internal/interfaces/web/handlers/order"
	"tmshopify/internal/providers"
)

func RegisterOrderRoute(routeGroup *gin.RouterGroup, repos *providers.Repositories, appConfig *config.AppConfig) {
	orderGroup := routeGroup.Group("/order")

	orderService := order.NewService(repos)
	orderCountHandler := handlers.NewOrderCountHandler(orderService)
	orderGroup.GET("/count", orderCountHandler.GetOrderCount)
}
