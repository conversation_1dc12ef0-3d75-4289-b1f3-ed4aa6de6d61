package impl

import (
	"gorm.io/gorm"
	"strings"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type OrderTrack struct {
	Helper
}

func (o *OrderTrack) PreloadTrackInfo(userId int) *gorm.DB {
	var trackInfo models.TrackInfos
	var orderTrack models.OrderTracks
	return database.DB.Scopes(orderTrack.TableOfUser(orderTrack.TableName(), userId)).
		Preload("TrackInfos", func(db *gorm.DB) *gorm.DB {
			return database.DB.Scopes(trackInfo.TableOfUser(trackInfo.TableName(), userId)).Where("user_id", userId)
		}).Where("user_id", userId)
}

func (o *OrderTrack) GetOrderId(userId int, trackNumber string) (string, error) {
	var res struct {
		OrderId string `json:"order_id"`
	}
	result := database.DB.Scopes(models.OrderTracks{}.TableOfUser(models.OrderTracks{}.TableName(), userId)).
		Where("user_id = ? AND track_number = ? AND is_delete = ? AND order_id != ?", userId, trackNumber, 0, "").
		Order("created_at ASC").Limit(1).Select("order_id").Scan(&res)

	return res.OrderId, result.Error
}

func (o *OrderTrack) GetOrderFulfillTime(userId int, trackNumber string) (int, int, error) {
	var res struct {
		OrderFulfillTime int `json:"order_fulfill_time"`
		TrackStatus      int `json:"track_status"`
	}
	err := database.DB.Scopes(models.OrderTracks{}.TableOfUser(models.OrderTracks{}.TableName(), userId)).Where("user_id = ? AND track_number = ?", userId, trackNumber).Limit(1).Select("order_fulfill_time, track_status").Scan(&res).Error
	return res.OrderFulfillTime, res.TrackStatus, err
}

func (o *OrderTrack) GetOrderIdByOrderName(userId int, orderName string) (string, error) {
	var res struct {
		OrderId string `json:"order_id"`
	}
	originOrderName := orderName
	check := strings.HasPrefix(orderName, "#")
	if !check {
		orderName = "#" + originOrderName
	}

	query := database.DB.Scopes(models.OrderTracks{}.TableOfUser(models.OrderTracks{}.TableName(), userId)).
		Where("user_id = ? AND is_delete = ? AND order_id != ?", userId, 0, "")
	if orderName != originOrderName {
		query.Where("order_name IN (?)", []string{orderName, originOrderName})
	} else {
		query.Where("order_name = ?", orderName)
	}
	result := query.Order("created_at ASC").Limit(1).Select("order_id").Scan(&res)
	return res.OrderId, result.Error
}
