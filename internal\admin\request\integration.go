package request

var CreateIntegrationApp = map[string]string{
	"name":         "required,max=50",
	"logo":         "required",
	"listing_Link": "required",
	"description":  "required,max=200",
	"category_Id":  "required,number",
	"plan":         "required,number",
	"status":       "required,number",
	"sort":         "required,number",
	"tag":          "required,number",
}

var UpdateIntegrationApp = map[string]string{
	"name":         "required,max=50",
	"logo":         "required",
	"listing_Link": "required",
	"description":  "required,max=200",
	"category_Id":  "required,number",
	"status":       "required,number",
	"sort":         "required,number",
	"id":           "required,number",
}

var CreateIntegrationAds = map[string]string{
	"title":         "required,max=50",
	"image":         "required",
	"description":   "required,max=200",
	"bg_color":      "required",
	"referral_link": "required,max=100",
	"style":         "required,number",
	"show_at":       "required",
}

var UpdateIntegrationAds = map[string]string{
	"id":            "required,number",
	"title":         "required,max=50",
	"image":         "required",
	"description":   "required,max=200",
	"bg_color":      "required",
	"sort":          "required,number",
	"referral_link": "required,max=100",
	"style":         "required,number",
	"show_at":       "required",
}
