package api

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/locales/en"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	enTranslations "github.com/go-playground/validator/v10/translations/en"
	"golang.org/x/crypto/bcrypt"
	"strconv"
	"time"
	"tmshopify/internal/admin/request"
	"tmshopify/internal/admin/service"
	"tmshopify/pkg/auth"
	"tmshopify/pkg/utils/ginparam"
	"tmshopify/server/response"
	"tmshopify/store/database"
	"tmshopify/store/models"
	"unicode"
)

type Auth struct {
}

// use a single instance, it caches struct info
var (
	uni      *ut.UniversalTranslator
	validate *validator.Validate
)

func PasswordValidation(fl validator.FieldLevel) bool {
	password := fl.Field().String()
	minLen := 16
	var (
		isUpper   = false
		isLower   = false
		isNumber  = false
		isSpecial = false
	)

	if len(password) < minLen {
		return false
	}

	for _, s := range password {
		switch {
		case unicode.IsUpper(s):
			isUpper = true
		case unicode.IsLower(s):
			isLower = true
		case unicode.IsNumber(s):
			isNumber = true
		case unicode.IsPunct(s) || unicode.IsSymbol(s):
			isSpecial = true
		default:
		}
	}
	if (isUpper && isLower) && (isNumber || isSpecial) {
		return true
	}
	return false

}
func init() {
	validate = validator.New(validator.WithRequiredStructEnabled())

	// Register the custom validation function
	validate.RegisterValidation("password", PasswordValidation)
	enTranslator := en.New()
	cnTranslator := zh.New()
	uni = ut.New(enTranslator, cnTranslator)

	// also see uni.FindTranslator(...)
	trans, _ := uni.GetTranslator("en")
	enTranslations.RegisterDefaultTranslations(validate, trans)
	validate.RegisterTranslation("password", trans, registerTranslator("password", "The password must be at least 16 characters long, with at least 1 uppercase letter, 1 lowercase letter, and 1 number. The rest can be any character!"), translate)

}

// registerTranslator 为自定义字段添加翻译功能
func registerTranslator(tag string, msg string) validator.RegisterTranslationsFunc {
	return func(trans ut.Translator) error {
		if err := trans.Add(tag, msg, false); err != nil {
			return err
		}
		return nil
	}
}

// translate 自定义字段的翻译方法
func translate(trans ut.Translator, fe validator.FieldError) string {
	msg, err := trans.T(fe.Tag(), fe.Field())
	if err != nil {
		panic(fe.(error).Error())
	}
	return msg
}

// Login handles the login functionality.
//
// It expects the following parameters:
// - ctx: A pointer to the gin.Context object.
//
// It does not return anything.
func (a *Auth) Login(ctx *gin.Context) {

	input, _ := ginparam.RequestInputs(ctx)
	email := input["email"].(string)
	code := input["code"].(string)
	res, _ := database.RS.Get("adminLogin:" + email)

	if string(res) != code {
		response.BadRequest(ctx, "验证码错误")
		return
	}
	password := input["password"].(string)
	if err := validate.ValidateMap(map[string]interface{}{
		"email":    email,
		"code":     code,
		"password": password,
	}, request.EmailLoginRequest); len(err) > 0 {
		errString, _ := json.Marshal(err)
		response.UnprocessableEntity(ctx, string(errString))
		return
	}
	var user models.AdminUsers
	var err error
	err = database.DB.First(&user, "email = ?", email).Error
	if err != nil {
		response.NotFound(ctx, "用户名或邮箱不存在")
		return
	}
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
	if err != nil {
		response.Forbidden(ctx, "密码有误")
		return
	}
	_ = database.RS.Delete("adminLogin:" + email)
	token, _ := auth.New().CreateUserToken(user.ID, "admin")
	//$user->created_at->addMonth()->getTimestamp()<time()
	needReset := false
	if user.UpdatedAt.AddDate(0, 1, 0).Unix() < time.Now().Unix() {
		needReset = true
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: map[string]interface{}{
			"token":     token,
			"user":      user,
			"need_rest": needReset,
		},
	})
}

// LoginPasswd handles the login functionality.
//
// It expects the following parameters:
// - ctx: A pointer to the gin.Context object.
//
// It does not return anything.
func (a *Auth) LoginPasswd(ctx *gin.Context) {
	input, _ := ginparam.RequestInputs(ctx)
	email := input["email"].(string)
	password := input["password"].(string)
	if err := validate.ValidateMap(map[string]interface{}{
		"email":    email,
		"password": password,
	}, request.LoginPasswdRequest); len(err) > 0 {
		errString, _ := json.Marshal(err)
		response.UnprocessableEntity(ctx, string(errString))
		return
	}
	var user models.AdminUsers
	var err error
	err = database.DB.First(&user, "email = ?", email).Error
	if err != nil {
		response.NotFound(ctx, "用户名或邮箱不存在")
		return
	}
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
	if err != nil {
		response.Forbidden(ctx, "密码有误")
		return
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: map[string]interface{}{
			"check": true,
		},
	})
}

func (a *Auth) Logout(ctx *gin.Context) {
	response.NotContent(ctx)
}

func (a *Auth) ChangePassword(ctx *gin.Context) {
	user := ctx.MustGet("User").(models.AdminUsers)
	input, _ := ginparam.RequestInputs(ctx)
	confirmPassword := input["confirm_password"].(string)
	newPassword := input["password"].(string)
	if err := validate.ValidateMap(map[string]interface{}{
		"confirm_password": input["confirm_password"],
		"password":         input["password"],
	}, request.ChangePasswordRequest); len(err) > 0 {
		errString, _ := json.Marshal(err)
		response.UnprocessableEntity(ctx, string(errString))
		return
	}
	if confirmPassword != newPassword {
		response.BadRequest(ctx, "二次输入的密码不相同")
		return
	}
	user, err := service.NewAdmin().ChangePassword(user, newPassword)
	if err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}
	token, _ := auth.New().CreateUserToken(user.ID, "admin")

	response.Success(ctx, &response.SuccessResponse{
		Data: map[string]interface{}{
			"token": token,
			"user":  service.NewAdmin().DealAdminUserData(user),
		},
	})
}

func (a *Auth) SendEmailCheck(ctx *gin.Context) {
	input, _ := ginparam.RequestInputs(ctx)
	email := input["email"]
	if err := validate.ValidateMap(map[string]interface{}{
		"email": email,
	}, request.SendEmailRequest); len(err) > 0 {
		errString, _ := json.Marshal(err)
		response.UnprocessableEntity(ctx, string(errString))
		return
	}
	err := service.NewAdmin().SendEmailCheck(email.(string))
	if err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}
	response.Success(ctx, &response.SuccessResponse{
		Message: "Send success",
	})
}

func (a *Auth) SendResetLink(ctx *gin.Context) {
	input, _ := ginparam.RequestInputs(ctx)
	email := input["email"]
	if err := validate.ValidateMap(map[string]interface{}{
		"email": email,
	}, request.SendEmailRequest); len(err) > 0 {
		errString, _ := json.Marshal(err)
		response.UnprocessableEntity(ctx, string(errString))
		return
	}
	err := service.NewAdmin().SendResetLink(email.(string))
	if err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}
	response.Success(ctx, &response.SuccessResponse{
		Message: "Send success",
	})
}

func (a *Auth) User(ctx *gin.Context) {
	user := ctx.MustGet("User").(models.AdminUsers)
	response.Success(ctx, &response.SuccessResponse{
		Data: service.NewAdmin().DealAdminUserData(user),
	})
}

func (a *Auth) TokenById(c *gin.Context) {
	userId := c.Param("id")
	ID, _ := strconv.Atoi(userId)
	token, _ := auth.New().CreateUserToken(ID, "admin")
	response.Success(c, &response.SuccessResponse{
		Data: token,
	})
}
