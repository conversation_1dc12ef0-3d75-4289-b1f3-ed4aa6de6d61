package models

type ShopifyCountries struct {
	Name        string `gorm:"column:name;type:varchar(255);comment:国家名;NOT NULL" json:"name"`
	PhonePrefix string `gorm:"column:phone_prefix;type:varchar(10);comment:手机号前缀;NOT NULL" json:"phone_prefix"`
	ProvinceKey string `gorm:"column:province_key;type:varchar(20);comment:省份表示名;NOT NULL" json:"province_key"`
	Code        string `gorm:"column:code;type:varchar(20);comment:国家简码;NOT NULL" json:"code"`
	Continent   string `gorm:"column:continent;type:varchar(255);comment:大洲;NOT NULL" json:"continent"`
	Zones       string `gorm:"column:zones;type:text;comment:省份列表" json:"zones"`
	Model
}

func (s ShopifyCountries) TableName() string {
	return "shopify_countries"
}
