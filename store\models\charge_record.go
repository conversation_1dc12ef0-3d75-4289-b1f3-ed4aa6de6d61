package models

import (
	"time"
)

type ChargeRecords struct {
	UserId             int       `gorm:"column:user_id;type:int(11);NOT NULL" json:"user_id"`
	ChargeId           int64     `gorm:"column:charge_id;type:bigint(20);comment:费用id;NOT NULL" json:"charge_id"`
	ApplicationCharge  string    `gorm:"column:application_charge;type:text;comment:创建费用响应内容;NOT NULL" json:"application_charge"`
	Status             int       `gorm:"column:status;type:tinyint(4);default:0;comment:是否激活，0/否，1/是;NOT NULL" json:"status"`
	Test               int       `gorm:"column:test;type:tinyint(4);default:0;comment:是否测试;NOT NULL" json:"test"`
	Price              float64   `gorm:"column:price;type:decimal(10,3) unsigned;comment:充值金额;NOT NULL" json:"price"`
	ChargeType         int       `gorm:"column:charge_type;type:tinyint(4);default:0;comment:充值类型0月度30天订阅，1年度，2一次性扣费;NOT NULL" json:"charge_type"`
	DiscountAmount     float64   `gorm:"column:discount_amount;type:decimal(10,3) unsigned;comment:折扣金额;NOT NULL" json:"discount_amount"`
	DiscountPercentage float64   `gorm:"column:discount_percentage;type:decimal(10,2) unsigned;comment:折扣比;NOT NULL" json:"discount_percentage"`
	DiscountInterval   int       `gorm:"column:discount_interval;type:mediumint(9);default:0;comment:优惠轮次;NOT NULL" json:"discount_interval"`
	FreeTrialDays      int       `gorm:"column:free_trial_days;type:int(11);default:0;comment:免费套餐天数;NOT NULL" json:"free_trial_days"`
	ActivatedAt        time.Time `gorm:"column:activated_at;type:datetime;comment:套餐激活时间" json:"activated_at"`
	ExpiredAt          time.Time `gorm:"column:expired_at;type:datetime;comment:收费到期时间" json:"expired_at"`
	Version            string    `gorm:"column:version;type:varchar(6);default:202306;comment:套餐版本;NOT NULL" json:"version"`
	CouponId           int       `gorm:"column:coupon_id;type:int(11);default:0;comment:卡券id;NOT NULL" json:"coupon_id"`
	Model
}

func (ChargeRecords) TableName() string {
	return "charge_records"
}
