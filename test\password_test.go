package main

import (
	"fmt"
	"golang.org/x/crypto/bcrypt"
	"testing"
)

// TestChangePassword 用于测试 ChangePassword 方法
func TestChangePassword(t *testing.T) {
	password := "admin"
	hash, err := bcrypt.GenerateFromPassword([]byte(password), 12)
	if err != nil {
		fmt.Println("Error generating hash:", err)
		return
	}
	fmt.Println("Generated Hash:", string(hash)) // 输出生成的 bcrypt 哈希

	// 测试用生成的哈希来验证密码
	err = bcrypt.CompareHashAndPassword(hash, []byte(password))
	if err != nil {
		fmt.Println("Password does not match")
	} else {
		fmt.Println("Password matches")
	}
}
