package users

import (
	"context"
	"time"

	"tmshopify/internal/domain/entity"
	"tmshopify/store/models"
)

// UserRepository 用户资源
type UserRepository interface {
	Get(ctx context.Context, id int, cols ...string) (*models.Users, error)
	GetLatestUser(ctx context.Context, cols ...string) (*models.Users, error)
	GetUsers(ctx context.Context, cursor int, limit int, cols ...string) ([]*models.Users, error)
	GetGroupStore(ctx context.Context, userID int, cols ...string) (models.ChargeGroupStores, error)
	GetIntegrations(ctx context.Context, userID int, setting string, cols ...string) (*models.UserIntegrations, error)
	GetPaidConversion(ctx context.Context, userID int, cols ...string) (*entity.DataPaidConversionLog, error)
	AddPaidConversion(ctx context.Context, m *entity.DataPaidConversionLog) error
	// GetActiveUsersCreatedAfter 获取指定时间后注册的活跃用户（用于数据同步）
	GetActiveUsersCreatedAfter(ctx context.Context, cursor int, limit int, createdAfter time.Time, cols ...string) ([]*models.Users, error)
}

// UserCacheRepository 用户缓存资源
type UserCacheRepository interface {
	GetTimezone(ctx context.Context, id int) (string, error)
}
