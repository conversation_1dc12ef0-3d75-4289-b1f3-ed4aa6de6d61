package middleware

import (
	"net/http"
	"net/url"
	"strconv"

	"github.com/gin-gonic/gin"

	"tmshopify/internal/application/auth"
	authEntity "tmshopify/internal/domain/entity/auth"
	"tmshopify/internal/infras/config"
	"tmshopify/internal/providers"
	authPkg "tmshopify/pkg/auth"
	"tmshopify/server/response"

	"github.com/channelwill/athena/logger"
)

// AuthMiddleware 认证中间件
func AuthMiddleware(appConfig config.AppConfig, repos *providers.Repositories) gin.HandlerFunc {
	return func(c *gin.Context) {

		// 获取JWT token
		authGuard := authPkg.New()
		payload, token, err := authGuard.JwtShopifyClaims(c, appConfig.Auth.JwtSecret)
		if err != nil {
			logger.Error(c, "JWT Exception: "+err.Error())
			response.Unauthorized(c, "[Server Error] Please reload page")
			c.Abort()
			return
		}

		// 旁路缓存读用户信息
		authService := auth.NewService(repos)

		userAuthInfoCache := repos.UserAuthInfoCache
		// 转为int64,如果sub是admin，就不用转
		var userID int64
		var shop string
		var user *authEntity.UserAuthInfoEntity
		if payload.Sub == "admin" {
			userID, err = strconv.ParseInt(payload.Iss, 10, 64)
		} else if payload.Dest != "" {
			// shopify 鉴权
			parsedURL, _ := url.Parse(payload.Dest)
			shop = parsedURL.Host
		} else {
			userID, err = strconv.ParseInt(payload.Sub, 10, 64)
		}
		if err != nil {
			response.Unauthorized(c, err.Error())
			c.Abort()
			return
		}
		if shop != "" {
			user, err = userAuthInfoCache.GetUserByStore(c, shop)
		} else {
			user, err = userAuthInfoCache.GetUserById(c, userID)
		}
		if err != nil {
			response.Unauthorized(c, err.Error())
			c.Abort()
			return
		}

		if payload.Role != 0 && payload.Sub == "admin" {
			logger.Info(c, "administer login user", map[string]interface{}{
				"role": payload.Role,
				"sub":  payload.Sub,
			})
			c.Set("user_id", user.UserInfo.UserID)
			c.Next()
			return
		}

		// 重新授权
		if user == nil {
			grantUser, err := authService.ReGrantUser(c, auth.ServiceOption{
				ClientID:     appConfig.ShopifyApiKey,
				ClientSecret: appConfig.ShopifyApiSecret,
			}, shop, token)
			if err != nil {
				response.InternalServerError(c, err.Error())
				c.Abort()
				return
			}
			user = grantUser
			user.Session = grantUser.Session
		}

		c.Set("user_id", user.UserInfo.UserID)

		logger.Info(c, "session", map[string]interface{}{
			"shop":  user.UserInfo.StoreName,
			"token": user.UserInfo.Token,
		})

		if user != nil && user.UserInfo.Token != "" {
			if user.Session.AccessToken == "" {
				var session authEntity.Session
				session.AccessToken = user.UserInfo.Token
				session.Scope = appConfig.ClientScope
				err := authService.StoreSession(c, &session)
				if err != nil {
					response.InternalServerError(c, err.Error())
					c.Abort()
					return
				}
			}
			c.Next()
			return
		}

		// TopLevel Redirection
		redirectURL := "/api/auth?shop=" + shop
		topLevelRedirect(c, redirectURL)
	}
}

func topLevelRedirect(c *gin.Context, redirectURL string) {
	c.Redirect(http.StatusFound, redirectURL)
	c.Abort()
}
