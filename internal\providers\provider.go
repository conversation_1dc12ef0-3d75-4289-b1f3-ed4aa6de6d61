package providers

import (
	"gorm.io/gorm"

	authRepo "tmshopify/internal/domain/repo/auth"
	"tmshopify/internal/domain/repo/charges"
	orderRepo "tmshopify/internal/domain/repo/orders"
	statsRepo "tmshopify/internal/domain/repo/stats"
	"tmshopify/internal/domain/repo/users"
	authCache "tmshopify/internal/infras/cache/auth"
	orderCache "tmshopify/internal/infras/cache/order"
	authInfra "tmshopify/internal/infras/persistence/auth"
	chargesInfra "tmshopify/internal/infras/persistence/charges"
	statsInfra "tmshopify/internal/infras/persistence/stats"
	userInfra "tmshopify/internal/infras/persistence/users"
	"tmshopify/store/database"
)

// Repositories 这个providers层可以根据实际情况看是否要添加
// 资源列表
type Repositories struct {
	UserInfoRepo      authRepo.UserInfoRepository
	UserAuthInfoCache authRepo.UserInfoCache
	OrderCountRepo    orderRepo.OrderCountRepository
	FunnelRepo        statsRepo.FunnelRepository
	UserRepo          users.UserRepository
	ChargeRepo        charges.ChargeRepository
}

// NewRepositories 创建 Repositories
func NewRepositories(db *gorm.DB, redisClient *database.Redis) *Repositories {

	userInfoRepo := authInfra.NewUserInfoRepo(db)

	userAuthInfoCache := authCache.NewUserAuthInfoCache(redisClient, userInfoRepo)

	orderCountRepo := orderCache.NewCountCache(redisClient)

	funnelRepo := statsInfra.NewFunnelRepository(db)

	userRepo := userInfra.NewUserRepository(db)

	chargeRepo := chargesInfra.NewChargeRepository(db)

	r := &Repositories{
		UserInfoRepo:      userInfoRepo,
		UserAuthInfoCache: userAuthInfoCache,
		OrderCountRepo:    orderCountRepo,
		FunnelRepo:        funnelRepo,
		UserRepo:          userRepo,
		ChargeRepo:        chargeRepo,
	}

	return r
}
