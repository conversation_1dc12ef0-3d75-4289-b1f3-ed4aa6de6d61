package middleware

import (
	"log"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/ulule/limiter/v3"
	mgin "github.com/ulule/limiter/v3/drivers/middleware/gin"
	"github.com/ulule/limiter/v3/drivers/store/memory"

	"tmshopify/server/response"
)

// RequestLimiter 创建基于路由模式的限流器，每个路由使用独立的限流器
func RequestLimiter(rateFormat string) gin.HandlerFunc {
	// 为当前路由创建独立的 store
	store := memory.NewStore()

	// 创建速率
	rate, err := limiter.NewRateFromFormatted(rateFormat)
	if err != nil {
		log.Fatal(err)
		return nil
	}

	// 创建限流器实例
	lmt := limiter.New(store, rate)

	// 日志打印锁，确保只打印一次
	var once sync.Once
	var routePath string

	// 创建中间件处理函数
	middleware := mgin.NewMiddleware(
		lmt,
		mgin.WithLimitReachedHandler(ExceededHandler),
		mgin.WithKeyGetter(func(c *gin.Context) string {
			// 所有请求共享同一个键，按路由模式限流
			return c.Request.URL.Path
		}),
	)

	// 返回处理函数
	return func(c *gin.Context) {
		// 只打印一次日志
		once.Do(func() {
			routePath = c.FullPath()
			logrus.Infof("为路径 %s 创建API限流器，速率: %s", routePath, rateFormat)
		})

		// 执行中间件
		middleware(c)
	}
}

func ExceededHandler(c *gin.Context) {
	response.BadTooManyRequest(c, "Too many requests")
}
