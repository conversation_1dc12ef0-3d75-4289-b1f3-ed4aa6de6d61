package service

import (
	"fmt"
	"time"
	"tmshopify/config"
	"tmshopify/pkg/shopify/webhook"
	"tmshopify/store/database"
	"tmshopify/store/repo/impl"
)

type FlowTrigger struct {
	flowTriggerRep *impl.ShopifyFlowTrigger
	userRep        *impl.User
}
type FlowTriggerService interface {
	SaveTrigger(data webhook.FlowWebhook) error
}

func (f *FlowTrigger) SaveTrigger(data webhook.FlowWebhook) error {
	err := f.flowTriggerRep.SaveFlowTrigger(data)
	if err == nil {
		user, _ := f.userRep.FirstActiveUserIdByShop(data.ShopifyDomain)
		if user.ID != 0 {
			enable := "0"
			if data.HasEnabledFlow {
				enable = "1"
			}
			err := database.RS.SetWithExpire(fmt.Sprintf("%s%d", config.CacheKey.ShopifyFlowTriggerEnabled, user.ID), enable, time.Second*3600)
			if err != nil {
				return err
			}
		}
	}
	return err
}

func NewFlowTriggerService() FlowTriggerService {
	return &FlowTrigger{
		flowTriggerRep: &impl.ShopifyFlowTrigger{},
		userRep:        &impl.User{},
	}
}
