package models

type LevelsPermissions struct {
	Level        uint   `gorm:"column:level;type:int(10) unsigned;default:0;comment:套餐等级，默认free;NOT NULL" json:"level"`
	PermissionId uint   `gorm:"column:permission_id;type:int(10) unsigned;NOT NULL" json:"permission_id"`
	Version      string `gorm:"column:version;type:char(6);default:202306;NOT NULL" json:"version"`
	Model
}

func (LevelsPermissions) TableName() string {
	return "levels_permissions"
}
