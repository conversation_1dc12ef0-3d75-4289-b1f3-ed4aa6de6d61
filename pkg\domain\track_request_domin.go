package domain

import (
	"net/mail"
	"strings"

	"tmshopify/config"
	"tmshopify/internal/app/request"
)

const (
	TrackingJsVersion  = "202505291"
	TrackingCssVersion = "202503111"
	PreviewEmail       = "<EMAIL>"
	PreviewNumber      = "TBA355969054000"
	PreviewOrder       = "#1234"
)

// AdvanceFillPreview 访问查询页面预填充预览单号信息的店铺列表
var AdvanceFillPreview = []string{
	"trackingmore-test.myshopify.com", "trackingmore-5.myshopify.com",
}

type TrackRequestDomain struct {
	Shop        string
	OrderName   string
	Email       string
	Phone       string
	TrackNumber string
	Preview     bool
	Lang        string
	IsBase64    bool
	IsAjax      bool
	IsEmbed     bool
	Callback    string
	Url         struct {
		Css string
		Js  string
	}
	ResourcePath string
	PhoneEmail   string
}

func HandleTrackRequest(params request.TrackRequest) TrackRequestDomain {
	resourcePath := "https://" + config.Get().ServerDomain + "/static"
	shop := params.Shop
	// 获取提交的参数
	//订单号
	orderName := strings.TrimSpace(params.Order)
	//邮箱
	email := strings.TrimSpace(params.Email)
	token := strings.TrimSpace(params.Token)
	if email == "" {
		email = token
	}
	//电话
	phone := strings.TrimSpace(params.Phone)
	//电话或邮箱 (合并查询)
	phoneOrEmail := params.PhoneEmail
	if phoneOrEmail != "" {
		if _, err := mail.ParseAddress(phoneOrEmail); err == nil {
			email = phoneOrEmail
		} else {
			phone = phoneOrEmail
		}
	}
	//单号
	trackNumber := strings.TrimSpace(params.Nums)
	//预览参数
	lang := params.PageLang
	css := resourcePath + "/css/??track_page/classic.css,leaflet.css?time=" + TrackingCssVersion
	js := resourcePath + "/js/??leaflet.js,track_page_classic.js?time=" + TrackingJsVersion
	return TrackRequestDomain{
		Shop:        shop,
		OrderName:   orderName,
		Email:       email,
		Phone:       phone,
		TrackNumber: trackNumber,
		Preview:     params.IsPreview,
		Lang:        lang,
		IsBase64:    params.IsBase64,
		IsAjax:      params.IsAjax,
		IsEmbed:     params.IsEmbed,
		Callback:    params.Callback,
		Url: struct {
			Css string
			Js  string
		}{css, js},
		ResourcePath: resourcePath,
		PhoneEmail:   phoneOrEmail,
	}
}
