package models

import "time"

type DataPromotionClickLogs struct {
	CreateDate time.Time `gorm:"column:create_date;type:DATE;comment:当天日期;not null;" json:"create_date"`              // 当天日期
	AppName    string    `gorm:"column:app_name;type:VARCHAR(255);comment:app 名称;not null;" json:"app_name"`          // app 名称
	Clicks     int       `gorm:"column:clicks;type:INT(11);comment:点击次数;not null;default:0;" json:"clicks"`           // 点击次数
	Source     int       `gorm:"column:source;type:TINYINT(3) UNSIGNED;comment:来源;not null;default:1;" json:"source"` // 来源
	Model
}

func (DataPromotionClickLogs) TableName() string {
	return "data_promotion_click_logs"
}
