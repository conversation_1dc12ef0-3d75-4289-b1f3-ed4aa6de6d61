package repo

import (
	"context"

	entity "tmshopify/internal/domain/entity/auth"
)

// UserInfoRepository 用户信息仓库
type UserInfoRepository interface {
	// GetUserByStoreName 根据店铺名称获取用户信息
	GetUserByStoreName(ctx context.Context, storeName string) (*entity.UserAuthInfoEntity, error)
	// GetUserByID 根据用户ID获取用户信息
	GetUserByID(ctx context.Context, id int64) (*entity.UserAuthInfoEntity, error)
	// GetSessionByID 根据ID获取会话信息
	GetSessionByID(ctx context.Context, id int64) (*entity.Session, error)
	// StoreSession 存储会话信息
	StoreSession(ctx context.Context, session *entity.Session) error
	// ReGrantUser 重新授权用户
	ReGrantUser(ctx context.Context, clientID string, clientSecret string, shop string, token string) (*entity.UserAuthInfoEntity, error)
}

// UserInfoCache UserAuthInfoCache 用户信息缓存
type UserInfoCache interface {
	// GetUserById 获取用户信息缓存
	GetUserById(ctx context.Context, userID int64) (*entity.UserAuthInfoEntity, error)
	// GetUserByStore 获取用户信息缓存
	GetUserByStore(ctx context.Context, storeName string) (*entity.UserAuthInfoEntity, error)
	// Delete 删除用户信息缓存
	Delete(ctx context.Context, userID int64) error
}
