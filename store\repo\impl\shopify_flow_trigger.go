package impl

import (
	"tmshopify/pkg/shopify/webhook"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type ShopifyFlowTrigger struct {
	Helper
}

func (s *ShopifyFlowTrigger) SaveFlowTrigger(data webhook.FlowWebhook) error {
	var trigger models.ShopifyFlowTriggers

	condition := map[string]interface{}{
		"trigger_id":  data.FlowTriggerDefinitionId,
		"shop_domain": data.ShopifyDomain,
	}
	var enable int32 = 0
	if data.HasEnabledFlow {
		enable = 1
	}
	attributes := map[string]interface{}{
		"has_enabled_flow": enable,
	}
	// 尝试查找记录
	result := database.DB.Where(condition).First(&trigger)
	// 如果找到了记录，则更新它
	if result.Error == nil {
		if err := database.DB.Model(&trigger).Updates(attributes).Error; err != nil {
			return err
		}
	} else if result.RowsAffected == 0 {
		// 如果未找到记录，则创建新的记录
		newTrigger := models.ShopifyFlowTriggers{
			TriggerId:      data.FlowTriggerDefinitionId,
			ShopDomain:     data.ShopifyDomain,
			HasEnabledFlow: enable,
		}
		if err := database.DB.Create(&newTrigger).Error; err != nil {
			return err
		}
	}
	return nil
}
