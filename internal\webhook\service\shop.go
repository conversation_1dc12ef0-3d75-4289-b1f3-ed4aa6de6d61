package service

import (
	"encoding/json"
	"io"
	"tmshopify/config"
	"tmshopify/pkg/shopify"
	"tmshopify/pkg/shopify/webhook"
	"tmshopify/pkg/utils/helper"
	"tmshopify/store/database"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type Shop struct {
}

type ShopService interface {
	AppUninstalled(c *gin.Context) bool
	ShopUpdate(c *gin.Context) bool
	ShopRedact(c *gin.Context) bool
	CustomersRedact(c *gin.Context) bool
}

type ShopPayload struct {
	ShopDomain string `json:"shop_domain"`
}

type ShopWebhook struct {
	Shop string       `json:"shop"`
	Data shopify.Shop `json:"data"`
}

func (s *Shop) AppUninstalled(c *gin.Context) bool {
	logrus.Warnf("App was uninstalled from %s - removing all sessions", c.GetHeader(webhook.XShopifyDomain))
	var shopWebhook ShopWebhook
	shopWebhook.Shop = c.GetHeader(webhook.XShopifyDomain)
	if err := c.ShouldBind(&shopWebhook.Data); err != nil {
		logrus.Warn("the body should be shopify shop, error:" + err.Error() + " shop is:" + shopWebhook.Shop)
		helper.CallWilding(err.Error())
	}
	shopJson, _ := json.Marshal(shopWebhook)
	_, redisErr := database.RS.SAdd(config.CacheKey.ShopUninstalledQueue, string(shopJson))
	if redisErr != nil {
		logrus.Warn("redis set error " + redisErr.Error())
		return false
	}
	return true
}

func (s *Shop) ShopUpdate(c *gin.Context) bool {
	logrus.Warnf("Shop:%s was updated", c.GetHeader(webhook.XShopifyDomain))
	var shopWebhook ShopWebhook
	shopWebhook.Shop = c.GetHeader(webhook.XShopifyDomain)
	if err := c.ShouldBind(&shopWebhook.Data); err != nil {
		logrus.Warn("the body should be shopify shop,err:" + err.Error())
		helper.CallWilding(err.Error())
	}
	shopJson, _ := json.Marshal(shopWebhook)
	_, redisErr := database.RS.SAdd(config.CacheKey.ShopUpdateQueue, string(shopJson))
	if redisErr != nil {
		logrus.Warn("redis sadd error " + redisErr.Error())
		return false
	}
	return true
}

func (s *Shop) ShopRedact(c *gin.Context) bool {
	logrus.Warnf("Shop:%s was redact", c.GetHeader(webhook.XShopifyDomain))
	var shopPayload ShopPayload
	if err := c.ShouldBind(&shopPayload); err != nil {
		logrus.Warn("the body should has shopify shop domain,err:" + err.Error())
	}

	_, redisErr := database.RS.SAdd(config.CacheKey.ShopRedactQueue, shopPayload.ShopDomain)
	if redisErr != nil {
		logrus.Warn("redis sadd error " + redisErr.Error())
		return false
	}
	return true
}

func (s *Shop) CustomersRedact(c *gin.Context) bool {
	logrus.Warnf("Shop:%s's customer or data was redact", c.GetHeader(webhook.XShopifyDomain))
	body, _ := io.ReadAll(c.Request.Body)
	logrus.Warn("customer payload is:" + string(body))
	return true
}

func NewShop() *Shop {
	return &Shop{}
}
