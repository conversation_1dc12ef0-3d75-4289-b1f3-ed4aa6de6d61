package impl

import (
	"time"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type PaidStatistic struct {
	Helper
}

func (p *PaidStatistic) FirstByMinId() (*models.DataPaidStatistics, error) {
	var m *models.DataPaidStatistics
	result := database.DB.First(&m)
	return m, result.Error
}

func (p *PaidStatistic) GetTotalPaid(startTime time.Time, endTime time.Time) ([]struct {
	Date  string
	Count int
}, error) {
	var column []struct {
		Date  string
		Count int
	}
	results := database.DB.Model(&models.DataPaidStatistics{}).
		Select("date_format(create_date,\"%Y-%m-%d\") as Date, total_paid as Count").
		Where("create_date between ? and ?", startTime, endTime).
		Order("create_date").
		Scan(&column)

	return column, results.Error
}

func (p *PaidStatistic) GetTotalPrice(startTime time.Time, endTime time.Time) ([]struct {
	Date  string
	Price float64
	Total int
}, error) {
	var column []struct {
		Date  string
		Price float64
		Total int
	}
	results := database.DB.Model(&models.DataPaidStatistics{}).
		Select("date_format(create_date,\"%Y-%m-%d\") as Date, total_paid_price as Price, total_paid as Total").
		Where("create_date between ? and ?", startTime, endTime).
		Order("create_date").
		Scan(&column)

	return column, results.Error
}

func (p *PaidStatistic) GetTotalUser(startTime time.Time, endTime time.Time) ([]struct {
	Date    string
	Install int
}, error) {
	var column []struct {
		Date    string
		Install int
	}
	results := database.DB.Model(&models.DataPaidStatistics{}).
		Select("date_format(create_date,\"%Y-%m-%d\") as Date, total_install as Install").
		Where("create_date between ? and ?", startTime, endTime).
		Order("create_date").
		Scan(&column)

	return column, results.Error
}

func (p *PaidStatistic) GetUserGrowthData(startTime time.Time, endTime time.Time) ([]struct {
	Date      string
	Install   int
	UnInstall int
}, error) {
	var column []struct {
		Date      string
		Install   int
		UnInstall int
	}
	results := database.DB.Model(&models.DataPaidStatistics{}).
		Select("date_format(create_date,\"%Y-%m-%d\") as Date, new_install+reopened_num as Install, uninstall_num+close_store_num as UnInstall").
		Where("create_date between ? and ?", startTime, endTime).
		Order("create_date").
		Scan(&column)

	return column, results.Error
}

func (p *PaidStatistic) GetSubscriberUserTotalUser(startTime time.Time, endTime time.Time) ([]struct {
	Date    string
	NewPaid int
	Resume  int
	Cancel  int
}, error) {
	var column []struct {
		Date    string
		NewPaid int
		Resume  int
		Cancel  int
	}
	results := database.DB.Model(&models.DataPaidStatistics{}).
		Select("date_format(create_date,\"%Y-%m-%d\") as Date, new_paid as NewPaid, resume_num as Resume, cancel_num as Cancel").
		Where("create_date between ? and ?", startTime, endTime).
		Order("create_date").
		Scan(&column)

	return column, results.Error
}
