package models

// OrderViewSet 订单视图
type OrderViewSet struct {
	Model

	UserID                int    `json:"user_id" gorm:"column:user_id"`
	Page                  int    `json:"page" gorm:"column:page"`
	PageSize              int    `json:"page_size" gorm:"column:page_size"`
	Name                  string `json:"name" gorm:"column:name"`
	Keywords              string `json:"keywords" gorm:"column:keywords"`
	Status                int8   `json:"status" gorm:"column:status"`
	StatusMulti           string `json:"status_multi" gorm:"column:status_multi"`
	SubStatus             string `json:"sub_status" gorm:"column:sub_status"`
	TimeRange             string `json:"time_range" gorm:"column:time_range"`
	DateFilter            string `json:"date_filter" gorm:"column:date_filter"`
	Couriers              string `json:"couriers" gorm:"column:couriers"`
	Destinations          string `json:"destinations" gorm:"column:destinations"`
	TransitTime           string `json:"transit_time" gorm:"column:transit_time"`
	OrderSort             int8   `json:"order_sort" gorm:"column:order_sort"`
	TableType             int8   `json:"table_type" gorm:"column:table_type"`
	CreatedDateFilter     string `json:"created_date_filter" gorm:"column:created_date_filter"`
	LastUpdatedDateFilter string `json:"last_updated_date_filter" gorm:"column:last_updated_date_filter"`
	CreatedRange          string `json:"created_range" gorm:"column:created_range"`
	LastUpdatedRange      string `json:"last_updated_range" gorm:"column:last_updated_range"`
	Origins               string `json:"origins" gorm:"column:origins"`
	HasNote               string `json:"has_note" gorm:"column:has_note"`
	FulfillmentStatus     int8   `json:"fulfillment_status" gorm:"column:fulfillment_status"`
}

// TableName table name of OrderViewSet
func (*OrderViewSet) TableName() string {
	return "order_view_sets"
}
