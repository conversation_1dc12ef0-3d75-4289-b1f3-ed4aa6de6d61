package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"tmshopify/config"
	"tmshopify/internal/app/entity"
	"tmshopify/internal/app/request"
	"tmshopify/internal/app/service"
	"tmshopify/internal/app/utils"
	"tmshopify/pkg/domain"
	"tmshopify/pkg/utils/validator"
	"tmshopify/server/response"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type Checkout struct {
}

func (c *Checkout) GetVersion(ctx *gin.Context) {
	// 有更新版本的时候 请修改以下参数 开始
	jsVersion := "20240702"
	jsName := "20200612"
	cssVersion := "20230313111"
	cssName := "20200612"

	jsList := []string{
		service.NewCheckoutService().GetCheckOutLinkByName(jsName, jsVersion, false),
	}

	cssList := []string{
		service.NewCheckoutService().GetCheckOutLinkByName(cssName, cssVersion, true),
	}

	response.Success(ctx, &response.SuccessResponse{
		Data: gin.H{
			"js":  jsList,
			"css": cssList,
		},
	})
}

func (c *Checkout) GetSetting(ctx *gin.Context) {
	result := validator.DefaultMessageValidate(ctx, &request.CheckoutSetting{})
	if len(result) > 0 {
		response.UnprocessableEntity(ctx, result)
		return
	}
	// 获取订单号
	orderName, _ := ctx.GetQuery("order_name")
	if orderName == "" {
		orderName, _ = ctx.GetQuery("order_id")
	}
	// 获取 shopify 店铺名称
	shopName, _ := ctx.GetQuery("shop")
	// 获取快递单号
	trackNumber, _ := ctx.GetQuery("nums")
	// 匹配订单号 删除空格
	orderName = strings.TrimSpace(strings.TrimLeft(orderName, "Order"))
	// 匹配特殊订单号
	re := regexp.MustCompile(` (#?.*\d{2}.*)$`)
	match := re.FindStringSubmatch(orderName)
	if len(match) > 1 {
		orderName = match[1]
	}
	// 如果没有必要参数存在
	if orderName == "" || shopName == "" {
		response.NotFound(ctx, "params can not be empty")
		return
	}
	// 测试单号直接返回数据
	if orderName == "#1001" && shopName == "trackingmore-test.myshopify.com" {
		replaceNumberHrefArr := map[string]string{trackNumber: fmt.Sprintf("https://%s/apps/trackingmore?nums=%s", shopName, trackNumber)}
		data := service.NewCheckoutService().GetCheckoutReturnData(replaceNumberHrefArr, "", "")
		response.Success(ctx, &response.SuccessResponse{
			Data: data,
		})
		return
	}
	// 测试单号直接返回数据
	if orderName == "#1004" && shopName == "trackingmore-test.myshopify.com" {
		trackLinkText := "Click here to get shipping updates"
		trackLinkHref := fmt.Sprintf("https://%s/apps/trackingmore?nums=9400110200830505412663", shopName)
		data := service.NewCheckoutService().GetCheckoutReturnData(map[string]string{}, trackLinkText, trackLinkHref)
		response.Success(ctx, &response.SuccessResponse{
			Data: data,
		})
		return
	}
	// 检查用户是否开启了此功能
	checkoutSetting := service.NewCheckoutService().GetCheckoutSetting(shopName)
	userId := checkoutSetting.UserId
	// 用户不存在 返回错误信息
	if userId == 0 {
		response.NotFound(ctx, "shop is not exist")
		return
	}
	// 获取用户设置 获取查询后缀
	checkPageSet := service.NewCheckoutService().GetUserTrackPageMessage(userId)
	if checkPageSet.ID != 0 {
		trackUrl := checkPageSet.TrackLink
		if trackUrl == "" {
			trackUrl = "apps/trackingmore"
		}
		// 链接替换功能开关
		replaceNumberOn := checkoutSetting.ReplaceNumberOn == 1
		// 追加查询链接功能开关
		trackLinkOn := checkoutSetting.TrackLinkOn == 1
		// 自定义链接的文本内容
		trackLinkText := checkoutSetting.TrackLinkText
		if trackLinkText == "" {
			trackLinkText = "Click here to get shipping updates"
		}
		// 如果没有开启任何一个功能
		if !trackLinkOn && !replaceNumberOn {
			response.Success(ctx, &response.SuccessResponse{
				Data:    "This feature is off",
				Message: "Created",
				Code:    http.StatusCreated,
			})
			return
		}
		// 继续检查此用户是否绑定了 shopify 店铺或者是否卸载 提取用户的真实域名
		checkUser := checkPageSet.User
		if checkUser.ID == 0 {
			logrus.Error("missing user data", checkPageSet)
			response.NotFound(ctx, "Request data does not exist")
			return
		}
		// 当前用户的真实域名
		shopRealName := checkUser.StoreRealName
		if shopRealName == "" {
			shopRealName = shopName
		}

		//检查订单号是否存在数据库中
		checkOrder := service.NewCheckoutService().CheckOrder(userId, orderName)
		// 如果订单不存在
		if checkOrder.ID == 0 {
			response.NotFound(ctx, "order is not exist")
			return
		}
		// 提取邮箱和快递单号
		customerEmail := checkOrder.CustomerEmail
		numberList := service.NewCheckoutService().GetTrackNumberList(userId, checkOrder.OrderId)
		// 如果开启了追加链接功能 需要检查单号是否存在
		trackLinkHref := ""
		addOnType := 0
		addOnSetting := make(map[string]interface{})
		if trackLinkOn {
			// 加密
			orderName = url.QueryEscape(orderName)
			// 链接参数列表
			params := []string{"order=" + orderName, "email=" + customerEmail}
			trackNumber = ""
			if len(numberList) != 0 {
				trackNumber = strings.TrimSpace(numberList[0])
			}

			//如果用户没有邮箱 但是有单号 那么将单号作为查询链接
			if customerEmail == "" && trackNumber != "" {
				params = []string{"nums=" + trackNumber}
			}
			// 单号的查询链接
			trackLinkHref = fmt.Sprintf("https://%s/%s/?%s", shopRealName, trackUrl, strings.Join(params, "&"))

			userRepo := &impl.User{}
			permission := userRepo.GetPermissionByUser(checkUser)
			addOnType = checkoutSetting.AddOnType
			if !permission.VerifyPermission(domain.OrderStatusCard) {
				addOnType = 2
			}
			if addOnType == 1 {
				// 订单状态
				orderHas := 0
				if checkOrder.CustomStatusTime != "" {
					var customStatusTime map[int]int
					_ = json.Unmarshal([]byte(checkOrder.CustomStatusTime), &customStatusTime)
					for k, v := range []int{1001, 1100, 2, 3, 4} {
						if customStatusTime[v] != 0 {
							orderHas = k
						}
					}
				}
				if orderHas == 0 {
					statusKeyArr := utils.GetStatusKey([]config.StatusNodeNumber{})
					statusSetStart := statusKeyArr[0]
					statusSetEnd := statusKeyArr[len(statusKeyArr)-1]
					// 检查此单号是否已经发货
					orderFulfillTime := 0
					orderStatus := 0
					if trackNumber != "" {
						orderFulfillTime, orderStatus = service.NewCheckoutService().GetOrderFulfillTime(userId, trackNumber)
					}
					if orderFulfillTime == 0 {
						orderHas = int(statusSetStart)
					} else {
						orderHas = int(statusSetEnd)
					}
					customTrackStatus := checkOrder.CustomTrackStatus
					if customTrackStatus != 0 && customTrackStatus != config.OrderedNodeStatusNumber && customTrackStatus != config.OrderReadyStatusNumber {
						orderHas = int(customTrackStatus)
					}
					if orderStatus != 0 {
						orderHas = orderStatus
					}
				}
				var userAddOnSetting impl.OrderStatusSetting
				if err := json.Unmarshal([]byte(checkoutSetting.OrderStatusSetting), &userAddOnSetting); err != nil {
					logrus.Error("orderStatusSetting json parsing error ", err)
				}
				// order status page 静态文本设置 start
				estimatedDeliveryTimeText := ""
				trackingNumberText := ""
				if userAddOnSetting.ExtraContents.EstimatedDeliveryTime != 0 {
					estimatedDeliveryTimeText = userAddOnSetting.Others.EstimatedDeliveryTime
				}
				if userAddOnSetting.ExtraContents.TrackingNumber != 0 {
					trackingNumberText = userAddOnSetting.Others.TrackingNumber
				}
				addOnSetting = map[string]interface{}{
					"estimatedDeliveryTime": estimatedDeliveryTimeText,
					"trackingNumberText":    trackingNumberText,
					"trackingNumber":        trackNumber,
					"buttonText":            userAddOnSetting.Others.TrackYourOrder,
					"trackButtonColor":      userAddOnSetting.TrackButtonColor,
				}
				if orderHas == 1001 {
					addOnSetting["status"] = userAddOnSetting.Status.Ordered
					addOnSetting["description"] = userAddOnSetting.Description.Ordered
				}
				if orderHas == 1100 || orderHas == 1 {
					addOnSetting["status"] = userAddOnSetting.Status.Fulfilled
					addOnSetting["description"] = userAddOnSetting.Description.Fulfilled
				}
				if orderHas >= 2 && orderHas <= 4 {
					addOnSetting["status"] = userAddOnSetting.Status.HasShippingUpdate
					addOnSetting["description"] = userAddOnSetting.Description.HasShippingUpdate
				}
				// order status page 静态文本设置 end

				// 显示的 ETA start
				var shippingTimeOn string
				if trackNumber != "" {
					trackInfo := service.NewCheckoutService().GetTrackInfoByNumber(userId, trackNumber)
					if trackInfo.ID != 0 && !trackInfo.ExpectedDeliveryTime.IsZero() {
						shippingTimeOn = trackInfo.ExpectedDeliveryTime.Format("01/02, 2006")
					}
				}
				if shippingTimeOn == "" {
					var orderTrack models.OrderTracks
					for _, track := range checkOrder.OrderTracks {
						if track.TrackNumber == trackNumber {
							orderTrack = track
							break
						}
					}
					eddSetting := entity.HandleEddSetting(userId)
					eddService := service.NewEddService()
					calculateEntity := eddService.GetCalculateEntity(eddSetting, service.TrackCalculateData{
						UserId:           orderTrack.UserId,
						OrderCreateTime:  orderTrack.OrderCreateTime,
						OrderFulfillTime: orderTrack.OrderFulfillTime,
						Destination:      orderTrack.Destination,
						Courier:          orderTrack.Courier,
						TrackNumber:      orderTrack.TrackNumber,
						FulfillmentId:    orderTrack.FulfillmentId,
					})
					dataShow := eddService.CalculateEddByOrderAndDestinationByEntities(userId,
						calculateEntity, eddSetting)
					if dataShow.StartShow != 0 && dataShow.EndShow != 0 {
						shippingTimeOn = fmt.Sprintf("%s - %s", time.Unix(dataShow.StartShow, 0).Format("01/02"), time.Unix(dataShow.EndShow, 0).Format("01/02, 2006"))
						if userId == 26237 {
							shippingTimeOn = fmt.Sprintf("%s - %s", time.Unix(dataShow.StartShow, 0).Format("02/01/2006"), time.Unix(dataShow.EndShow, 0).Format("02/01/2006"))
						}
					}
				}
				addOnSetting["shippingTimeCon"] = shippingTimeOn
				// 显示的 ETA end
			}
			if addOnType == 2 {
				linkColor := config.OrderStatusSetting["link"]["track_link_color"]
				addOnSetting = map[string]interface{}{
					"trackLinkColor": linkColor,
				}
				if checkoutSetting.TrackLinkColor != "" {
					addOnSetting["trackLinkColor"] = checkoutSetting.TrackLinkColor
				}
			}
		}
		// 如果开启了单号链接替换功能
		replaceNumberHrefArr := make(map[string]string)
		if replaceNumberOn {
			if len(numberList) != 0 {
				for _, number := range numberList {
					if number != "" {
						replaceNumberHrefArr[number] = fmt.Sprintf("https://%s/%s/?nums=%s", shopRealName, trackUrl, number)
					}
				}
			} else {
				replaceNumberHrefArr[trackNumber] = fmt.Sprintf("https://%s/%s/?nums=%s", shopRealName, trackUrl, trackNumber)
			}
		}
		// 当前自定义的查询链接
		data := service.NewCheckoutService().GetCheckoutReturnData(replaceNumberHrefArr, trackLinkText, trackLinkHref)
		data["addOnSetting"] = addOnSetting
		data["addOnType"] = addOnType
		response.Success(ctx, &response.SuccessResponse{
			Data: data,
		})
		return
	}
	response.Success(ctx, &response.SuccessResponse{
		Data:    "This feature is off",
		Message: "Created",
		Code:    http.StatusCreated,
	})
}

// GetOrderStatus 获取 order status 插件数据
func (c *Checkout) GetOrderStatus(ctx *gin.Context) {
	result := validator.DefaultMessageValidate(ctx, &request.OrderStatusWidgetRequest{})
	if len(result) > 0 {
		response.UnprocessableEntity(ctx, result)
		return
	}
	shopName, _ := ctx.GetQuery("shop")
	orderName, _ := ctx.GetQuery("order_name")

	// 如果没有必要参数存在
	if orderName == "" || shopName == "" {
		response.NotFound(ctx, "params can not be empty")
		return
	}

	checkoutSetting := service.NewCheckoutService().GetCheckoutSetting(shopName)
	userId := checkoutSetting.UserId
	// 用户不存在 返回错误信息
	if userId == 0 {
		response.NotFound(ctx, "shop is not exist")
		return
	}

	// 获取查询后缀
	checkPageSet := service.NewCheckoutService().GetUserTrackPageMessage(userId)
	checkUser := checkPageSet.User
	if checkUser.ID == 0 {
		logrus.Error("missing user data", checkPageSet)
		response.NotFound(ctx, "Request data does not exist")
		return
	}

	// 获取订单信息
	data := service.NewCheckoutService().GetOrderStatusWidgetText(checkUser, orderName, checkoutSetting)

	response.Success(ctx, &response.SuccessResponse{
		Data: data,
	})
}
