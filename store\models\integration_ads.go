package models

import (
	"database/sql"
	"time"
)

type IntegrationAds struct {
	Image        string       `gorm:"column:image;type:VARCHAR(255);comment:卡片宣传图或logo;not null;" json:"image"`                      // 卡片宣传图或logo
	Title        string       `gorm:"column:title;type:VARCHAR(255);comment:类型0展示的标题;not null;" json:"title"`                        // 类型0展示的标题
	Description  string       `gorm:"column:description;type:TEXT(65535);comment:类型0展示的描述;default:NULL;" json:"description"`         // 类型0展示的描述
	BgColor      string       `gorm:"column:bg_color;type:VARCHAR(20);comment:卡片的背景色;not null;default:#ffffff;" json:"bg_color"`     // 卡片的背景色
	ReferralLink string       `gorm:"column:referral_link;type:TEXT(65535);comment:推广链接;default:NULL;" json:"referral_link"`         // 推广链接
	Style        int          `gorm:"column:style;type:TINYINT(1);comment:广告样式;not null;default:0;" json:"style"`                    // 广告样式
	Sort         int          `gorm:"column:sort;type:INT;comment:排序;not null;default:0;" json:"sort"`                               // 广告样式
	ShowAt       time.Time    `gorm:"column:show_at;type:TIMESTAMP;comment:上架时间;not null;default:CURRENT_TIMESTAMP;" json:"show_at"` // 上架时间
	DraftAt      sql.NullTime `gorm:"column:draft_at;type:TIMESTAMP;comment:下架时间;default:NULL;" json:"draft_at"`                     // 下架时间
	Model
}

func (IntegrationAds) TableName() string {
	return "integration_ads"
}
