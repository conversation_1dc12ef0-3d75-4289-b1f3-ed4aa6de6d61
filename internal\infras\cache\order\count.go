package cache

import (
	"context"
	"fmt"
	"strconv"

	orderRepo "tmshopify/internal/domain/repo/orders"
	"tmshopify/internal/infras/cache"
	"tmshopify/store/database"
)

var _ orderRepo.OrderCountRepository = (*OrderCountCache)(nil)

// OrderCountCache 订单数量缓存
type OrderCountCache struct {
	redisClient *database.Redis
}

// NewCountCache 创建订单数量缓存
func NewCountCache(redisClient *database.Redis) *OrderCountCache {
	return &OrderCountCache{
		redisClient: redisClient,
	}
}

// GetCount 获取订单数量
func (c *OrderCountCache) GetCount(ctx context.Context, userID int64) (int64, int64, error) {
	userIDStr := strconv.FormatInt(userID, 10)
	// 获取订单数量
	key := fmt.Sprintf("%s%s%s", cache.RedisShopifyPrefix, cache.UserSyncScheduleOrderCount, userIDStr)
	countByte, err := c.redisClient.Get(key)
	if err != nil {
		return 0, 0, err
	}
	count, err := strconv.ParseInt(string(countByte), 10, 64)
	if err != nil {
		return 0, 0, err
	}

	// 获取已处理订单数量
	key = fmt.Sprintf("%s%s%s", cache.RedisShopifyPrefix, cache.UserSyncScheduleOrderProcessed, userIDStr)
	processedCountByte, err := c.redisClient.Get(key)
	if err != nil {
		return 0, 0, err
	}
	processedCount, err := strconv.ParseInt(string(processedCountByte), 10, 64)
	if err != nil {
		return 0, 0, err
	}
	return count, processedCount, nil
}
