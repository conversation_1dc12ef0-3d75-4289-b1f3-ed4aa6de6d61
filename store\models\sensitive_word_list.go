package models

import "database/sql"

type SensitiveWordLists struct {
	Pid    uint           `gorm:"column:pid;type:int(10) unsigned;default:0;comment:上一级ID（实现无限级分类）0：表示没有上级，;NOT NULL" json:"pid"`
	CnName sql.NullString `gorm:"column:cn_name;type:varchar(255);comment:中文名 暂时无意义的字段" json:"cn_name"`
	EnName sql.NullString `gorm:"column:en_name;type:varchar(255);comment:英文名" json:"en_name"`
	Model
}

func (SensitiveWordLists) TableName() string {
	return "sensitive_word_lists"
}
