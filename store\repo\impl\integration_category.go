package impl

import (
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type IntegrationCategory struct {
	Helper
}

func (i *IntegrationCategory) Get() ([]models.IntegrationCategories, error) {
	var items []models.IntegrationCategories
	result := database.DB.Find(&items)
	return items, result.Error
}

func (i *IntegrationCategory) Create(m *models.IntegrationCategories) error {
	result := database.DB.Create(m)
	return result.Error
}

func (i *IntegrationCategory) Update(id string, fields map[string]interface{}) error {
	return i.UpdateByMap(&models.IntegrationCategories{}, id, fields)
}

func (i *IntegrationCategory) IsUniqueCategoryByNameAndId(name string, id string) bool {
	var count int64
	if len(id) > 0 {
		database.DB.Model(&models.IntegrationCategories{}).Where("name =? and id!=? ", name, id).Count(&count)
	} else {
		database.DB.Model(&models.IntegrationCategories{}).Where("name=?", name).Count(&count)
	}
	return count == 0
}
