package models

type IntegrationApps struct {
	Name         string `gorm:"column:name;type:varchar(60);comment:app 名称;NOT NULL" form:"name" json:"name"`
	Logo         string `gorm:"column:logo;type:varchar(255);comment:app logo;NOT NULL" form:"logo" json:"logo"`
	CategoryId   int    `gorm:"column:category_id;type:int(11);comment:分类id;NOT NULL" form:"category_id" json:"category_id"`
	Tag          int    `gorm:"column:tag;type:tinyint(4);default:0;comment:标签样式0为无标签;NOT NULL" form:"tag" json:"tag"`
	DiscountLine string `gorm:"column:discount_line;type:varchar(255);comment:折扣描述;NOT NULL" form:"discount_line" json:"discount_line"`
	Status       int    `gorm:"column:status;type:tinyint(4);default:0;comment:0为draft 1为publish 2为hidden;NOT NULL" form:"status" json:"status"`
	Sort         int    `gorm:"column:sort;type:int(11);default:99;comment:排序;NOT NULL" form:"sort" json:"sort"`
	AppType      int    `gorm:"column:app_type;type:tinyint(4);default:0;comment:0为展示型，1为功能型;NOT NULL" form:"app_type" json:"app_type"`
	Description  string `gorm:"column:description;type:text;comment:app 描述" form:"description" json:"description"`
	ListingLink  string `gorm:"column:listing_link;type:varchar(255);comment:listing链接;NOT NULL" form:"listing_link" json:"listing_link"`
	SettingLink  string `gorm:"column:setting_link;type:varchar(255);comment:设置页面子链接;NOT NULL" form:"setting_link" json:"setting_link"`
	HelpLink     string `gorm:"column:help_link;type:varchar(255);comment:帮助文档链接链接;NOT NULL" form:"help_link" json:"help_link"`
	Top          int    `gorm:"column:top;type:tinyint(3);default:0;comment:置顶0为不置顶，1为置顶;NOT NULL" form:"top" json:"top"`
	Score        string `gorm:"column:score;type:varchar(255);comment:评分展示;" form:"score" json:"score"`
	ShortDesc    string `gorm:"column:short_desc;type:text;comment:首页卡片标题显示;" form:"short_desc" json:"short_desc"`
	Model
	Category IntegrationCategories `gorm:"foreignkey:CategoryId" json:"category"`
}

func (IntegrationApps) TableName() string {
	return "integration_apps"
}
