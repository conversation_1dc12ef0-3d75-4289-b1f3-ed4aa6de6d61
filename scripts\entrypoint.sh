#!/usr/bin/env bash

set -e
# 根据环境变量选择配置文件
if [ "$APP_ENV" = "development" ] || [ "$APP_ENV" = "test" ]; then
  echo "Running in $APP_ENV environment"
  cp /app/config/config-test.yaml /app/config/config.yaml
else
  echo "Running in production environment"
  # 保持默认的生产配置文件不变
fi

# 通用多进程监控脚本
declare -A PIDS
# 启动多个关键进程
/app/app  1>/dev/stdout 2>&1 & PIDS["api"]=$!
/app/webhook  1>/dev/stdout 2>&1 & PIDS["webhook"]=$!
/app/admin  1>/dev/stdout & PIDS["admin"]=$!

# 定义优雅退出函数
graceful_exit() {
    echo "捕获退出信号，终止所有进程..."
    kill -TERM "${PIDS[@]}"
    wait "${PIDS[@]}"
    exit 143
}
trap graceful_exit SIGTERM SIGINT
# 持续监控进程状态
while true; do
    for name in "${!PIDS[@]}"; do
        if ! kill -0 "${PIDS[$name]}" 2>/dev/null; then
            echo "$name 进程异常退出"
        fi
    done
    sleep 5
done