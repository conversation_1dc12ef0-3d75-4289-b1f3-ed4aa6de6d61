<?php

namespace App\Services\Impl;

use App\Models\Admin\NotificationReadLog;
use App\Models\Common\Country;
use App\Models\Order\OrderCouponSell;
use App\Models\System\Translation;
use App\Models\Tracking\TrackPageSetting;
use App\Models\User;
use App\Models\User\UserSetting;
use App\Models\User\UserType;
use App\Repositories\Admin\NotificationRepository;
use App\Repositories\Common\TranslationRepository;
use App\Repositories\Money\ChargeRecordRepository;
use App\Repositories\Money\OnetimeRecordRepository;
use App\Repositories\OrderTrackRepository;
use App\Repositories\Permission\LevelPermissionRepository;
use App\Repositories\Statistics\ShipmentTransitTimeDailyLogRepository;
use App\Repositories\Tracking\Courier\CourierRepository;
use App\Repositories\Tracking\TrackPageSettingRepository;
use App\Repositories\Tracking\TrackPageThemeRepository;
use App\Repositories\User\UserCouponRepository;
use App\Repositories\User\UserSettingRepository;
use App\Services\Domain\AffiliateType;
use App\Services\Domain\Charge\CouponDomain;
use App\Services\Domain\Charge\GroupSubDomain;
use App\Services\Domain\Plan\Level;
use App\Services\Domain\Plan\Version;
use App\Services\Domain\PlanDomainV2;
use App\Services\Domain\TrackDomain;
use App\Services\Interfaces\ChargeInterface;
use App\Services\Interfaces\UserInterface;
use App\Utils\Redis;
use Carbon\Carbon;

class UserService implements UserInterface
{
    private TrackPageSettingRepository $trackPageSetting;
    private UserSettingRepository $userSetting;
    private ChargeRecordRepository $chargeRecord;
    private OrderTrackRepository $orderTrack;
    private UserCouponRepository $userCoupon;
    private OnetimeRecordRepository $onetimeRecord;
    private TranslationRepository $translationRep;
    private NotificationRepository $notificationRep;
    private TrackPageThemeRepository $trackPageTheme;
    private ShipmentTransitTimeDailyLogRepository $transitTimeDailyLogRep;
    private CourierRepository $courierRep;
    private ChargeRecordRepository $chargeRecordRep;
    private UserCouponRepository $couponRep;

    public function __construct(
        UserSettingRepository                 $userSetting,
        TrackPageSettingRepository            $trackPageSetting,
        ChargeRecordRepository                $chargeRecord,
        OrderTrackRepository                  $orderTrack,
        UserCouponRepository                  $userCoupon,
        OnetimeRecordRepository               $onetimeRecord,
        TranslationRepository                 $translationRep,
        NotificationRepository                $notificationRep,
        TrackPageThemeRepository              $trackPageTheme,
        ShipmentTransitTimeDailyLogRepository $transitTimeDailyLogRep,
        CourierRepository                     $courierRep,
        ChargeRecordRepository                $chargeRecordRep,
        UserCouponRepository                  $couponRep
    )
    {
        $this->userSetting = $userSetting;
        $this->chargeRecord = $chargeRecord;
        $this->orderTrack = $orderTrack;
        $this->trackPageSetting = $trackPageSetting;
        $this->userCoupon = $userCoupon;
        $this->onetimeRecord = $onetimeRecord;
        $this->translationRep = $translationRep;
        $this->notificationRep = $notificationRep;
        $this->trackPageTheme = $trackPageTheme;
        $this->transitTimeDailyLogRep = $transitTimeDailyLogRep;
        $this->courierRep = $courierRep;
        $this->chargeRecordRep = $chargeRecordRep;
        $this->couponRep = $couponRep;
    }

    /**
     * 它获取订单的状态。
     *
     * @param $user_id
     * @return array
     */
    public function getUserStatusList($user_id): array
    {
        $data = $this->orderTrack->getTrackStatusList($user_id);
        $item = TrackDomain::$statusList;

        foreach ($data['status_list'] as $key => $statusItem) {
            $color = '#303030';
            if ($statusItem['status'] === 1) {
                $color = '#91D0FF';
            }
            if ($statusItem['status'] === 2) {
                $color = '#005BD3';
            }
            if ($statusItem['status'] === 3) {
                $color = '#FFE600';
            }
            if ($statusItem['status'] === 6) {
                $color = '#997AFC';
            }
            if ($statusItem['status'] === 7) {
                $color = '#C70A24';
            }
            $statusItem['name'] = $item[$statusItem['status']];
            $statusItem['value'] = $statusItem['nums'];
            $statusItem['itemStyle'] = [
                "color" => $color,
            ];
            $data['status_list'][$key] = $statusItem;
        }
        return $data;
    }

    /**
     * 获取用户旧套餐的剩余进度
     * @param User $user
     * @return array
     */
    public function getUserOldRemain(User $user): array
    {
        $result['old_total'] = $user->old_total ?: 0;
        $result['consumer_previous'] = $user->old_consume ?: 0;
        $result['remain_previous'] = $result['old_total'] - $result['consumer_previous'];
        if ($result['old_total']) {
            $result['remain_progress'] = round($result['remain_previous'] * 100 / $result['old_total'], 2);
        } else {
            $result['remain_progress'] = 0;
        }
        return $result;
    }

    /**
     * 它返回一个用于呈现用户仪表板的数据数组
     *
     * @param User $user 用户的信息，包括订单数量、订单数量、订单数量。
     *
     * @return array 返回值是用于填充仪表板的数据数组。
     */
    public function getShopifyMessage(User $user): array
    {
        $shop_name = $user->name ?: "noShopName";
        $shop = $user->store_name;
        $shop_real_domain = $user->store_real_name ?: $shop;
        # 检查用户是否注册了 track page 没有则注册
        $user_id = $user->id;
        $groupSubDomain = new GroupSubDomain($user);
        $track_path = $this->trackPageSetting->getUserTrackLink($user_id);
        $mainStore = $groupSubDomain->getMainStore();
        # 主店铺已经不是 pro 或者 自己不是免费用户, 使用自己的额度情况
        if (!is_null($mainStore) && !$groupSubDomain->inheritFromMain()) {
            $mainStore = null;
        }
        if ($mainStore) {
            # 免费套餐额度
            $free = $this->userSetting->getValueByUserKey($mainStore->id, UserSetting::$free_total) ?? config('user_order.free_order_limit');
            # 获取用户或主店铺使用额度情况
            $result = $mainStore->getPlanData($free);
        }else{
            # 免费套餐额度
            $free = $this->userSetting->getValueByUserKey($user->id, UserSetting::$free_total) ?? config('user_order.free_order_limit');
            # 获取用户或主店铺使用额度情况
            $result = $user->getPlanData($free);
        }
        // 付费过期时间 订阅时间
        $where = ["user_id" => $user->id, ['status', '!=', ChargeInterface::PENDING]];
        $permissionDomain = $groupSubDomain->getPermissionDomain();
        # 提取套餐列表
        if ($mainStore) {
            $where['user_id'] = $mainStore->id;
        }
        $versionArr = $this->chargeRecord->where($where)->orderBy('created_at')->pluck('version', 'status');
        if (!empty($versionArr[ChargeInterface::ACTIVE])) {
            $result['free_trial'] = false;
            $version = $versionArr[ChargeInterface::ACTIVE];
        } else {
            $result['free_trial'] = count($versionArr) <= 0;
            $version = Version::LATEST;
        }
        $plan_id = $mainStore?$groupSubDomain->getMainPlanId():$groupSubDomain->getUserPlanId();
        $planDomain = $mainStore?new PlanDomainV2($mainStore->id):new PlanDomainV2($user->id,$version);
        $planItem = $planDomain->getPlanItem($plan_id);
        # 单号数量
        $result['level_num'] = $planItem->getLevel();
        $result['version'] = $version;

        $settingArr = $this->userSetting->getAllSettingByKeys($user_id, [UserSetting::$guidance_model, UserSetting::$lang, UserSetting::$dashboard_guide, UserSetting::$dashboard_app_show, UserSetting::$dashboard_coupon_banner,UserSetting::$dashboard_affiliate_banner, UserSetting::$affiliate_mark, UserSetting::$free_trial, UserSetting::$free_total,UserSetting::$dashboard_guide_toggle_state,UserSetting::$dashboard_trends_toggle_state]);
        $guide_tab = empty($settingArr[UserSetting::$guidance_model]);
        $lang = empty($settingArr[UserSetting::$lang]) ? 'en' : $settingArr[UserSetting::$lang];
        $permission = $permissionDomain->getPermissions();
        # 用户订单同步天数
        $sync_day = $user->sync_day ?? 30;
        $dashboard_guide = $settingArr[UserSetting::$dashboard_guide] ?? null;
        $dashboard_app_show = empty($settingArr[UserSetting::$dashboard_app_show]);
        $dashboard_coupon_banner = empty($settingArr[UserSetting::$dashboard_coupon_banner]);
        $dashboard_affiliate_banner = empty($settingArr[UserSetting::$dashboard_affiliate_banner]);
        $guide_toggle_state = !empty($settingArr[UserSetting::$dashboard_guide_toggle_state]);
        $trends_toggle_state =  !empty($settingArr[UserSetting::$dashboard_trends_toggle_state]);
        $hasAnySubscribe = $this->chargeRecordRep->getUserHasAnySubscribe($user);
        $userCoupon = $this->couponRep->getActiveCouponByUserAndCode($user->id, '');
        // 新的20折扣的新人优惠
        $coupon_banner_show = $userCoupon->where('coupon_code', CouponDomain::FIRST202MONTH)->isNotEmpty() && $dashboard_coupon_banner && !$hasAnySubscribe;
        $affiliate_mark = AffiliateType::match($settingArr[UserSetting::$affiliate_mark] ?? 0);
        $affiliateCoupon = $this->couponRep->getAffiliateCoupon($userCoupon,$affiliate_mark,$dashboard_coupon_banner);
        $affiliate_discount = 0;
        if ($affiliate_mark!==AffiliateType::DEFAULT) {
            // 存在 affiliate 折扣的话不展示新人优惠banner
            if ($affiliate_mark === AffiliateType::MONTHLY_DISCOUNT) {
                $coupon_banner_show = false;
            }
            // affiliate 额外免费天数优惠计算
            if ($affiliate_mark === AffiliateType::FREE_TRIAL || $affiliate_mark === AffiliateType::NORMAL) {
                $free_trial = $settingArr[UserSetting::$free_trial] ?? config('user_order.free_trial');
                $affiliate_discount = (int)$free_trial !== config('user_order.free_trial') ? (int)$free_trial : 0;
            }
            // affiliate 额外免费单号计算
            if ($affiliate_mark === AffiliateType::FREE_QUOTA) {
                $free_total = $settingArr[UserSetting::$free_total] ?? config('user_order.free_order_limit');
                $free_total = (int)$free_total;
                $affiliate_discount = $free_total - config('user_order.free_order_limit');
            }
            // affiliate 额外单号计算
            if ($affiliate_mark === AffiliateType::EXTRA_QUOTA) {
                $affiliate_discount = $user->old_total;
            }
        }
        if ($coupon_banner_show) {
            $new_user_coupon = CouponDomain::FIRST202MONTH;
        }else{
            $new_user_coupon = null;
        }
        // 免费额度超量触点礼盒是否出现 1. 用户没有接受过我们的赠送 2. 用户是免费用户 3. 用户近 30 天没有订阅过任何套餐 4. 剩余免费单号不足10%
        $free_consume_percentage = 1;
        if ($result['free'] !== 0) {
            $free_consume_percentage = ($result['free'] - $result['free_consume']) / $result['free'];
        }
        $has_free_user_coupon = $this->couponRep->checkUserHasCoupon($user_id, CouponDomain::FREEUSER1MONTH);
        $hasAnySubscribeWithin30days = $this->chargeRecord->getUserSubscriptionByDate($user);
        $contact_gift_box = $user->plan_id === 0 && ($free_consume_percentage < 0.1) && !$hasAnySubscribeWithin30days && $has_free_user_coupon === "No discount code found";
        // 用户分级
        $target_type = UserType::where('user_id', $user->id)->value('target_type')??UserType::BEGIN_USER;
        if ($result['level_num']->value>Level::FREE->value) {
            $target_type = $result['level_num']===Level::ENTERPRISE?UserType::GLOBAL_USER:UserType::GROW_USER;
        }
        // 是否展示广告
        $show_ad = $user->created_at->diffInDays(now()) > 30;
        $status_list = $this->orderTrack->getTrackStatusList($user_id,true);
        $show_analytics = true;
        if ($status_list['order_count'] === 0) {
            $show_analytics = false;
        }
        if ($status_list['order_count'] > 0 && $user->created_at->diffInDays(Carbon::now()) < 2) {
            $show_analytics = false;
        }
        # 返回的数据
        return [
            'guide_tab' => $guide_tab,
            'guide_toggle_state' => $guide_toggle_state,
            'trends_toggle_state' => $trends_toggle_state,
            'show_analytics' => $show_analytics,
            'target_type' => $target_type,
            'show_ad' => $show_ad,
            'email' => $user->email,
            'lang' => $lang,
            "shop" => $shop,
            "dashboard_guide" => !empty($dashboard_guide) ? json_decode($dashboard_guide,1) : ['courier_setting' => false, 'track_page_setting' => false, 'notify_setting' => false, 'choose_plan' => false],
            "shop_name" => $shop_name,
            "shop_real_domain" => $shop_real_domain,
            "permission" => $permission,
            "track_path" => $track_path,
            "user" => $result,
            "rating" => $user->rating,
            'courier_cdn'=>config('track_config.tm_cdn_domain'),
            'resource_version'=>config('track_config.assets_version'),
            "sync_day" => $sync_day,
            "dashboard_app_show" => $dashboard_app_show,
            "new_user_coupon" => $new_user_coupon,
            "affiliate_coupon" => $affiliateCoupon,
            "affiliate_type" => $affiliate_mark,
            "affiliate_discount" => $affiliate_discount,
            "contact_gift_box" => $contact_gift_box,
            "dashboard_affiliate_banner" => $dashboard_affiliate_banner,
        ];
    }

    /**获取用户套餐情况
     * @param $user
     * @return array
     */
    public function getUserQuota($user): array
    {
        # shopify 套餐总额度
        $total_order_count = $user->total ?? 0;
        # shopify 套餐已使用额度
        $consume_order_count = $user->consume ?? 0;
        # shopify 套餐id
        $plan_id = $user->plan_id ?? 0;
        # 免费套餐额度
        $free = $this->userSetting->getValueByUserKey($user->id, UserSetting::$free_total) ?? config('user_order.free_order_limit');
        return [
            'plan' => $plan_id,
            'total' => $total_order_count, 'consume' => min($consume_order_count, $total_order_count),
            'free' => (int)$free, 'free_consume' => min($free, $user->free_order_use),
            'old_total' => $user->old_total, 'old_consume' => min($user->old_total,$user->old_consume), 'double' => (int)$free !== config('user_order.free_order_limit'),
        ];
    }

    /**获取聊天相关数据
     * @param User $user
     * @return array
     */
    public function getUserOther(User $user): array
    {
        $user_id = $user->id;
        $isNewNotice = false;
        $newNoticeId = $this->notificationRep->where('publish', 1)->orderBy('updated_at', 'desc')->value('id');
        if (!empty($newNoticeId)) {
            $readLog = NotificationReadLog::where(['user_id' => $user_id, 'notification_id' => $newNoticeId])->first();
            $isNewNotice = $readLog === null;
        }
        $result = $this->getUserQuota($user);
        ### 店铺付费类型 start ###
        $shopifyPlanName = $user->shopify_plan_name ?: 'Normal';
        # 用户留评等级
        $rating = $user->rating;
        # 用户国家
        $country_code = $user->country ?: '';
        # 补偿单量
        $old_total = $user->old_total ?? 0;
        $old_consume = $user->old_consume ?? 0;
        // 消费单号数量
        $consume_order_count = $result['consume'];
        $total_order_count = $result['total'];
        $shop_name = $user->name ?: "noShopName";
        $shop = $user->store_name;
        $userEmail = $user->email ?: "<EMAIL>";
        $plan = empty($user['plan_id']) ? 0 : (int)$user['plan_id'];
        $sign_date = $user->created_at ?: now();
        $country_name = !empty($country_code) ? Country::where('two_code', $country_code)->value('en_name') : $country_code;
        $session_data = [
            ['A_User_name', $shop_name],
            ['B_User_tag', 'TMS'],
            ['C_User_id', $user_id],
            ['D_User_email', $userEmail],
            ['E_User_type', $shopifyPlanName],
            ['F_User_plan', $plan],
            ['G_Quota_remaining', $total_order_count - $consume_order_count],
            ['H_Additional_quota_total', $old_total],
            ['I_Additional_quota_remaining', $old_total - $old_consume],
            ['J_User_registration_time', $sign_date->format('Y-m-d H-i-s')],
            ['K_Country', $country_name],
            ['L_Company_name', $shop],
            ['M_Admin_url', 'https://' . config('shopify_config.admin_domain') . '/'.config('admin.route.prefix').'/users_c?user=' . $user_id],
            ['N_Company_url', $shop],
            ['O_Rating', $rating],
        ];
        $helpUrl = [
            "dashboard" => [],
            "tracking-page" => [],
            "orders" => [],
            "notification" => [],
            "setting" => [],
            "faq" => [],
        ];
        # NPS 数据
        $nps = [
            'npsSwitch' => true,
            'token' => md5('16ISNI5AHFLN4PO2NIRNXWGU011PQ0PHMIO0NNQO' . time()),
            'accessTime' => time(),
            'planDisplayName' => $user->shopify_plan_name,
            'days' => (int)((time() - $sign_date->timestamp) / (3600 * 24)),
            'installTime' => $sign_date->timestamp,
            'lastTime' => $user->last_login_time?Carbon::make($user->last_login_time)->timestamp:now(),
        ];

        return [
            'email' => $user->email,
            'name' => $shop_name,
            'session_data' => $session_data,
            'is_new_notice' => $isNewNotice,
            "nps" => $nps,
            'shop' => $shop,
            'token_id' => md5("202208" . (in_array($user_id, [2, 2194], true) ? $shop . 'rest' : $shop)),
            "help_url" => $helpUrl,
        ];
    }

    public function dropshippingMode(User $user,bool $open): bool
    {
        $trackPage = $this->trackPageSetting->getUserTrackPage($user->id);
        if ($open) {
            $trackPage = $this->openDropshippingMode($trackPage, $user);
            return $trackPage->save();
        }
        $this->userSetting->setUserValue($user->id, UserSetting::$dropshipping_mode_open, 0);
        $hideSetting = json_decode($trackPage->hide_setting, 1);
        if (empty($hideSetting)) {
            $hideSetting = [];
        }
        if (!empty($hideSetting)) {
            foreach ($hideSetting as $key=>$item) {
                if ($item['origin'] === '{dropshipping_couriers}') {
                    unset($hideSetting[$key]);
                }
                if ($item['origin'] === '{china_cities}') {
                    unset($hideSetting[$key]);
                }
            }
        }
        $trackPage->hide_setting = json_encode($hideSetting);
        $classicTheme = $trackPage->classic_theme;
        $themeSettings = json_decode($classicTheme->settings, 1);
        $themeSettings['detail_info']['courier']['switch'] = true;
        $classicTheme->settings = json_encode($themeSettings);
        $classicTheme->save();
        return $trackPage->save();
    }

    /**
     * @param TrackPageSetting $trackPage
     * @param User $user
     * @return TrackPageSetting
     */
    private function openDropshippingMode(TrackPageSetting $trackPage, User $user): TrackPageSetting
    {
        $this->userSetting->setUserValue($user->id, UserSetting::$dropshipping_mode_open, 1);
        $hideSetting = json_decode($trackPage->hide_setting, 1);
        $hasSet = false;
        $hasSetCity = false;
        if (empty($hideSetting)) {
            $hideSetting = [];
        }
        if (!empty($hideSetting)) {
            foreach ($hideSetting as $item) {
                if ($item['origin'] === '{dropshipping_couriers}') {
                    $hasSet = true;
                }
                if ($item['origin'] === '{china_cities}') {
                    $hasSetCity = true;
                }
            }
        }
        if (!$hasSet) {
            $hideSetting[] = [
                'origin' => '{dropshipping_couriers}', 'hide_type' => 0, 'replace_type' => 0, 'replace' => '',
                'sort' => 0,
            ];

        }
        if (!$hasSetCity) {
            $hideSetting[] = [
                'origin' => '{china_cities}', 'hide_type' => 0, 'replace_type' => 0, 'replace' => '', 'sort' => 0,
            ];
        }
        $trackPage->hide_setting = json_encode($hideSetting);
        $classicTheme = $trackPage->classic_theme;
        $themeSettings = json_decode($classicTheme->settings, 1);
        $themeSettings['detail_info']['courier']['switch'] = false;
        $themeSettings['map'] = ['location' => false, 'destination' => true];
        $classicTheme->settings = json_encode($themeSettings);
        $classicTheme->save();
        return $trackPage;
    }

    public function changePlanModalData(User $user): array
    {
        $planDomain = new PlanDomainV2($user->id);
        $chargeList = $planDomain->planList();
        $pro = [];
        foreach ($chargeList as $plan_id => $item) {
            if ($item->getLevel() === Level::PRO) {
                $pro[$plan_id] = $item;
            }
        }
        $data["recharge_list"] = $pro;
        $data['coupon'] = null;
        $activeCoupons = [CouponDomain::NEWUSER3M, CouponDomain::NEWUSER1M];
        $coupons = $this->userCoupon->getActiveCouponByUser($user->id,
            [CouponDomain::UNLIMITED_COUPON, CouponDomain::RECURRING_COUPON]);
        if ($coupons->isNotEmpty()) {
            foreach ($coupons as $coupon) {
                if (in_array($coupon->coupon_code, $activeCoupons, true)) {
                    $data['coupon'] = [
                        'code' => $coupon->coupon_code,
                        'amount' => $coupon->amount,
                        'duration' => $coupon->duration,
                        'discount_type' => $coupon->discount_type,
                        'coupon_type' => $coupon->coupon_type,
                        'end_at' => $coupon->end_at,
                        'expired_at' => $coupon->expired_at,
                    ];
                }
            }
        }
        # 是否有免费试用资格
        $data['free_trial'] = !$this->chargeRecord->getUserHasAnySubscribe($user);
        return $data;
    }

    public function getNotificationList(int $user_id)
    {
        $res = $this->notificationRep->where('publish', 1)->select(['id', 'title', 'tag', 'content', 'updated_at'])->orderBy('updated_at', 'desc')->simplePaginate(5);
        $data = [];
        foreach ($res as $item) {
            $data[] = [
                'id' => $item['id'],
                'title' => $item['title'],
                'tag' => $item['tag'],
                'content' => $item['content'],
                'last_update' => $item['updated_at']->format('F d, Y'),
            ];
        }
        $text = $res->toArray();
        $next_page_url = explode('=', $text['next_page_url']);
        $next_page_num = end($next_page_url);
        return [
            'notification_list' => $data,
            'next_page_num' => $next_page_num,
        ];
    }

    /** 记录通知已被阅读
     */
    public function notificationRead(int $user_id)
    {
        $logList = [];
        $ids = $this->notificationRep->where('publish', 1)->pluck('id');
        if (count($ids) !== 0) {
            foreach ($ids as $id) {
                $logList[] = ['user_id' => $user_id, 'notification_id' => $id];
            }
            NotificationReadLog::upsert($logList, ['user_id', 'notification_id'], []);
        }
        return true;
    }

    public function getCouponSellsData(User $user)
    {
        $cache = Redis::get(config('cache_key.couponSellRecordCache') . $user->id);
        if ($cache) {
            $dataCache = json_decode($cache, 1);
            $data['total_amount'] = $dataCache['total_amount'] ?? 0;
            $data['couponSellRecord'] = $dataCache['couponSellRecord'] ?? [];
        } else {
            $data['couponSellRecord'] = [];
            $data['total_amount'] = 0;
            $coupons = OrderCouponSell::where(['user_id' => $user->id])->orderBy('purchase_date', 'desc')->selectRaw('amount, purchase_date')->limit(5)->get();
            if ($coupons->isNotEmpty()) {
                foreach ($coupons as $k => $item) {
                    $data['couponSellRecord'][] = [
                        'amount' => $item->amount,
                        'purchase_date' => !empty($item->purchase_date) ? Carbon::make($item->purchase_date)?->format('F d Y H:i:s') : '',
                    ];
                }
                $data['total_amount'] = number_format(OrderCouponSell::where(['user_id' => $user->id])->sum('amount'), 2);
            }
            Redis::set(config('cache_key.couponSellRecordCache') . $user->id, json_encode($data), 'ex', 4500);
        }
        $data['currency'] = $user->currency;
        # 获取用户设置里的优惠券代码
        $coupon_setting = $this->userSetting->getCouponSellSetting($user->id);
        $data['switch'] = !empty($coupon_setting["notification_coupon_set"]) || !empty($coupon_setting["track_page_coupon_set"]) || (!empty($data['couponSellRecord']));
        return $data;
    }

    public function getLangData(string $lang): array
    {
        foreach (Translation::$lang_keys as $lang_key=>$name) {
            if (\Str::startsWith($lang,$lang_key)) {
                $lang = $lang_key;
                break;
            }
        }
        if ($lang==="zh-CN") {
            $lang = "cn";
        }
        if ($lang==='zh-TW') {
            $lang = "tw";
        }
        return [$lang,$this->translationRep->getLangAll($lang)];
    }

    public function getTransitData(int $user_id, $start, $end): array
    {
        $data = $this->transitTimeDailyLogRep->getDashboardTransitData($user_id, $start, $end);
        if (empty($data)) {
            return [];
        }
        // 获取运输商名称
        $courierCodeList = array_keys($data);
        $courierNameList = $this->courierRep->getCourierNameByCodes($courierCodeList);
        $courierNameList = array_combine(array_column($courierNameList, "express"), array_column($courierNameList, "name"));
        foreach ($data as $key => $item) {
            $data[$key]["average"] = sprintf("%.2f", $item["average"]);
            $data[$key]["name"] = !empty($courierNameList[$data[$key]["name"]]) ? $courierNameList[$data[$key]["name"]] : $key;
            // 所有桶内有几条数据
            $total = $item["total"];
            unset($data[$key]["total"]);
            $p85List = $item["p85"];
            if (empty($p85List)) {
                $data[$key]["p85"] = 0;
                continue;
            }
            $p85 = $this->transitTimeDailyLogRep->handleP85Data($p85List, $total);
            $data[$key]["p85"] = $p85;
        }
        return array_values($data);
    }

    /**通过token(users表的password字段)获取user_id
     */
    public function getUserByToken(string $token) {
        return User::where(['is_delete' => 0, 'password' => $token])->first();
    }
}
