package config

type cacheKeyConfig struct {
	TrackingWebhookUpdate       string
	ShopUninstalledQueue        string
	ShopUpdateQueue             string
	ShopRedactQueue             string
	CreateOrderQueue            string
	AppSubscriptionsUpdateQueue string
	RecommendProductKey         string
	CollectionProductCache      string
	PermissionCache             string
	ShopifyFlowTriggerEnabled   string
	ChinaCityHideKey            string
	ShopifyCountries            string
	ThemeCss                    string
	ThemeSetting                string
	ShopInstallCache            string
	UserChargeCustomizePlans    string
	WebhookEntityLogCache       string
	ChargePlans                 string
	ChargePlansV2               string
	UserChargeCustomizePlansV2  string
}

var CacheKey = cacheKeyConfig{
	TrackingWebhookUpdate:       "TRACKING_WEBHOOK_UPDATE",
	ShopUninstalledQueue:        "SHOP_UNINSTALLED_QUEUE",
	ShopUpdateQueue:             "SHOP_UPDATE_QUEUE",
	ShopRedactQueue:             "SHOP_REDACT_QUEUE",
	CreateOrderQueue:            "CREATE_ORDER_QUEUE",
	AppSubscriptionsUpdateQueue: "APP_SUBSCRIPTIONS_UPDATE_QUEUE",
	RecommendProductKey:         "USER_RECOMMEND_PRODUCT_LIST_",
	CollectionProductCache:      "COLLECTION_PRODUCT_COLLECTION_ID_",
	PermissionCache:             "USER_PERMISSION_",
	ShopifyFlowTriggerEnabled:   "SHOPIFY_FLOW_TRIGGER_ENABLED_",
	ChinaCityHideKey:            "CHINA_CITIES_KEY",
	ShopifyCountries:            "SHOPIFY_COUNTRIES",
	ThemeCss:                    "TMshopify_THEME_CSS",
	ThemeSetting:                "TMshopify_THEME_SETTING",
	ShopInstallCache:            "SHOP_INSTALL_",
	UserChargeCustomizePlans:    "USER_CUSTOMIZE_PLANS_",
	WebhookEntityLogCache:       "WEBHOOK_ENTITY_LOG_CACHE",
	ChargePlans:                 "CHARGE_PLANS_",
	ChargePlansV2:               "CHARGE_PLANS_V2_",
	UserChargeCustomizePlansV2:  "USER_CUSTOMIZE_PLANS_V2_",
}
