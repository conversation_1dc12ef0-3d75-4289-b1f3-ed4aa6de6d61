package models

type UserIntegrations struct {
	UserId    int    `gorm:"column:user_id;type:int(11);comment:user id;NOT NULL" json:"user_id"`
	AppId     int    `gorm:"column:app_id;type:int(11);comment:app id;NOT NULL" json:"app_id"`
	Connected int    `gorm:"column:connected;type:tinyint(4);default:0;comment:0为未对接1为已对接;NOT NULL" json:"connected"`
	Setting   string `gorm:"column:setting;type:text;comment:用户设置json" json:"setting"`
}

func (UserIntegrations) TableName() string {
	return "user_integrations"
}
