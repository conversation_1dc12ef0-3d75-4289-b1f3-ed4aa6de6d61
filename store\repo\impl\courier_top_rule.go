package impl

import (
	"tmshopify/pkg/utils/paginate"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type CourierTopRule struct {
	Helper
}

func (c *CourierTopRule) Create(m *models.CourierTopRules) error {
	return database.DB.Create(m).Error
}

func (c *CourierTopRule) Update(m *models.CourierTopRules) error {
	return database.DB.Omit("created_at", "created_by").Save(m).Error
}
func (c *CourierTopRule) Count() int64 {
	var count int64
	database.DB.Model(&models.CourierTopRules{}).Count(&count)
	return count
}
func (c *CourierTopRule) GetCourierTopRuleList(paginateParam *paginate.Param) ([]models.CourierTopRules, int64, error) {
	var items []models.CourierTopRules
	total := c.Count()
	err := database.DB.
		Order("created_at DESC").
		Scopes(paginate.ORMScope(paginateParam)).Find(&items).Error
	return items, total, err
}
