package service

import (
	"tmshopify/pkg/utils/paginate"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type Notification struct {
	shopifyNotificationRepo *impl.ShopifyNotificationLog
}

func NewNotification() *Notification {
	return &Notification{
		shopifyNotificationRepo: &impl.ShopifyNotificationLog{},
	}
}

func (s *Notification) GetShopifyNotificationLogList(param *paginate.Param, userId int, trackNumber string) ([]models.ShopifyNotificationLog, int64) {
	data, count, _ := s.shopifyNotificationRepo.GetShopifyNotificationLog(param, userId, trackNumber)
	return data, count
}
