package logger

import (
	"time"
	"tmshopify/pkg/utils/helper"

	"github.com/lestrrat-go/file-rotatelogs"
	"github.com/rifflock/lfshook"
	"github.com/sirupsen/logrus"
)

// 与Logging相关的常量设置
const (
	logFileExtension = ".log"
	logTimeFormat    = "2006-01-02 15:04:05"
	cutTimeAge       = 24 * time.Hour     // 日志切割时间
	maxLogAge        = 7 * 24 * time.Hour // Days
)

// ExceptionReport 用于日志异常报告
type ExceptionReport struct{}

// Levels 获取日志级别
func (e *ExceptionReport) Levels() []logrus.Level {
	return []logrus.Level{
		logrus.ErrorLevel, logrus.PanicLevel, logrus.FatalLevel,
	}
}

// Fire 触发日志报告的钩子函数
func (e *ExceptionReport) Fire(entry *logrus.Entry) error {
	helper.CallWilding(entry.Message)
	return nil
}

func InitLogger(path string) {
	logWriter, _ := rotatelogs.New(
		path+".%Y-%m-%d"+logFileExtension,
		rotatelogs.WithLinkName(path),           // 生成软链，指向最新日志文件
		rotatelogs.WithMaxAge(maxLogAge),        // 文件最大保存时间
		rotatelogs.WithRotationTime(cutTimeAge), // 日志切割时间间隔
	)
	switch {
	case helper.IsProduction():
		logrus.SetLevel(logrus.WarnLevel)
	case helper.IsTesting():
		logrus.SetLevel(logrus.DebugLevel)
	default:
		logrus.SetLevel(logrus.InfoLevel)
	}
	// 异常报警
	e := &ExceptionReport{}
	logrus.AddHook(e)

	writeMap := lfshook.WriterMap{
		logrus.InfoLevel:  logWriter,
		logrus.FatalLevel: logWriter,
		logrus.DebugLevel: logWriter,
		logrus.WarnLevel:  logWriter,
		logrus.ErrorLevel: logWriter,
		logrus.PanicLevel: logWriter,
	}
	lfHook := lfshook.NewHook(writeMap, &logrus.JSONFormatter{
		TimestampFormat: logTimeFormat,
	})
	logrus.AddHook(lfHook)
}
