package models

type AdminUsers struct {
	Username      string `gorm:"column:username;type:varchar(120);NOT NULL" json:"username"`
	Password      string `gorm:"column:password;type:varchar(80);NOT NULL" json:"password"`
	Name          string `gorm:"column:name;type:varchar(255);NOT NULL" json:"name"`
	Avatar        string `gorm:"column:avatar;type:varchar(255)" json:"avatar"`
	RememberToken string `gorm:"column:remember_token;type:varchar(100)" json:"remember_token"`
	Email         string `gorm:"column:email;type:varchar(255);comment:超管邮箱登录" json:"email"`
	Model
	Roles []AdminRoles `gorm:"many2many:admin_role_users;ForeignKey:ID;joinForeignKey:UserId;References:ID;joinReferences:RoleId" json:"roles"`
}

func (AdminUsers) TableName() string {
	return "admin_users"
}
