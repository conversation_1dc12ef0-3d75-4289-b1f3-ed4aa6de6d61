package impl

import (
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type DailyPendingTracks struct {
	Helper
}

type pieData struct {
	Name  string `json:"name"`
	Value int    `json:"value"`
}

type samplingTable struct {
	Courier     string `json:"courier"`
	UserId      int    `json:"user_id"`
	PendingDays int    `json:"pending_days"`
	TrackNumber string `json:"track_number"`
}

func (d *DailyPendingTracks) GroupByTrackLength() ([]map[string]interface{}, error) {
	var column []map[string]interface{}
	result := database.DB.Model(models.DailyPendingTracks{}).Select("track_length, COUNT(id) as total").Group("track_length").Scan(&column)
	return column, result.Error
}

func (d *DailyPendingTracks) GetCourierDistributionByTrackLength(trackLength int) ([]pieData, error) {
	var results []pieData
	result := database.DB.Model(&models.DailyPendingTracks{}).
		Select("courier as name, COUNT(*) as value").
		Where("track_length = ?", trackLength).
		Group("courier").
		Scan(&results)
	return results, result.Error
}

func (d *DailyPendingTracks) GetUniqueTracksByUserAndCourier(trackLength int) ([]samplingTable, error) {
	var results []samplingTable
	subQuery1 := database.DB.Model(&models.DailyPendingTracks{}).
		Select("courier, user_id, MAX(pending_days) AS pending_days").
		Where("track_length = ?", trackLength).
		Group("courier, user_id")
	subQuery2 := database.DB.Model(&models.DailyPendingTracks{})
	res := database.DB.Table("(?) AS t1, (?) AS t2", subQuery1, subQuery2).
		Select("t1.courier, t1.user_id, t1.pending_days, track_number").
		Where("t1.pending_days = t2.pending_days AND t1.user_id = t2.user_id AND t1.courier = t2.courier AND track_length = ?", trackLength).
		Group("courier, user_id").
		Order("pending_days DESC").
		Scan(&results)
	return results, res.Error
}
