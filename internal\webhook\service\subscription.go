package service

import (
	"encoding/json"
	"tmshopify/config"
	"tmshopify/pkg/shopify/webhook"
	"tmshopify/store/database"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type Subscription struct {
}

type SubscriptionService interface {
	SubscriptionUpdate(c *gin.Context) bool
}

type SubscriptionWebhook struct {
	Shop string                             `json:"shop"`
	Data webhook.RecurringApplicationCharge `json:"data"`
}

func (s *Subscription) SubscriptionUpdate(c *gin.Context) bool {
	shop := c.GetHeader(webhook.XShopifyDomain)
	logrus.Warnf("subscription was update from %s", shop)
	subscription := new(SubscriptionWebhook)
	subscription.Shop = shop
	if err := c.ShouldBind(&(subscription.Data)); err != nil {
		logrus.Warn("the body should be RecurringApplicationCharge entity, error:" + err.Error() + " shop is:" + shop)
	}
	subscriptionJson, _ := json.Marshal(subscription)
	_, redisErr := database.RS.SAdd(config.CacheKey.AppSubscriptionsUpdateQueue, string(subscriptionJson))
	if redisErr != nil {
		logrus.Warn("redis set error " + redisErr.Error())
		return false
	}
	return true
}

func NewSubscription() *Subscription {
	return &Subscription{}
}
