package service

import (
	"errors"
	"github.com/sirupsen/logrus"
	"strconv"
	"tmshopify/pkg/domain"
	"tmshopify/pkg/utils/paginate"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type UserService struct {
	userRep               impl.UserRepository
	chargeRep             impl.ChargeRecordRepository
	subscriptionDetailRep impl.SubscriptionDetailRepository
	chargePlanRep         impl.ChargePlanRepository
}

func (s *UserService) GetChargePlans(paramUserId string, paramIsDelete string) []models.ChargePlans {
	userId, _ := strconv.Atoi(paramUserId)
	var isDelete int
	if len(paramIsDelete) > 0 {
		if paramIsDelete == "1" {
			isDelete = 1
		} else {
			isDelete = 0
		}
	} else {
		isDelete = 2
	}
	chargePlans := s.chargePlanRep.GetPlanList(userId, isDelete)
	return chargePlans
}

type searchUserResult struct {
	Id        int    `json:"id"`
	StoreName string `json:"store_name"`
}

type subscriptionItem struct {
	UserId      int            `json:"user_id"`
	Plans       int            `json:"plans"`
	ActivatedAt string         `json:"activated_at"`
	RenewAt     string         `json:"renew_at"`
	FrozenAt    string         `json:"frozen_at"`
	CanceledAt  string         `json:"canceled_at"`
	ExpiredAt   string         `json:"expired_at"`
	Status      string         `json:"status"`
	Price       float32        `json:"price"`
	ExtraPrice  float32        `json:"extra_price"`
	Version     models.Version `json:"version"`
	PlanType    string         `json:"plan_type"`
}

func (s *UserService) SearchUser(keyword string) []searchUserResult {
	var data []searchUserResult
	var users []models.Users
	if len(keyword) == 0 {
		users, _, _ = s.userRep.Paginate(&paginate.Param{Page: 1, PageSize: 20})

	} else if userId, err := strconv.Atoi(keyword); err == nil {
		users = s.userRep.SearchUserByIdField(userId)
	} else {
		users = s.userRep.SearchUserByEmailOrStoreNameField(keyword)
	}
	if len(users) > 0 {
		for _, user := range users {
			data = append(data, searchUserResult{
				Id:        user.ID,
				StoreName: user.StoreName,
			})
		}
	}
	return data
}

func (s *UserService) CreatePlan(customizePlan models.ChargePlans) error {
	err := s.chargePlanRep.CreatePlan(&customizePlan)
	if customizePlan.PlanType == models.CUSTOM {
		s.chargePlanRep.DeleteCustomPlanRs(customizePlan.UserId)
	}
	if customizePlan.PlanType == models.SYSTEM {
		s.chargePlanRep.DeleteSystemPlanRs()
	}
	return err
}

func (s *UserService) UpdatePlan(customizePlan models.ChargePlans) error {
	err := s.chargePlanRep.UpdatePlan(&customizePlan)
	if customizePlan.PlanType == models.CUSTOM {
		s.chargePlanRep.DeleteCustomPlanRs(customizePlan.UserId)
	}
	if customizePlan.PlanType == models.SYSTEM {
		s.chargePlanRep.DeleteSystemPlanRs()
	}
	return err
}

func (s *UserService) ChangePlanStatus(id, status, userId string, planType models.PlanType) error {
	if status == "1" {
		has := s.chargeRep.CheckHasBeenActive(id)
		if has {
			return errors.New("already has been used in active charge")
		}
	}
	err := s.chargePlanRep.ChangePlanStatus(id, status)
	userIdInt, _ := strconv.Atoi(userId)
	if planType == models.CUSTOM {
		s.chargePlanRep.DeleteCustomPlanRs(userIdInt)
	}
	if planType == models.SYSTEM {
		s.chargePlanRep.DeleteSystemPlanRs()
	}
	return err
}

func (s *UserService) GetSubscriptionList(paramUserId, status string, param paginate.Param) []subscriptionItem {
	var userId int
	if paramUserId != "" {
		userId, _ = strconv.Atoi(paramUserId)
	}
	data := make([]subscriptionItem, 0)
	result := s.subscriptionDetailRep.GetSubscriptionList(userId, status, param)
	for _, item := range result {
		var activatedAt, renewAt, frozenAt, canceledAt, expiredAt, chargeStatus string
		if !item.ActivatedAt.IsZero() {
			activatedAt = item.ActivatedAt.Format("2006-01-02 15:04:05")
		}
		if item.RenewAt.Valid {
			renewAt = item.RenewAt.Time.Format("2006-01-02 15:04:05")
		}
		if item.FrozenAt.Valid {
			frozenAt = item.FrozenAt.Time.Format("2006-01-02 15:04:05")
		}
		if item.CanceledAt.Valid {
			canceledAt = item.CanceledAt.Time.Format("2006-01-02 15:04:05")
		}
		if item.ExpiredAt.Valid {
			expiredAt = item.ExpiredAt.Time.Format("2006-01-02 15:04:05")
		}
		chargeStatus = domain.ChargeStatus[item.Status]
		data = append(data, subscriptionItem{
			UserId:      item.UserId,
			Plans:       item.Plans,
			ActivatedAt: activatedAt,
			RenewAt:     renewAt,
			FrozenAt:    frozenAt,
			CanceledAt:  canceledAt,
			ExpiredAt:   expiredAt,
			Status:      chargeStatus,
			Price:       float32(item.Price),
			ExtraPrice:  float32(item.ExtraPrice),
			Version:     item.Version,
			PlanType:    item.PlanType,
		})
	}
	return data
}

func (s *UserService) ChangeSubscriptionExtraPrice(userid int, extraPrice float32) error {
	err := s.subscriptionDetailRep.ChangeSubscriptionExtraPrice(userid, extraPrice)
	if err != nil {
		logrus.Error("ChangeSubscriptionExtraPrice error:", err.Error(), " userid:", userid, " extraPrice:", extraPrice)
	}
	return err
}

func NewUserService() *UserService {
	return &UserService{
		userRep:               &impl.User{},
		chargeRep:             &impl.ChargeRecordRepositoryImpl{},
		subscriptionDetailRep: &impl.SubscriptionDetailRepositoryImpl{},
		chargePlanRep:         &impl.ChargePlanRepositoryImpl{},
	}
}
