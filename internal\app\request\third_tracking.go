package request

import "github.com/thedevsaddam/govalidator"

type WilldeskInstall struct {
}

type WilldeskTracking struct {
}

func (w *WilldeskInstall) Rules() govalidator.MapData {
	return govalidator.MapData{
		"shop": []string{"required", "max:255"},
	}
}

func (w *WilldeskInstall) Messages() govalidator.MapData {
	return govalidator.MapData{
		"shop": []string{"required:缺少shop", "max:最大255个字符"},
	}
}

func (w *WilldeskTracking) Rules() govalidator.MapData {
	return govalidator.MapData{
		"shop":     []string{"required", "max:255"},
		"tracking": []string{"required", "max:50"},
	}
}

func (w *WilldeskTracking) Messages() govalidator.MapData {
	return govalidator.MapData{
		"shop":     []string{"required:缺少shop", "max:最大255个字符"},
		"tracking": []string{"required:缺少tracking", "max:最大50个字符"},
	}
}
