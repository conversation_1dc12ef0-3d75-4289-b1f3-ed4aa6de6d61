package models

import "time"

type PaidConversionRateStatistics struct {
	CreateDate      time.Time `gorm:"column:create_date;type:date;comment:当天日期;NOT NULL" json:"create_date"`
	MonthNewPaid    int       `gorm:"column:month_new_paid;type:int(11);default:0;comment:新增付费用户数;NOT NULL" json:"month_new_paid"`
	MonthNewInstall int       `gorm:"column:month_new_install;type:int(11);default:0;comment:新增安装用户数;NOT NULL" json:"month_new_install"`
	MonthPaid       int       `gorm:"column:month_paid;type:int(11);default:0;comment:月付费用户数;NOT NULL" json:"month_paid"`
	MonthRenew      int       `gorm:"column:month_renew;type:int(11);default:0;comment:月续订用户数;NOT NULL" json:"month_renew"`
	Model
}

func (PaidConversionRateStatistics) TableName() string {
	return "data_paid_conversion_rate_statistics"
}
