package impl

import (
	"tmshopify/internal/app/definition"
	"tmshopify/pkg/utils/helper"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type OrderProduct struct {
}

func (p *OrderProduct) GetImgInfoByOrderId(orderId string, userId int) []definition.OrderProductImgData {
	var products []models.OrderProduct
	var productModel models.OrderProduct
	database.DB.Scopes(productModel.TableOfUser(productModel.TableName(), userId)).Where("order_id = ? and user_id = ?", orderId, userId).Select("title, image,quantity, variant_id,sku, product_id").Find(&products)

	return p.handleImageInfo(products)
}

func (p *OrderProduct) GetImgInfoByFulfillmentId(fulfillmentId string, userId int) []definition.OrderProductImgData {
	var products []models.OrderProduct
	var productModel models.OrderProduct
	database.DB.Scopes(productModel.TableOfUser(productModel.TableName(), userId)).Where("fulfillment_id = ? and user_id = ?", fulfillmentId, userId).Select("title, image,quantity, variant_id,sku, product_id").Find(&products)
	return p.handleImageInfo(products)
}

func (p *OrderProduct) handleImageInfo(products []models.OrderProduct) []definition.OrderProductImgData {
	var imageInfo []definition.OrderProductImgData
	// 检查 products 是否不为空
	if len(products) > 0 {
		// 遍历 products
		for _, product := range products {
			data := definition.OrderProductImgData{
				TitleImg:  product.Image,
				Title:     product.Title,
				ProductId: product.ProductId,
				Quantity:  product.Quantity,
			}

			data.Variant = product.Sku
			// 将数据添加到 imageInfo 数组中
			imageInfo = append(imageInfo, data)
		}
	}
	return imageInfo
}

func (p *OrderProduct) UpdateOrCreate(condition map[string]interface{}, attributes map[string]interface{}) error {
	var orderProduct models.OrderProduct
	// 尝试查找记录
	userId, _ := condition["user_id"].(int)
	database.DB.Scopes(orderProduct.TableOfUser(orderProduct.TableName(), userId)).Where(condition).First(&orderProduct)
	// 如果找到了记录，则更新它
	if orderProduct.ID > 0 {
		if err := database.DB.Scopes(orderProduct.TableOfUser(orderProduct.TableName(), userId)).Model(orderProduct).Updates(attributes).Error; err != nil {
			return err
		}
		return nil
	} else {
		// 如果未找到记录，则创建新的记录
		createData := helper.MergeMaps(condition, attributes)
		if err := database.DB.Scopes(orderProduct.TableOfUser(orderProduct.TableName(), userId)).Create(createData).Error; err != nil {
			return err
		}
		return nil
	}
}
