package service

import (
	"encoding/json"
	"fmt"
	"github.com/dromara/carbon/v2"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"sort"
	"strconv"
	"strings"
	"time"
	"tmshopify/config"
	"tmshopify/internal/app/definition"
	"tmshopify/pkg/domain"
	"tmshopify/pkg/utils/track"
	"tmshopify/store/database"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type ThirdTracking struct {
	userRepo             *impl.User
	trackInfoRepo        *impl.TrackInfo
	orderRecordRepo      *impl.OrderRecord
	orderTrackRepo       *impl.OrderTrack
	courierRepo          *impl.Courier
	trackPageThemeRepo   *impl.TrackPageTheme
	trackPageSettingRepo *impl.TrackPageSetting
}

type Tracking struct {
	Trackinfo             []definition.TrackInfo `json:"trackinfo"`
	DeliveryStatus        string                 `json:"delivery_status"`
	CourierCode           string                 `json:"courier_code"`
	CourierName           string                 `json:"courier_name"`
	Substatus             string                 `json:"substatus"`
	Destination           string                 `json:"destination"`
	Original              string                 `json:"original"`
	ScheduledDeliveryDate string                 `json:"scheduled_delivery_date"`
}

type Data struct {
	Tracking       Tracking `json:"tracking"`
	TrackingNumber string   `json:"tracking_number"`
	OrderName      string   `json:"order_name"`
}

type TrackPageThemeSettingStruct struct {
	GoogleTranslate bool `json:"google_translate"`
	General         struct {
		Width            int    `json:"width"`
		Font             string `json:"font"`
		EstimatedShow    int    `json:"estimated_show"`
		OrderLookup      bool   `json:"order_lookup"`
		ProgressBar      bool   `json:"progress_bar"`
		TrackingHistory  bool   `json:"tracking_history"`
		CouponSell       bool   `json:"coupon_sell"`
		DetailInfo       bool   `json:"detail_info"`
		Border           bool   `json:"border"`
		TextColor        string `json:"text_color"`
		PrimaryColor     string `json:"primary_color"`
		ShipmentReview   bool   `json:"shipment_review"`
		FeaturedProducts bool   `json:"featured_products"`
	} `json:"general"`
	OrderLookup struct {
		OrderNumber int `json:"order_number"`
	} `json:"order_lookup"`
	ProgressBar struct {
		DateFormat int    `json:"date_format"`
		TimeFormat int    `json:"time_format"`
		Color      string `json:"color"`
	} `json:"progress_bar"`
	TrackingHistory struct {
		DateFormat      int    `json:"date_format"`
		TimeFormat      int    `json:"time_format"`
		BackgroundColor string `json:"background_color"`
		Color           string `json:"color"`
		Map             string `json:"map"`
	} `json:"tracking_history"`
	DetailInfo struct {
		Courier   bool `json:"courier"`
		Number    bool `json:"number"`
		LineItems bool `json:"line_items"`
		Note      bool `json:"note"`
	} `json:"detail_info"`
	FeaturedProducts struct {
		BeforeTracking struct {
			Collection int  `json:"collection"`
			Selected   int  `json:"selected"`
			Open       bool `json:"open"`
		} `json:"before_tracking"`
		AfterTracking struct {
			Collection int  `json:"collection"`
			Selected   int  `json:"selected"`
			Open       bool `json:"open"`
			Position   int  `json:"position"`
		} `json:"after_tracking"`
	} `json:"featured_products"`
	CouponSell struct {
		Display         string `json:"display"`
		MainTitle       string `json:"main_title"`
		Description     string `json:"description"`
		CouponCode      string `json:"coupon_code"`
		ButtonText      string `json:"button_text"`
		ButtonLink      string `json:"button_link"`
		BackgroundColor string `json:"background_color"`
	} `json:"coupon_sell"`
	ShipmentReview struct {
		StarColor string `json:"star_color"`
	} `json:"shipment_review"`
}

type TrackPageThemeTranslationStruct struct {
	TrackYourOrder          string `json:"track_your_order"`
	Order                   string `json:"order"`
	Status                  string `json:"status"`
	MayLike                 string `json:"may_like"`
	OrderNumber             string `json:"order_number"`
	TrackNumber             string `json:"track_number"`
	Email                   string `json:"email"`
	Track                   string `json:"track"`
	Or                      string `json:"or"`
	EnterYourOrder          string `json:"enter_your_order"`
	EnterYourTrackingNumber string `json:"enter_your_tracking_number"`
	EnterYourEmail          string `json:"enter_your_email"`
	IncorrectEmail          string `json:"incorrect_email"`
	Ordered                 string `json:"ordered"`
	OrderReady              string `json:"order_ready"`
	Transit                 string `json:"transit"`
	Pickup                  string `json:"pickup"`
	Delivered               string `json:"delivered"`
	Pending                 string `json:"pending"`
	Expired                 string `json:"expired"`
	Undelivered             string `json:"undelivered"`
	Exception               string `json:"exception"`
	InfoReceive             string `json:"info_receive"`
	OrderNotFound           string `json:"order_not_found"`
	WaitingUpdate           string `json:"waiting_update"`
	NotYetShipped           string `json:"not_yet_shipped"`
	Carrier                 string `json:"carrier"`
	Product                 string `json:"product"`
	Note                    string `json:"note"`
	ExpectedDeliveryDate    string `json:"expected_delivery_date"`
	ShippingTo              string `json:"shipping_to"`
	CurrentLocation         string `json:"current_location"`
	Shipment                string `json:"shipment"`
	EnterYourPhoneNumber    string `json:"enter_your_phone_number"`
	IncorrectPhoneNumber    string `json:"incorrect_phone_number"`
	ReviewTitle             string `json:"review_title"`
	Phone                   string `json:"phone"`
}

func NewThirdTracking() *ThirdTracking {
	return &ThirdTracking{
		userRepo:             &impl.User{},
		trackInfoRepo:        &impl.TrackInfo{},
		orderRecordRepo:      &impl.OrderRecord{},
		orderTrackRepo:       &impl.OrderTrack{},
		courierRepo:          &impl.Courier{},
		trackPageThemeRepo:   &impl.TrackPageTheme{},
		trackPageSettingRepo: &impl.TrackPageSetting{},
	}
}

func (t *ThirdTracking) GetInstallData(shop string) (models.Users, error) {
	data, err := t.userRepo.FirstActiveUserIdByShop(shop)
	return data, err
}

func (t *ThirdTracking) dealInfoData(orderData models.OrderTracks) (Data, error) {
	var ScheduledDeliveryDateString string
	userId := orderData.UserId
	orderId := orderData.OrderId
	logrus.Warnf("order %s track data:%d", orderId, userId)
	orderRecord, err := t.orderRecordRepo.FirstByOrderId(userId, orderId)
	if err != nil {
		logrus.Info(err.Error())
		return Data{}, err
	}
	var deliveryStatus string
	orderTrack := orderData
	var info []definition.TrackInfo
	trackInfos := orderTrack.TrackInfos
	// 排除空数组情况
	if len(orderRecord.CustomStatusTime) > 0 && orderRecord.CustomStatusTime != "[]" {
		// 如果自定义了订单状态，优先显示自定义订单状态
		// 解析发件信息JSON字符串到结构体
		var customStatusTimeStruct map[config.StatusNodeNumber]int
		err = json.Unmarshal([]byte(orderRecord.CustomStatusTime), &customStatusTimeStruct)
		if err != nil {
			fmt.Println("解析CustomStatusTime json失败:", err)
		} else {
			var dateFormat = 6
			var timeFormat = 1
			translation := map[string]string{
				"ordered":     "Ordered",
				"order_ready": "Order Ready",
				"transit":     "In Transit",
				"pickup":      "Out for Delivery",
				"delivered":   "Delivered",
			}

			trackPageSetting, err := t.trackPageSettingRepo.FirstByUserId(userId)
			if err == nil {
				trackPageTheme, err := t.trackPageThemeRepo.FirstByThemeId(trackPageSetting.ThemeId)
				if err == nil {
					//获取用户主题设置信息
					var trackPageThemeSettingStruct TrackPageThemeSettingStruct
					var trackPageThemeTranslationStruct TrackPageThemeTranslationStruct

					err = json.Unmarshal([]byte(trackPageTheme.Settings), &trackPageThemeSettingStruct)
					if err == nil {
						trackingHistory := trackPageThemeSettingStruct.TrackingHistory
						dateFormat = trackingHistory.DateFormat
						timeFormat = trackingHistory.TimeFormat
					} else {
						fmt.Println("解析trackPageThemeSettingStruct json失败:", err)
					}

					err = json.Unmarshal([]byte(trackPageTheme.Translation), &trackPageThemeTranslationStruct)
					if err == nil {
						translation["ordered"] = trackPageThemeTranslationStruct.Ordered
						translation["order_ready"] = trackPageThemeTranslationStruct.OrderReady
						translation["transit"] = trackPageThemeTranslationStruct.Transit
						translation["pickup"] = trackPageThemeTranslationStruct.Pickup
						translation["delivered"] = trackPageThemeTranslationStruct.Delivered
					} else {
						fmt.Println("解析trackPageThemeTranslationStruct json失败:", err)
					}
				}
			}
			for k, item := range customStatusTimeStruct {
				// 格式化日期
				checkpointDate := int64(item)
				info = append(info, definition.TrackInfo{
					CheckpointDate:              track.HandleTimeFormatBySet(checkpointDate, dateFormat, timeFormat, false, false),
					TrackingDetail:              track.GetStatusSummary(translation, k),
					Location:                    "",
					CheckpointDeliveryStatus:    track.GetCheckpointStatus(k),
					CheckpointDeliverySubstatus: "",
				})
			}
		}
	} else {

		if trackInfos.OriginInfo != "" {
			// 解析发件信息JSON字符串到结构体
			var originInfo definition.TrackingInfoData
			err = json.Unmarshal([]byte(trackInfos.OriginInfo), &originInfo)
			if err != nil {
				fmt.Println("解析OriginInfo json失败:", err)
			} else {
				info = append(info, originInfo.TrackInfo...)
			}
		}

		if trackInfos.DestinationInfo != "" {
			// 解析收件信息JSON字符串到结构体
			var destinationInfo definition.TrackingInfoData
			err = json.Unmarshal([]byte(trackInfos.DestinationInfo), &destinationInfo)
			if err != nil {
				fmt.Println("解析DestinationInfo json失败:", err)
			} else {
				info = append(info, destinationInfo.TrackInfo...)
			}
		}

		if len(info) > 0 {
			sort.Slice(info, func(i, j int) bool {
				// 将日期字符串转换为时间对象
				timeI, _ := time.Parse(time.DateTime, info[i].CheckpointDate)
				timeJ, _ := time.Parse(time.DateTime, info[j].CheckpointDate)
				// 比较时间
				return timeI.After(timeJ)
			})
		}
	}
	deliveryStatus = getTrackList()[orderTrack.TrackStatus]
	courierData := t.courierRepo.GetCourierAndName("en")
	if trackInfos.ExpectedDeliveryTime.IsZero() {
		ScheduledDeliveryDateString = ""
	} else {
		ScheduledDeliveryDateString = trackInfos.ExpectedDeliveryTime.Format(time.DateOnly)
	}
	data := Data{
		Tracking: Tracking{
			Trackinfo:             info,
			DeliveryStatus:        deliveryStatus,                  //最终状态
			CourierCode:           orderTrack.Courier,              //物流商简码
			CourierName:           courierData[orderTrack.Courier], //物流商名称
			Substatus:             trackInfos.SubStatus,            //最新物流信息的子状态
			Destination:           trackInfos.DestinationCountry,   //目的国简码
			Original:              trackInfos.OriginalCountry,      //发件国简码
			ScheduledDeliveryDate: ScheduledDeliveryDateString,     //预计到达时间
		},
		TrackingNumber: orderTrack.TrackNumber, //运单号
		OrderName:      orderTrack.OrderName,   //订单名如#1001
	}
	return data, err
}

func (t *ThirdTracking) GetOrderTrackingData(shop string, orderName string, email string) (Data, error) {
	user, _ := t.userRepo.FirstByStoreName(shop)
	if user.ID == 0 {
		return Data{}, nil
	}
	originOrderName := orderName
	//检查订单号是否携带 # 号 没有则添加
	res := strings.HasPrefix(originOrderName, "#")
	if !res {
		orderName = "#" + originOrderName
	}
	//在提取单号信息
	trackQuery := t.orderTrackRepo.PreloadTrackInfo(user.ID).Where("is_delete = ?", 0)
	//如果是订单查询
	//如果没有携带 # 号查询 那么同时查询有和没有 # 号的订单号
	if orderName != originOrderName {
		trackQuery.Where("order_name IN (?)", []string{orderName, originOrderName})
	} else {
		trackQuery.Where("order_name = ?", orderName)
	}
	if len(email) > 0 {
		trackQuery.Where("customer_email = ?", email)
	}
	var numberMessage models.OrderTracks
	// 查询可能会有多个结果
	trackQuery.First(&numberMessage)
	return t.dealInfoData(numberMessage)
}

func (t *ThirdTracking) GetTrackingData(shop string, tracking string) (Data, error) {
	user, _ := t.userRepo.FirstByStoreName(shop)
	if user.ID == 0 {
		return Data{}, nil
	}
	var orderTrack models.OrderTracks
	err := t.orderTrackRepo.PreloadTrackInfo(user.ID).Where("is_delete = ?", 0).Where("track_number = ?", tracking).Order("created_at desc").First(&orderTrack)
	if err.Error != nil || orderTrack.ID == 0 {
		return Data{}, err.Error
	}
	return t.dealInfoData(orderTrack)

}

func (t *ThirdTracking) GetGorgiasTracking(user models.Users, email string) []map[string]string {
	userId := user.ID
	var orderTrack []models.OrderTracks
	var orderProduct models.OrderProduct

	err := t.orderTrackRepo.PreloadTrackInfo(user.ID).
		Preload("OrderProducts", func(db *gorm.DB) *gorm.DB {
			return database.DB.Scopes(orderProduct.TableOfUser(orderProduct.TableName(), userId)).Where("user_id", userId)
		}).
		Where("user_id = ? AND customer_email = ? AND is_delete = ? AND track_number != ? AND order_fulfill_time != ?", userId, email, 0, "", "").
		Limit(10).Order("order_create_time DESC").Order("order_id DESC").Find(&orderTrack).Error
	var data []map[string]string
	if err != nil {
		return data
	}

	trackPath := t.trackPageSettingRepo.GetUserTrackLink(userId)
	if trackPath == "" {
		trackPath = "apps/trackingmore"
	}
	storeRealUrl := user.StoreName
	if user.StoreRealName != "" {
		storeRealUrl = user.StoreRealName
	}

	trackingLink := fmt.Sprintf("https://%s/%s/?nums=", storeRealUrl, trackPath)
	// 用于记录是否是同一个订单的
	orderNameArr := make(map[string]int)
	for _, order := range orderTrack {
		orderNameArr[order.OrderName]++
	}
	for _, order := range orderTrack {
		orderName := order.OrderName
		if orderNameArr[orderName] > 1 {
			orderName = fmt.Sprintf("%s-F%d", orderName, orderNameArr[orderName])
			orderNameArr[orderName]--
		}
		courier := t.courierRepo.GetCompanyInfoByCodeAndLang(order.Courier, "en")
		trackInfos := order.TrackInfos
		products := order.OrderProducts

		var info []definition.TrackInfo
		var lastCheckoutPoint, lastCheckpointTime, residenceTime string
		if trackInfos.OriginInfo != "" {
			// 解析发件信息JSON字符串到结构体
			var originInfo definition.TrackingInfoData
			err = json.Unmarshal([]byte(trackInfos.OriginInfo), &originInfo)
			if err != nil {
				logrus.Warning("解析OriginInfo json失败:", err)
			} else {
				info = append(info, originInfo.TrackInfo...)
				residenceTime = originInfo.ReceivedDate
			}
		}

		if trackInfos.DestinationInfo != "" {
			// 解析收件信息JSON字符串到结构体
			var destinationInfo definition.TrackingInfoData
			err = json.Unmarshal([]byte(trackInfos.DestinationInfo), &destinationInfo)
			if err != nil {
				logrus.Warning("解析DestinationInfo json失败:", err)
			} else {
				info = append(info, destinationInfo.TrackInfo...)
				residenceTime = destinationInfo.ReceivedDate
			}
		}

		if len(info) > 0 {
			sort.Slice(info, func(i, j int) bool {
				// 将日期字符串转换为时间对象
				timeI, _ := time.Parse(time.DateTime, info[i].CheckpointDate)
				timeJ, _ := time.Parse(time.DateTime, info[j].CheckpointDate)
				// 比较时间
				return timeI.After(timeJ)
			})
			lastCheckoutPoint = info[0].TrackingDetail
			lastCheckpointTime = info[0].CheckpointDate
		}

		if residenceTime != "" {
			residenceTime = carbon.Parse(residenceTime).Format("M d,Y H:i:s")
		}
		if lastCheckpointTime != "" {
			lastCheckpointTime = carbon.Parse(lastCheckpointTime).Format("M d,Y H:i:s")
		}

		var productNames []string
		for _, product := range products {
			productNames = append(productNames, product.Title)
		}
		productName := strings.Join(productNames, "; ")
		data = append(data, map[string]string{
			"tracking_link":          trackingLink + order.TrackNumber,
			"shipment_status":        getTrackList()[order.TrackStatus],
			"order_number":           orderName,
			"order_created_at":       carbon.CreateFromTimestamp(int64(order.OrderCreateTime)).Format("M d,Y H:i:s"),
			"tracking_number":        order.TrackNumber,
			"carrier_name":           courier.CompanyName,
			"carrier_contact":        courier.CompanyTel,
			"transit_time":           strconv.Itoa(int(order.TransitTime)),
			"last_check_point":       lastCheckoutPoint,
			"last_checkpoint_time":   lastCheckpointTime,
			"residence_time":         residenceTime,
			"customer_email":         order.CustomerEmail,
			"customer_phone":         order.CustomerPhone,
			"fulfillment_created_at": carbon.CreateFromTimestamp(int64(order.OrderFulfillTime)).Format("M d,Y H:i:s"),
			"product_name":           productName,
		})
	}
	if len(data) == 0 {
		data = []map[string]string{
			{
				"carrier_contact":        "Example carrier contact",
				"carrier_name":           "Example carrier",
				"customer_email":         "<EMAIL>",
				"customer_phone":         "",
				"fulfillment_created_at": carbon.Now().Format("M d,Y H:i:s"),
				"last_check_point":       "",
				"last_checkpoint_time":   "",
				"order_created_at":       carbon.Now().Format("M d,Y H:i:s"),
				"order_number":           "No recent shipments",
				"product_name":           "Example product",
				"residence_time":         "",
				"shipment_status":        "Pending",
				"tracking_link":          fmt.Sprintf("https://%s/%s/?nums=", storeRealUrl, trackPath) + domain.PreviewNumber,
				"tracking_number":        "Example tracking number",
				"transit_time":           "0",
			},
		}
	}
	return data
}

func getTrackList() map[uint]string {
	return map[uint]string{
		0: "Pending",
		1: "Info Received",
		2: "In Transit",
		3: "Out for Delivery",
		4: "Delivered",
		5: "Expired",
		6: "Failed Attempt",
		7: "Exception",
	}
}
