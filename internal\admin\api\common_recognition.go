package api

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"

	"tmshopify/internal/admin/request"
	"tmshopify/internal/admin/service"
	"tmshopify/pkg/utils/paginate"
	"tmshopify/server/response"
	"tmshopify/store/models"
)

type CommonRecognition struct {
}

func (c *CommonRecognition) TotalTable(ctx *gin.Context) {
	result := service.NewCommonRecognition().TotalTable()
	response.Success(ctx, &response.SuccessResponse{
		Data: result,
	})
}

func (c *CommonRecognition) DataTable(ctx *gin.Context) {
	trackLength := ctx.Query("track_length")
	length, err := strconv.Atoi(trackLength)
	if err != nil {
		response.Fail(ctx, err)
		return
	}
	data, err := service.NewCommonRecognition().DataTable(length)
	if err != nil {
		response.Fail(ctx, err)
		return
	}
	response.Success(ctx, data)
}

func (c *CommonRecognition) CreateCommonRule(ctx *gin.Context) {
	user := ctx.MustGet("User").(models.AdminUsers)
	var m models.CourierTopRules
	if err := ctx.ShouldBindJSON(&m); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	var validate = validator.New()
	validate.RegisterStructValidationMapRules(request.CreateCommonRule, models.CourierTopRules{})
	if err := validate.Struct(m); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	m.CreatedBy = user.ID
	m.UpdatedBy = user.ID
	err := service.NewCommonRecognition().CreateCommonRule(m)
	if err != nil {
		response.Fail(ctx, err)
		return
	}
	response.Created(ctx)
}

func (c *CommonRecognition) UpdateCommonRule(ctx *gin.Context) {
	user := ctx.MustGet("User").(models.AdminUsers)
	var m models.CourierTopRules
	if err := ctx.ShouldBindJSON(&m); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	var validate = validator.New()
	validate.RegisterStructValidationMapRules(request.UpdateCommonRule, models.CourierTopRules{})
	if err := validate.Struct(m); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	m.UpdatedBy = user.ID
	err := service.NewCommonRecognition().UpdateCommonRule(m)
	if err != nil {
		response.Fail(ctx, err)
		return
	}
	response.NotContent(ctx)
}

func (c *CommonRecognition) DeleteCommonRule(ctx *gin.Context) {
	ID := ctx.Param("id")
	err := service.NewCommonRecognition().DeleteCommonRule(ID)
	if err != nil {
		response.InternalServerError(ctx, err.Error())
		return
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: ID,
	})
}

func (c *CommonRecognition) GetCommonRuleList(ctx *gin.Context) {
	var searchParam struct {
		paginate.Param
	}
	_ = ctx.ShouldBind(&searchParam)
	result, err := service.NewCommonRecognition().GetCommonRuleList(&searchParam.Param)
	if err != nil {
		response.Fail(ctx, err)
		return
	}

	response.Success(ctx, result)
}
