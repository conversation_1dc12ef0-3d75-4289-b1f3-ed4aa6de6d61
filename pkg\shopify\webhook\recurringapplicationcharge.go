package webhook

import (
	"time"
)

type RecurringApplicationCharge struct {
	AppSubscription struct {
		AdminGraphqlApiId     string    `json:"admin_graphql_api_id"`
		Name                  string    `json:"name"`
		Status                string    `json:"status"`
		AdminGraphqlApiShopId string    `json:"admin_graphql_api_shop_id"`
		CreatedAt             time.Time `json:"created_at"`
		UpdatedAt             time.Time `json:"updated_at"`
		Currency              string    `json:"currency"`
		CappedAmount          string    `json:"capped_amount"`
	} `json:"app_subscription"`
}
