package auth

import "time"

type Sessions struct {
	ID          int64     `json:"id" gorm:"column:id"`
	SessionID   string    `json:"session_id" gorm:"column:session_id"`
	Shop        string    `json:"shop" gorm:"column:shop"`
	IsOnline    int64     `json:"is_online" gorm:"column:is_online"`
	State       string    `json:"state" gorm:"column:state"`
	CreatedAt   time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"column:updated_at"`
	Scope       string    `json:"scope" gorm:"column:scope"`
	AccessToken string    `json:"access_token" gorm:"column:access_token"`
	UID         int64     `json:"uid" gorm:"column:uid"`
}
