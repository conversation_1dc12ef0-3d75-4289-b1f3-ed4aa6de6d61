package server

import (
	"net/http"
	"tmshopify/internal/app/api"
	"tmshopify/server/middleware"
	"tmshopify/server/response"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func InitAppRouter(router *gin.Engine) *gin.Engine {

	err := router.SetTrustedProxies([]string{"*************"})
	if err != nil {
		return nil
	}

	v2 := router.Group("/api/v2")
	{
		ThirdTracking := new(api.ThirdTracking)
		//集成app对外接口
		v2.GET("/:thirdpart/install", middleware.RequestLimiter("240-M"), ThirdTracking.ThirdpartInstall)    //thirdpart查询安装状态
		v2.POST("/:thirdpart/tracking", middleware.RequestLimiter("240-M"), ThirdTracking.ThirdpartTracking) //thirdpart单号查询
		v2.POST("/", func(c *gin.Context) {
			logrus.Warnf("ClientIP: %s\n", c.ClientIP())
			response.Success(c, &response.SuccessResponse{
				Code: http.StatusOK,
				Data: map[string]interface{}{
					"test": 123,
				},
			})
		})
		v2.GET("/gorgias_tracking", ThirdTracking.GorgiasTracking) //Gorgias 插件查询接口

		trackPageGroup := v2.Group("/track-page")
		{
			TrackPage := new(api.TrackPage)
			trackPageGroup.GET("/back-data", TrackPage.Tracking)
			trackPageGroup.GET("/recommend-product", TrackPage.RecommendProduct)
			trackPageGroup.GET("/collection-product", TrackPage.CollectionProduct)
			trackPageGroup.POST("/review/:shop", TrackPage.ReviewList)
			trackPageGroup.PUT("/review/:shop", TrackPage.DoReview)
			trackPageGroup.GET("/order-product", TrackPage.OrderProduct)
			trackPageGroup.GET("/plugin/back-data", TrackPage.PluginTracking)
			trackPageGroup.POST("/click-product", TrackPage.ProductClick)
		}

		checkoutGroup := v2.Group("/checkout")
		{
			Checkout := new(api.Checkout)
			checkoutGroup.GET("/version", Checkout.GetVersion)
			checkoutGroup.GET("/setting", Checkout.GetSetting)
			checkoutGroup.GET("/order_status_widget", Checkout.GetOrderStatus)
		}
	}
	return router

}
