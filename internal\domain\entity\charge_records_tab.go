package entity

import "time"

// ChargeRecords 充值记录表
type ChargeRecords struct {
	ID                 uint64     `json:"id" gorm:"column:id;primaryKey;autoIncrement"`          // 主键ID
	UserID             int64      `json:"user_id" gorm:"column:user_id"`                         // 用户ID
	ChargeID           int64      `json:"charge_id" gorm:"column:charge_id"`                     // 费用id
	PlanID             *uint64    `json:"plan_id" gorm:"column:plan_id"`                         // 套餐id
	ApplicationCharge  string     `json:"application_charge" gorm:"column:application_charge"`   // 创建费用响应内容
	Status             int8       `json:"status" gorm:"column:status"`                           // 是否激活，0：待处理，1：激活，2：已接受，3：已拒绝，4：已过期，5：已冻结，6：已取消，7：已确定
	Test               int8       `json:"test" gorm:"column:test"`                               // 是否测试
	Price              float64    `json:"price" gorm:"column:price"`                             // 充值金额
	DiscountAmount     float64    `json:"discount_amount" gorm:"column:discount_amount"`         // 折扣金额
	DiscountPercentage float64    `json:"discount_percentage" gorm:"column:discount_percentage"` // 折扣比
	DiscountInterval   int64      `json:"discount_interval" gorm:"column:discount_interval"`     // 优惠轮次
	FreeTrialDays      int64      `json:"free_trial_days" gorm:"column:free_trial_days"`         // 免费套餐天数
	ActivatedAt        *time.Time `json:"activated_at" gorm:"column:activated_at"`               // 套餐激活时间
	ExpiredAt          *time.Time `json:"expired_at" gorm:"column:expired_at"`                   // 收费到期时间
	Version            string     `json:"version" gorm:"column:version"`                         // 套餐版本
	BillingCycle       string     `json:"billing_cycle" gorm:"column:billing_cycle"`             // 计费周期：monthly,annual,onetime
	CreatedAt          *time.Time `json:"created_at" gorm:"column:created_at"`                   // 创建时间
	UpdatedAt          *time.Time `json:"updated_at" gorm:"column:updated_at"`                   // 更新时间
	CouponID           int64      `json:"coupon_id" gorm:"column:coupon_id"`                     // 卡券id
}

// TableName charge_records
func (c *ChargeRecords) TableName() string {
	return "charge_records"
}
