package domain

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/dromara/carbon/v2"
	"github.com/sirupsen/logrus"

	"tmshopify/config"
	"tmshopify/internal/app/entity"
	"tmshopify/pkg/utils/helper"
	"tmshopify/pkg/utils/track"
	"tmshopify/store/models/order"
)

const (
	Edd    string = "edd"
	PreEdd string = "pre_edd"
)

type EddDomain struct {
	EddSetting entity.EddSettingEntityStruct
	StartTime  int
	EddType    string
}

type EddSetting struct {
	config.Estimate
	UserTimeZone int    `json:"user_time_zone"`
	TimeZone     string `json:"time_zone"`
}

func (e *EddDomain) InitEddDomain(setting entity.EddSettingEntityStruct, startTime int, eddType string) *EddDomain {
	e.EddSetting = setting
	e.StartTime = startTime
	e.EddType = eddType
	return e
}

// CalculateEstimateTime 计算预计到达时间
func (e *EddDomain) CalculateEstimateTime() time.Time {
	if len(e.EddSetting.WorkDays) == 0 {
		return time.Time{}
	}
	loc := time.Local
	if e.EddSetting.TimeZone != "" {
		timeZone := e.EddSetting.TimeZone
		location := track.ConvertTimeZone(timeZone)
		var err error
		loc, err = time.LoadLocation(location)
		if err != nil {
			loc = time.FixedZone("UTC", 0)
			logrus.Errorf("Load location: %s happened a error: %s user time zone: %s", location, err.Error(), timeZone)
		}
	}
	var edd time.Time

	if e.EddType == Edd {
		startTime := time.Unix(int64(e.StartTime), 0)
		// 再次检查时区是否有效
		if loc == nil {
			loc = time.FixedZone("UTC", 0)
			logrus.Errorf("loc is nil, user time zone setting: %s", e.EddSetting.TimeZone)
		}
		edd = time.Date(startTime.Year(), startTime.Month(), startTime.Day(), startTime.Hour(), 0, 0, 0, loc)
		// 如果是使用发货时间作为起始时间，则不进行后续计算(上线时间2025-01-02 02:15:30, 未更新过的设置依旧使用旧逻辑)
		if e.EddSetting.CalType == entity.EstimateCalFulfill && e.EddSetting.UpdateAt.Unix() > 1735786530 {
			return edd
		}
	} else {
		edd = time.Now().Truncate(time.Hour)
	}

	if edd.Hour() > cutoffHour(e.EddSetting.CutoffTime) {
		edd = edd.AddDate(0, 0, 1)
	}

	if e.EddSetting.ProgressTime != 0 {
		startTime := edd.Unix()
		endTime := e.getWeekDay(startTime, 0)
		edd = edd.AddDate(0, 0, int((endTime-startTime)/86400))
	}

	return edd
}

// getWeekDay 排除非工作日，获取结算时间日期
func (e *EddDomain) getWeekDay(startTime int64, restDay int) int64 {
	weekDays := map[int]string{
		1: "Mon",
		2: "Tue",
		3: "Wed",
		4: "Thu",
		5: "Fri",
		6: "Sat",
		0: "Sun",
	}
	dailyTime := int64(0)

	for i := 0; i < e.EddSetting.ProgressTime; i++ {
		if restDay >= e.EddSetting.ProgressTime {
			break
		}

		dailyTime = startTime + int64(i*86400)
		week := time.Unix(dailyTime, 0).Weekday()
		weekDayStr := weekDays[int(week)]

		if contains(e.EddSetting.WorkDays, weekDayStr) {
			restDay++
		}
	}

	if restDay < e.EddSetting.ProgressTime {
		dailyTime = e.getWeekDay(dailyTime+86400, restDay)
	}

	return dailyTime
}

func cutoffHour(cutoffTime string) int {
	hourStr := strings.TrimSuffix(cutoffTime, ":00")
	hour, _ := time.Parse("15", hourStr)
	return hour.Hour()
}

func contains(slice []string, target string) bool {
	for _, value := range slice {
		if value == target {
			return true
		}
	}
	return false
}

// CalculateDaysSkippingWeekend 运输时间忽略周末
func CalculateDaysSkippingWeekend(startTime time.Time, days int) int {
	restDay := 0   // 已计算的有效工作日
	totalDays := 0 // 累计天数

	for restDay < days {
		totalDays++ // 每次循环代表往后推一天
		// 计算当前日期
		currentDay := startTime.AddDate(0, 0, totalDays)
		// 获取当前是星期几（0 是周日，6 是周六，1~5 是工作日）
		weekDay := currentDay.Weekday()

		// 如果是工作日（周一到周五）
		if weekDay >= time.Monday && weekDay <= time.Friday {
			restDay++ // 工作日计数
		}
	}

	return totalDays // 返回总共经过的天数
}

// CalculateEddResults 计算 EDD 结果
func (e *EddDomain) CalculateEddResults(edd time.Time, code string, destinationInfo struct {
	Id        int
	Continent string
}) struct {
	StartShow int64
	EndShow   int64
} {
	var data struct {
		StartShow int64
		EndShow   int64
	}
	// 增加的天数
	var minRange, maxRange int
	if destinationInfo.Id == 0 {
		return data
	}
	for _, value := range e.EddSetting.EddRange {
		for _, region := range value.Region {
			if _, isString := region.(string); (!isString && int(region.(float64)) == destinationInfo.Id) ||
				(isString && region.(string) == destinationInfo.Continent) {
				minRange = value.MinRange
				maxRange = value.MaxRange
			}
		}
	}
	// 判断是否可以使用默认规则
	var check bool
	// 如果没设置过可用国家则默认全可用
	if len(e.EddSetting.Countries.Countries) == 0 && len(e.EddSetting.Countries.Continent) == 0 {
		check = true
	}
	// 存在符合的国家则可用默认规则
	if len(e.EddSetting.Countries.Countries) != 0 {
		for _, item := range e.EddSetting.Countries.Countries {
			if item.Code == code {
				check = true
			}
		}
	}
	// 存在符合的大洲则可用默认规则
	if len(e.EddSetting.Countries.Continent) != 0 {
		for _, item := range e.EddSetting.Countries.Continent {
			if item == destinationInfo.Continent {
				check = true
			}
		}
	}
	// 使用默认设置
	if check && minRange == 0 && maxRange == 0 {
		minRange = e.EddSetting.TransitMin
		maxRange = e.EddSetting.TransitMax
	}
	if minRange != 0 && maxRange != 0 {
		// 是否忽略周末
		if e.EddSetting.WorkOnWeekend == 1 {
			minRange = CalculateDaysSkippingWeekend(edd, minRange)
			maxRange = CalculateDaysSkippingWeekend(edd, maxRange)
		}
		data.StartShow = edd.AddDate(0, 0, minRange).Unix()
		data.EndShow = edd.AddDate(0, 0, maxRange).Unix()
	}
	return data
}

// CalculateEddResultsByCalEntity  计算 EDD 结果
func (e *EddDomain) CalculateEddResultsByCalEntity(edd time.Time, calculateEntity entity.CalculateEntity, destinationInfo struct {
	Id        int
	Continent string
}) struct {
	StartShow int64
	EndShow   int64
} {
	var data struct {
		StartShow int64
		EndShow   int64
	}
	// 增加的天数
	var minRange, maxRange int
	if destinationInfo.Id == 0 {
		return data
	}
	for _, value := range e.EddSetting.EddRange {
		for _, region := range value.Region {
			if _, isString := region.(string); (!isString && int(region.(float64)) == destinationInfo.Id) ||
				(isString && region.(string) == destinationInfo.Continent) {
				minRange = value.MinRange
				maxRange = value.MaxRange
			}
		}
	}
	// 判断是否可以使用默认规则
	var check bool
	// 如果没设置过可用国家则默认全可用
	if len(e.EddSetting.Countries.Countries) == 0 && len(e.EddSetting.Countries.Continent) == 0 {
		check = true
	}
	// 存在符合的国家则可用默认规则
	if len(e.EddSetting.Countries.Countries) != 0 {
		for _, item := range e.EddSetting.Countries.Countries {
			if item.Code == calculateEntity.Destination {
				check = true
			}
		}
	}
	// 存在符合的大洲则可用默认规则
	if len(e.EddSetting.Countries.Continent) != 0 {
		for _, item := range e.EddSetting.Countries.Continent {
			if item == destinationInfo.Continent {
				check = true
			}
		}
	}
	if check && calculateEntity.OrderEstimate.ID != "" {
		pickupDate := edd.Format(time.DateTime)
		eddC := requestAiEdd(pickupDate, calculateEntity.TrackNumber, calculateEntity.OrderEstimate)

		if eddC.EndShow != 0 && eddC.StartShow != 0 {
			data.StartShow = eddC.StartShow
			data.EndShow = eddC.EndShow
			return data
		}
	}
	// 使用默认设置
	if check && minRange == 0 && maxRange == 0 {
		minRange = e.EddSetting.TransitMin
		maxRange = e.EddSetting.TransitMax
	}
	if minRange != 0 && maxRange != 0 {
		// 是否忽略周末
		if e.EddSetting.WorkOnWeekend == 1 {
			minRange = CalculateDaysSkippingWeekend(edd, minRange)
			maxRange = CalculateDaysSkippingWeekend(edd, maxRange)
		}
		data.StartShow = edd.AddDate(0, 0, minRange).Unix()
		data.EndShow = edd.AddDate(0, 0, maxRange).Unix()
	}
	return data
}

// requestAiEdd 调用 AI 预计送达时间 (EDD) 接口，根据订单预估数据生成预计送达时间信息。
// 参数 pickupTime 表示揽收时间字符串，orderEstimate 是 OrderEstimate 类型的订单预估信息。
// 返回值为一个结构体，包含开始显示时间和结束显示时间字段。
func requestAiEdd(pickupTime string, trackNumber string, orderEstimate order.Estimate) struct {
	StartShow int64
	EndShow   int64
} {
	estimateRes := struct {
		StartShow int64
		EndShow   int64
	}{0, 0}
	defer func() {
		if err := recover(); err != nil {
			// Log the exception and track number
			logrus.Error("AI EDD API exception", map[string]interface{}{
				"message":      err,
				"track_number": orderEstimate.TrackNumber,
			})
		}
	}()
	if orderEstimate.PickupTime.Valid {
		pickupTime = orderEstimate.PickupTime.Time.String()
	}
	// Prepare the payload
	payload := []map[string]interface{}{
		{
			"id":                       fmt.Sprintf("%s_%d_%s", orderEstimate.ID, orderEstimate.UserId, trackNumber),
			"type":                     "2",
			"source":                   "1",
			"carrier_code":             orderEstimate.CarrierCode,
			"origin_country":           orderEstimate.OriginCountry,
			"origin_state":             orderEstimate.OriginState,
			"origin_city":              orderEstimate.OriginCity,
			"origin_postal_code":       orderEstimate.OriginPostalCode,
			"origin_raw_location":      fmt.Sprintf("%s,%s,%s,%s", orderEstimate.OriginRawLocation, orderEstimate.OriginCity, orderEstimate.OriginState, orderEstimate.OriginCountry),
			"destination_country":      orderEstimate.DestinationCountry,
			"destination_state":        orderEstimate.DestinationState,
			"destination_city":         orderEstimate.DestinationCity,
			"destination_postal_code":  orderEstimate.DestinationPostalCode,
			"destination_raw_location": fmt.Sprintf("%s,%s,%s,%s", orderEstimate.DestinationRawLocation, orderEstimate.DestinationCity, orderEstimate.DestinationState, orderEstimate.DestinationCountry),
			"weight":                   orderEstimate.Weight,
			"package_count":            orderEstimate.PackageCount,
			"pickup_time":              pickupTime,
			"service_code":             orderEstimate.ServiceCode,
		},
	}

	// Make the HTTP POST request
	response, err := helper.HttpPostJSON(entity.AI_EDD_API, payload)
	if err != nil {
		// Log the error and track number
		logrus.Error("AI EDD API error", map[string]interface{}{
			"response":     err.Error(),
			"track_number": orderEstimate.TrackNumber,
		})
		return estimateRes
	}

	// Check if the response was successful
	if response != nil {
		var eddApiResponse struct {
			Data []struct {
				EstimatedDeliveryDateMin string `json:"estimated_delivery_date_min"`
				EstimatedDeliveryDateMax string `json:"estimated_delivery_date_max"`
			} `json:"data"`
		}
		err := json.Unmarshal(response, &eddApiResponse)
		if err != nil {
			// Log unsuccessful response and track number
			logrus.Error("AI EDD API error", map[string]interface{}{
				"response":     string(response),
				"track_number": orderEstimate.TrackNumber,
			})
			return estimateRes
		}
		minDate := eddApiResponse.Data[0].EstimatedDeliveryDateMin
		maxDate := eddApiResponse.Data[0].EstimatedDeliveryDateMax
		if minDate == "" || maxDate == "" {
			return estimateRes
		}
		startTime := carbon.Parse(minDate)
		endTime := carbon.Parse(maxDate)
		if (startTime != nil && startTime.Error != nil) || (endTime != nil && endTime.Error != nil) {
			return estimateRes
		}
		if endTime.Lt(startTime) {
			logrus.Error("AI EDD Time rang error", map[string]interface{}{
				"response":     string(response),
				"track_number": orderEstimate.TrackNumber,
			})
		}
		return struct {
			StartShow int64
			EndShow   int64
		}{StartShow: startTime.Timestamp(), EndShow: endTime.Timestamp()}
	}

	return estimateRes
}

func NewEddDomain() *EddDomain {
	return &EddDomain{
		EddSetting: entity.EddSettingEntityStruct{},
		StartTime:  0,
		EddType:    "",
	}
}
