package stats

import (
	"fmt"
	"log"

	"github.com/channelwill/athena/graceful"
	"github.com/channelwill/athena/logger"
	"github.com/spf13/cobra"

	"tmshopify/internal/application/stats"
	"tmshopify/internal/infras/config"
	"tmshopify/internal/providers"
	"tmshopify/store/database"
)

func newPaidConversionCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "paid-conversion",
		Short: "处理用户付费转化状态更新",
		Long:  "按照状态机流程处理用户从注册到试用到付费到续费的完整生命周期状态更新",
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()

			// connect db
			db, err := config.NewDB("db_conf")
			if err != nil {
				log.Fatalf("db init error:%v", err)
			}
			// 兼容旧配置
			database.DB = db

			// 兼容旧版本
			database.InitRedis()

			// init repositories
			redisClient := database.RS
			repos := providers.NewRepositories(db, redisClient)

			// init service
			conversionService := stats.NewConversionProcessService(repos)

			logger.Info(ctx, "开始执行付费转化状态更新任务")

			// 执行状态机流程处理
			err = conversionService.ProcessUserConversionStates(ctx)
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("付费转化状态更新失败: %v", err))
				return err
			}

			logger.Info(ctx, "付费转化状态更新任务执行成功")
			graceful.Stop()
			return nil
		},
	}

	return cmd
}
