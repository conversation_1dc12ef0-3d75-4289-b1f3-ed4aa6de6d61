<?php

namespace App\Services\Impl;

use App\Models\Tracking\TrackPage\Tag;
use App\Models\User;
use App\Models\User\UserSetting;
use App\Repositories\Common\ShopifyCountryRepository;
use App\Repositories\OrderTrackRepository;
use App\Repositories\Statistics\DataDailyDeliveredProductRepository;
use App\Repositories\Statistics\DataOrderReportRepository;
use App\Repositories\Statistics\ShipmentTransitTimeDailyLogRepository;
use App\Repositories\Statistics\TrackPageClickRepository;
use App\Repositories\Tracking\Courier\CourierRepository;
use App\Repositories\Tracking\TrackPage\ReviewRepository;
use App\Repositories\Tracking\TrackPage\ReviewTagRepository;
use App\Repositories\Tracking\TrackPage\TagRepository;
use App\Repositories\Tracking\TrackPageThemeRepository;
use App\Repositories\User\UserSettingRepository;
use App\Services\Domain\Shopify\GraphqlProductDomain;
use App\Services\Interfaces\AnalyticsInterface;
use App\Utils\Redis;
use App\Utils\ShopifyUtils;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Shopify\Exception\MissingArgumentException;

class AnalyticsService implements AnalyticsInterface
{
    private ReviewRepository $reviewRep;
    private ReviewTagRepository $reviewTagRep;
    private TagRepository $tagRep;
    private TrackPageThemeRepository $trackPageThemeRep;
    private ShipmentTransitTimeDailyLogRepository $transitTimeDailyLogRep;
    private CourierRepository $courierRep;
    private ShopifyCountryRepository $shopifyCountryRep;
    private DataOrderReportRepository $dataOrderReportRep;
    private DataDailyDeliveredProductRepository $dailyDeliveredProductRep;
    private OrderTrackRepository $orderTrack;
    private UserSettingRepository $userSettingRep;
    private TrackPageClickRepository $trackPageClickRep;

    public function __construct(
        ReviewRepository                      $reviewRep,
        ReviewTagRepository                   $reviewTagRep,
        TagRepository                         $tagRep,
        TrackPageThemeRepository              $trackPageThemeRep,
        ShipmentTransitTimeDailyLogRepository $transitTimeDailyLogRep,
        CourierRepository                     $courierRep,
        ShopifyCountryRepository              $shopifyCountryRep,
        DataOrderReportRepository             $dataOrderReportRep,
        DataDailyDeliveredProductRepository   $dailyDeliveredProductRep,
        OrderTrackRepository                  $orderTrack,
        UserSettingRepository                 $userSettingRep,
        TrackPageClickRepository              $trackPageClickRep
    )
    {
        $this->reviewRep = $reviewRep;
        $this->reviewTagRep = $reviewTagRep;
        $this->tagRep = $tagRep;
        $this->trackPageThemeRep = $trackPageThemeRep;
        $this->transitTimeDailyLogRep = $transitTimeDailyLogRep;
        $this->courierRep = $courierRep;
        $this->shopifyCountryRep = $shopifyCountryRep;
        $this->dataOrderReportRep = $dataOrderReportRep;
        $this->dailyDeliveredProductRep = $dailyDeliveredProductRep;
        $this->orderTrack = $orderTrack;
        $this->userSettingRep = $userSettingRep;
        $this->trackPageClickRep = $trackPageClickRep;
    }

    public function getAverageRating(User $user, $startTime, $endTime, $courier): array
    {
        $this->reviewRep->resetModel();
        $query =  $this->reviewRep->where(['user_id'=>$user->id,]);
        if (!empty($courier)) {
            $query->whereIn('courier',$courier);
        }
        $data = $query->whereBetween('created_at',[$startTime,$endTime])->selectRaw('count(id) as sum,star')->groupBy('star')->get();
        $starList = [['sum'=>0,'percent'=>0,'star'=>5],['sum'=>0,'percent'=>0,'star'=>4],['sum'=>0,'percent'=>0,'star'=>3],['sum'=>0,'percent'=>0,'star'=>2],['sum'=>0,'percent'=>0,'star'=>1]];
        if ($data->isEmpty()) {
            return $starList;
        }
        $stars = $data->pluck('star')->toArray();
        $sums = $data->pluck('sum');
        $total = $sums->sum();
        foreach ($starList as $k=>$item) {
            $hasData = array_search($item['star'], $stars, true);
            if ($hasData!==false) {
                $starList[$k]['percent'] = (int)($sums[$hasData]*100/$total);
                $starList[$k]['sum'] = $sums[$hasData];
            }
        }
        return $starList;
    }

    public function getTagsData(User $user, $startTime, $endTime,$courier)
    {

        $allTag = $this->tagRep->get(['id','tag_type','name']);
        $goodTags = [];
        $badTags = [];
        foreach ($allTag as $tag) {
            if ($tag->tag_type===Tag::GOOD) {
                $goodTags[] = $tag->id;
            }
            if ($tag->tag_type===Tag::BAD) {
                $badTags[] = $tag->id;
            }

        }
        $goodTagSort = $this->generateTagSortByType($user->id,$courier,$startTime,$endTime,$goodTags,$allTag,Tag::GOOD);
        $badTagSort = $this->generateTagSortByType($user->id,$courier,$startTime,$endTime,$badTags,$allTag,Tag::BAD);
        return [
            'positive'=>$goodTagSort,
            'negative'=>$badTagSort
        ];
    }

    private function generateTagSortByType($user_id,$courier,$startTime,$endTime,$tagIn,$allTag,$type): array
    {
        $this->reviewTagRep->resetModel();
        $query = $this->reviewTagRep->where(['user_id'=>$user_id]);
        if (!empty($courier)) {
            $query->whereHas('review',function ($query) use ($courier){
                $query->whereIn('courier',$courier);
            });
        }
        /** @var Collection<Tag> $tagSort */
        $tagSort = $query->whereBetween('created_at',[$startTime,$endTime])->whereIn('tag_id',$tagIn)->selectRaw('count(tag_id) as count,tag_id')->groupBy('tag_id')->orderBy('count','desc')->limit(3)->get();
        if ($tagSort->isEmpty()) {
            $tagSort = [];

            $lang = $this->trackPageThemeRep->getUserTagLang($user_id);
            $tags = [];
            if ($lang !== 'en') {
                $tags = $this->tagRep->getTagTranslation($lang);
                $tagIdList = \Arr::pluck($tags, "id");
                $tags = array_combine($tagIdList, $tags);
            }

            foreach ($allTag as $tag) {
                if ($tag->tag_type===$type) {
                    $tagSort[] = [
                        'name'=> empty($tags)?$tag->name:$tags[$tag->id]["name"],
                        'tag_id'=>$tag->id,
                        'count'=>0,
                    ];
                    if (count($tagSort)===3) {
                        break;
                    }
                }
            }
        }else{
            $lang = $this->trackPageThemeRep->getUserTagLang($user_id);
            if ($lang !== 'en') {
                $tags = $this->tagRep->getTagTranslation($lang);
                $tagIdList = \Arr::pluck($tags, "id");
                $tagNameList = \Arr::pluck($tags, "name");
                $tagNames = array_combine($tagIdList, $tagNameList);
            } else {
                $tagNames = $allTag->pluck('name','id');
            }
            foreach ($tagSort as $key=>$tag) {
                if (!empty($tagNames[$tag->tag_id])) {
                    $tagSort[$key]['name'] = $tagNames[$tag->tag_id];
                }else{
                    $tagSort[$key]['name'] = 'Tag_'.$tag->tag_id;
                }
            }
            $tagSort = $tagSort->toArray();
        }
        return $tagSort;
    }

    public function getReviewList(User $user,$startTime,$endTime,$star,$courier,$sort,$pageSize){
        $this->reviewRep->resetModel();
        $query = $this->reviewRep->with('tags:name,tag_type,id')->where(['user_id'=>$user->id,])->whereBetween('created_at',[$startTime,$endTime]);
        if (!empty($courier)) {
            $query->whereIn('courier',$courier);
        }
        if (!empty($star)) {
            $query->whereIn('star',$star);
        }
        switch ($sort) {
            case "ratingLow":
                $query->orderBy('star','asc');
                break;
            case "oldestCreate":
                $query->orderBy('created_at','asc');
                break;
            case "ratingHigh":
                $query->orderBy('star','desc');
                break;
            case "newestCreate":
            default:
                $query->orderBy('created_at','desc');
                break;
        }
        $result =  $query->simplePaginate($pageSize,['content','courier','courier_name','created_at','star','track_number','id']);

        $lang = $this->trackPageThemeRep->getUserTagLang($user->id);
        $tags = [];
        if ($lang !== 'en') {
            $tags = $this->tagRep->getTagTranslation($lang);
            $tagIdList = \Arr::pluck($tags, "id");
            $tags = array_combine($tagIdList, $tags);
        }
        return $result->map(function ($item) use($tags) {
            $item->createdAt = $item->created_at->format('M d,Y');
            if (!empty($tags)) {
                foreach ($item->tags as $tag) {
                    $tag->name = $tags[$tag->id]["name"];
                }
            }
            return $item;
        });
    }

    public function getCourierSelect(User $user, Carbon $startTime, Carbon $endTime)
    {
        $this->reviewRep->resetModel();
        return $this->reviewRep->where('user_id',$user->id)->whereBetween('created_at',[$startTime,$endTime])->groupBy('courier')->get(['courier as value','courier_name as label']);
    }

    public function getTransitData($user_id, $data_type, $start, $end): array {
        $data = $this->transitTimeDailyLogRep->getAnalyticsTransitData($user_id, $data_type, $start, $end);
        if (empty($data)) {
            return [];
        }
        if ((int)$data_type === 0) {
            // 获取运输商名称
            $courierCodeList = array_keys($data);
            $courierNameList = $this->courierRep->getCourierNameByCodes($courierCodeList);
            $courierNameList = array_combine(array_column($courierNameList, "express"), array_column($courierNameList, "name"));
        } else {
            // 获取目的国名称
            $countryCodeList = array_keys($data);
            $countryNameList = $this->shopifyCountryRep->getCountryNameByCodes($countryCodeList);
        }
        foreach ($data as $key => $item) {
            $data[$key]["maximum"] = (int)$item["maximum"];
            $data[$key]["minimum"] = (int)$item["minimum"];
            $data[$key]["average"] = (float)sprintf("%.2f", $item["average"]);
            $data[$key]["fastest"] = (float)sprintf("%.2f", $item["fastest"] / $item["total"] * 100);
            $data[$key]["ordinary"] = (float)sprintf("%.2f", $item["ordinary"] / $item["total"] * 100);
            $data[$key]["tardy"] = (float)sprintf("%.2f", $item["tardy"] / $item["total"] * 100);
            $data[$key]["poor"] = (float)sprintf("%.2f", $item["poor"] / $item["total"] * 100);
            if ((int)$data_type === 0 && !empty($courierNameList)) {
                $data[$key]["name"] = !empty($courierNameList[$data[$key]["name"]]) ? $courierNameList[$data[$key]["name"]] : $key;
            }
            if ((int)$data_type === 1 && !empty($countryNameList)) {
                $data[$key]["name"] = !empty($countryNameList[$data[$key]["name"]]) ? $countryNameList[$data[$key]["name"]] : $key;
            }
            // 所有桶内有几条数据
            $total = $item["total"];
            unset($data[$key]["total"]);
            $p85List = $item["p85"];
            if (empty($p85List)) {
                $data[$key]["p85"] = 0;
                continue;
            }
            $p85 = $this->transitTimeDailyLogRep->handleP85Data($p85List, $total);
            $data[$key]["p85"] = $p85;
        }
        $data = array_values($data);
        $sortColumn = array_column($data, 'p85');
        array_multisort($sortColumn, SORT_DESC, $data);
        return $data;
    }

    /** 获取趋势图表数据
     * @param User $user
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     * @throws MissingArgumentException
     */
    public function getTrendChartData(User $user,Carbon $startDate, Carbon $endDate): array
    {
        // 对比时间暂时固定为一个月
        $compareEndDate = $startDate->copy()->subDays(1);
        $compareStartDate = $compareEndDate->copy()->subDays(30);
        $redisKey = config('cache_key.dashboardTrendChart') . "{$user->id}_{$startDate->timestamp}_{$endDate->timestamp}";
        $dataCache = Redis::get($redisKey);
        $data = json_decode($dataCache, true);
        if (!empty($data)) {
            return $data;
        }

        $date_range = sprintf("%s-%s", $startDate->format('M d'), $endDate->format('M d Y'));
        $last_month_date_range = sprintf("%s-%s", $compareStartDate->format('M d'), $compareEndDate->format('M d Y'));

        // 获取订单状态图表的信息
        $orderReport = $this->dataOrderReportRep->getOrderReportByDate($user->id, $compareStartDate, $endDate);
        $orderReport = $orderReport->keyBy('create_date')->toArray();
        $defaultChartData = ['data'=>[], 'lastMonthData' => [], 'total' => 0,'lastMonthTotal' => 0];
        $deliveredData = $defaultChartData;
        $failAttemptData = $defaultChartData;
        $exceptionData = $defaultChartData;
        $period = $compareStartDate->daysUntil($endDate);
        // 处理成图表所需要的数据格式
        foreach ($period as $day) {
            $title = $day->format('M d, Y');
            $date = $day->format('Y-m-d');
            $delivered_num = $orderReport[$date]['delivered_num'] ?? 0;
            $fail_attempt_num = $orderReport[$date]['fail_attempt_num'] ?? 0;
            $exception_num = $orderReport[$date]['exception_num'] ?? 0;
            if ($day->lte($compareEndDate)) {
                $deliveredData['lastMonthTotal'] += $delivered_num;
                $failAttemptData['lastMonthTotal'] += $fail_attempt_num;
                $exceptionData['lastMonthTotal'] += $exception_num;
                $deliveredData['lastMonthData'][] = [
                    'title' => $title,
                    'value' => $delivered_num
                ];
                $failAttemptData['lastMonthData'][] = [
                    'title' => $title,
                    'value' => $fail_attempt_num
                ];
                $exceptionData['lastMonthData'][] = [
                    'title' => $title,
                    'value' => $exception_num
                ];
            } else {
                $deliveredData['total'] += $delivered_num;
                $failAttemptData['total'] += $fail_attempt_num;
                $exceptionData['total'] += $exception_num;
                $deliveredData['data'][] = [
                    'title' => $title,
                    'value' => $delivered_num
                ];
                $failAttemptData['data'][] = [
                    'title' => $title,
                    'value' => $fail_attempt_num
                ];
                $exceptionData['data'][] = [
                    'title' => $title,
                    'value' => $exception_num
                ];
            }
        }

        // 获取签收订单商品信息
        $deliveredProducts = $this->dailyDeliveredProductRep->getDeliveredProductsByDate($user->id, $compareStartDate, $endDate);
        $deliveredProductsItem = $deliveredProducts->whereBetween('create_date', [$startDate, $endDate]);
        $lastMonthDeliveredProductsItem = $deliveredProducts->whereBetween('create_date', [$compareStartDate, $compareEndDate]);
        // 按产品分组
        $groupByProduct = $deliveredProductsItem->groupBy('product_id');
        $productQuantity = [];
        $lastMonthProductQuantity = [];
        // 统计产品的数量
        foreach ($groupByProduct as $product_id => $productInfo) {
            $productQuantity[$product_id] = $productInfo->sum('quantity');
            $lastMonthProductQuantity[$product_id] = $lastMonthDeliveredProductsItem->where('product_id', $product_id)->sum('quantity');
        }
        // 显示数据量前十的产品
        arsort($productQuantity);
        $productQuantity = count($productQuantity) > 10 ? array_slice($productQuantity, 0, 10, true) : $productQuantity;
        $top_product_id = "";
        if (!empty($productQuantity)) {
            $top_product_quantity = max($productQuantity);
            $top_product_id = array_keys($productQuantity, $top_product_quantity)[0];
        }
        $top_product_name = "";
        $productIds = array_keys($productQuantity);
        $productData = [];
        $lastMonthProductData = [];
        if (!empty($productIds)) {
            $session = $user->auth_session;
            $productsCache = Redis::get(config('cache_key.dashboardProductName') . $user->id);
            if (!empty($productsCache)) {
                $shopifyProducts = json_decode($productsCache, true);
            } else {
                $graphqlProductDomain = new GraphqlProductDomain($session);
                $shopifyProducts = $graphqlProductDomain->queryProductByIds($productIds,['title',"id"]);
                if (empty($shopifyProducts['error'])) {
                    Redis::set(config('cache_key.dashboardProductName') . $user->id, json_encode($shopifyProducts), 'ex', 4500);
                }
            }
            if (empty($shopifyProducts['error'])) {
                foreach ($shopifyProducts as $shopifyProduct) {
                    $product_id = ShopifyUtils::extractIdFromGid($shopifyProduct["id"] ?? "");
                    $product_title = $shopifyProduct["title"] ?? "";
                    if ($product_id === (string)$top_product_id) {
                        $top_product_name = $shopifyProduct["title"] ?? "";
                    }
                    if (empty($product_id) || empty($product_title)) {
                        continue;
                    }
                    $productData[] = [
                        'title' => $product_title,
                        'value' => $productQuantity[$product_id] ?? 0
                    ];
                    $lastMonthProductData[] = [
                        'title' => $product_title,
                        'value' => $lastMonthProductQuantity[$product_id] ?? 0
                    ];
                }
            }
        }
        $deliveredProductBarChart = [
            'top_product' => $top_product_name,
            'data' => $productData,
            'lastMonthData' => $lastMonthProductData
        ];
        $data = [
            'date_range' => $date_range,
            'last_month_date_range' => $last_month_date_range,
            'delivered_chart' => $deliveredData,
            'fail_attempt_chart' => $failAttemptData,
            'exception_chart' => $exceptionData,
            'delivered_product_chart' => $deliveredProductBarChart,
        ];
        Redis::set($redisKey, json_encode($data), "ex", 4500);
        return $data;
    }

    /** 获取异常状态看板数据
     * @param User $user
     * @return array
     */
    public function getSpecialStatusData(User $user):array {
        $dataCache = Redis::get(config('cache_key.dashboardSpecialStatus') . $user->id);
        $data = json_decode($dataCache, true);
        if (!empty($data)) {
            return $data;
        }
        $setting = $this->userSettingRep->getValueByUserKey($user->id, UserSetting::$special_status_settings);
        if (!is_null($setting)) {
            $setting = json_decode($setting, true);
        } else {
            $setting = ["information_delayed"=>3,"pick_up"=>15,"transport_too_long"=>3];
        }
        $data = $this->orderTrack->getSpecialStatus($user->id, $setting);
        Redis::set(config('cache_key.dashboardSpecialStatus') . $user->id, json_encode($data), "ex", 4500);
        return $data;
    }

    /** 获取 shipments_lookups 图表数据
     * @param User $user
     * @return array[]
     */
    public function getShipmentsLookupsData(User $user): array
    {
        $endDate =  Carbon::createFromTime()->subDay();
        $startDate =  $endDate->copy()->addDays(-30);

        $redisKey = config('cache_key.dashboardShipmentsLookups') . "{$user->id}_{$startDate->timestamp}_{$endDate->timestamp}";
        $dataCache = Redis::get($redisKey);
        $data = json_decode($dataCache, true);
        if (!empty($data)) {
            return $data;
        }
        $shipmentItems = $this->dataOrderReportRep->getShipmenByDate($user->id, $startDate, $endDate);
        $clickItems = $this->trackPageClickRep->getClickNumByDate($user->id, $startDate->timestamp, $endDate->timestamp);
        $clickData = [];
        $shipmentData = [];
        $period = $startDate->daysUntil($endDate);
        foreach ($period as $day) {
            $title = $day->format('Y-m-d');
            $clickData[] = [
                'title' => $title,
                'value' => $clickItems[$day->timestamp] ?? 0
            ];
            $shipmentData[] = [
                'title' => $title,
                'value' => $shipmentItems[$title] ?? 0
            ];
        }
        $data = [
            'clickData' => $clickData,
            'shipmentData' => $shipmentData,
            'total_shipments'=>array_sum(array_column($shipmentData,'value')),
            'total_lookups'=>array_sum(array_column($clickData,'value'))
        ];
        Redis::set($redisKey, json_encode($data), "ex", 4500);
        return $data;
    }
}
