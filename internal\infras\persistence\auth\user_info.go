package auth

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
	"gorm.io/gorm"

	entity "tmshopify/internal/domain/entity/auth"
	authrepo "tmshopify/internal/domain/repo/auth"
)

var _ authrepo.UserInfoRepository = (*UserInfoRepoImpl)(nil)

// UserInfoRepoImpl 用户信息仓库实现
type UserInfoRepoImpl struct {
	db *gorm.DB
}

// NewUserInfoRepo 创建用户信息仓库
func NewUserInfoRepo(db *gorm.DB) authrepo.UserInfoRepository {
	return &UserInfoRepoImpl{db: db}
}

// GetUserByStoreName 根据店铺名称和用户ID获取用户信息，包括token和session
func (r *UserInfoRepoImpl) GetUserByStoreName(ctx context.Context, storeName string) (*entity.UserAuthInfoEntity, error) {
	// 根据 storeName 和 id 查询用户
	var user entity.Users
	err := r.db.WithContext(ctx).Scopes(r.activeUser()).Where("store_name = ? ", storeName).
		Order("created_at desc").First(&user).Error
	if err != nil {
		return nil, err
	}

	session, err := r.GetSessionByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	userAuth := &entity.UserAuthInfoEntity{
		UserInfo: entity.UserInfo{
			UserID: user.ID,
			Token:  user.Token,
		},
		Session: *session,
	}

	return userAuth, nil
}

// GetUserByID 根据用户ID获取用户信息
func (r *UserInfoRepoImpl) GetUserByID(ctx context.Context, id int64) (*entity.UserAuthInfoEntity, error) {
	var user entity.Users
	err := r.db.WithContext(ctx).Where("id = ? AND redact = 0 AND is_delete=0 ", id).First(&user).Error
	if err != nil {
		return nil, err
	}

	session, err := r.GetSessionByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	userAuth := &entity.UserAuthInfoEntity{
		UserInfo: entity.UserInfo{
			UserID: user.ID,
			Token:  user.Token,
		},
		Session: *session,
	}

	return userAuth, nil
}

// GetSessionByID 根据ID获取会话信息
func (r *UserInfoRepoImpl) GetSessionByID(ctx context.Context, id int64) (*entity.Session, error) {
	var session entity.Session
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&session).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return &session, nil
}

// StoreSession 存储会话信息
func (r *UserInfoRepoImpl) StoreSession(ctx context.Context, session *entity.Session) error {
	return r.db.WithContext(ctx).Create(session).Error
}

// ReGrantUser 重新授权用户
func (r *UserInfoRepoImpl) ReGrantUser(ctx context.Context, clientID string, clientSecret string, shop string, token string) (*entity.UserAuthInfoEntity, error) {
	// 1. 重新授权token - 获取offline access token
	accessToken, err := getOfflineSession(shop, clientID, clientSecret, token)
	if err != nil {
		return nil, fmt.Errorf("failed to grant token: %w", err)
	}

	// 2. 更新或创建用户
	updatedUser, err := r.checkHasUser(ctx, shop, accessToken)
	if err != nil {
		return nil, fmt.Errorf("failed to check/update user: %w", err)
	}

	// 3. 获取用户ID
	var uid int64
	if updatedUser.UserInfo.UserID == 0 {
		// 如果用户ID为空，获取活跃用户的ID
		var activeUser entity.Users
		err = r.db.WithContext(ctx).Scopes(r.activeUser()).Where("store_name = ? ", shop).
			Order("created_at desc").First(&activeUser).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("failed to get active user: %w", err)
		}
		uid = activeUser.ID
		updatedUser.UserInfo.UserID = uid
	} else {
		uid = updatedUser.UserInfo.UserID
	}

	// 4. 创建session
	session := &entity.Session{
		AccessToken: accessToken.AccessToken,
		Scope:       accessToken.Scope,
		UID:         uid,
	}

	err = r.StoreSession(ctx, session)
	if err != nil {
		return nil, fmt.Errorf("failed to store session: %w", err)
	}

	updatedUser.Session = *session

	return updatedUser, nil
}
func (r *UserInfoRepoImpl) activeUser() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("is_delete = 0 AND redact = 0")
	}
}

// checkHasUser 检查并更新用户
func (r *UserInfoRepoImpl) checkHasUser(ctx context.Context, shop string, accessToken *OfflineTokenResponse) (*entity.UserAuthInfoEntity, error) {
	// 查询是否存在该用户
	var existingUser entity.Users
	err := r.db.WithContext(ctx).Scopes(r.activeUser()).Where("store_name = ? ", shop).
		Order("created_at desc").First(&existingUser).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户不存在，创建新用户
			newUser := &entity.Users{
				StoreName: shop,
				Token:     accessToken.AccessToken,
				Redact:    0,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			}
			err = r.db.WithContext(ctx).Create(newUser).Error
			if err != nil {
				return nil, fmt.Errorf("failed to create user: %w", err)
			}

			return &entity.UserAuthInfoEntity{
				UserInfo: entity.UserInfo{
					UserID:    newUser.ID,
					Token:     newUser.Token,
					StoreName: newUser.StoreName,
				},
				Session: entity.Session{
					AccessToken: accessToken.AccessToken,
					Scope:       accessToken.Scope,
				},
			}, nil
		}
		return nil, fmt.Errorf("failed to query user: %w", err)
	}

	// 更新现有用户的token
	existingUser.Token = accessToken.AccessToken
	err = r.db.WithContext(ctx).Save(&existingUser).Error
	if err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return &entity.UserAuthInfoEntity{
		UserInfo: entity.UserInfo{
			UserID:    existingUser.ID,
			Token:     existingUser.Token,
			StoreName: existingUser.StoreName,
		},
		Session: entity.Session{
			AccessToken: accessToken.AccessToken,
			Scope:       accessToken.Scope,
		},
	}, nil
}

func getOfflineSession(shop, clientID, clientSecret, idToken string) (*OfflineTokenResponse, error) {
	url := fmt.Sprintf("https://%s.myshopify.com/admin/oauth/access_token", shop)

	payload := map[string]string{
		"client_id":            clientID,
		"client_secret":        clientSecret,
		"grant_type":           "urn:ietf:params:oauth:grant-type:token-exchange",
		"subject_token":        idToken,
		"subject_token_type":   "urn:ietf:params:oauth:token-type:id_token",
		"requested_token_type": "urn:shopify:params:oauth:token-type:offline-access-token",
	}

	client := resty.New()
	var tokenResp OfflineTokenResponse

	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Accept", "application/json").
		SetBody(payload).
		SetResult(&tokenResp).
		Post(url)

	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode(), string(resp.Body()))
	}

	return &tokenResp, nil
}

// OfflineTokenResponse 表示 Shopify offline access token 响应
type OfflineTokenResponse struct {
	AccessToken string `json:"access_token"`
	Scope       string `json:"scope"`
}
