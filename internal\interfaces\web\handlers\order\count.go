package handlers

import (
	"errors"

	"github.com/gin-gonic/gin"
	"github.com/gomodule/redigo/redis"

	"tmshopify/internal/application/order"
	"tmshopify/internal/interfaces/web/handlers"
	"tmshopify/server/response"
)

// OrderCountResponse 订单数量响应
type OrderCountResponse struct {
	OrderCount     int64 `json:"order_count"`
	OrderProcessed int64 `json:"order_processed"`
}

// OrderCountHandler OrderCountHandler 实例
type OrderCountHandler struct {
	handlers.BaseHandler
	orderService *order.Service
}

// NewOrderCountHandler 创建一个 OrderCountHandler 实例
func NewOrderCountHandler(orderService *order.Service) *OrderCountHandler {
	return &OrderCountHandler{
		orderService: orderService,
	}
}

// GetOrderCount 获取订单数量
func (h *OrderCountHandler) GetOrderCount(ctx *gin.Context) {
	orderCount, orderProcessed, err := h.orderService.GetCount(ctx, ctx.GetInt64("user_id"))
	if err != nil && !errors.Is(err, redis.ErrNil) {
		response.InternalServerError(ctx, err.Error())
		return
	}
	response.Success(ctx, &response.SuccessResponse{
		Message: "success",
		Data: OrderCountResponse{
			OrderCount:     orderCount,
			OrderProcessed: orderProcessed,
		},
	})
}
