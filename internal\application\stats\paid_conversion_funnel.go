package stats

import (
	"context"
	"time"

	"tmshopify/internal/domain/repo/stats"

	"github.com/channelwill/athena/logger"
)

// FunnelService 付费转化漏斗服务
type FunnelService struct {
	funnelRepo stats.FunnelRepository
}

// NewFunnelService 创建付费转化漏斗服务
func NewFunnelService(funnelRepo stats.FunnelRepository) *FunnelService {
	return &FunnelService{
		funnelRepo: funnelRepo,
	}
}

// FunnelStatistics 漏斗统计数据结构
type FunnelStatistics struct {
	NewInstallUsers       int64 `json:"new_install_users"`        // 新安装用户数
	FreeTrialUsers        int64 `json:"free_trial_users"`         // 免费试用用户数
	PaidConversionUsers   int64 `json:"paid_conversion_users"`    // 付费转化用户数
	NextMonthRenewalUsers int64 `json:"next_month_renewal_users"` // 次月续费用户数
}

// GetFunnelStatistics 获取付费转化漏斗统计数据
func (s *FunnelService) GetFunnelStatistics(ctx context.Context, startDate, endDate time.Time) (*FunnelStatistics, error) {
	// 1. 统计时段内新安装注册的用户数
	newInstallUsers, err := s.funnelRepo.CountNewInstallUsersInTimeRange(ctx, startDate, endDate)
	if err != nil {
		logger.Error(ctx, "获取新安装用户数失败", map[string]interface{}{"error": err.Error()})
		return nil, err
	}

	// 2. 统计时段内开启免费试用注册的用户数
	freeTrialUsers, err := s.funnelRepo.CountTrialUsersInTimeRange(ctx, startDate, endDate)
	if err != nil {
		logger.Error(ctx, "获取免费试用用户数失败", map[string]interface{}{"error": err.Error()})
		return nil, err
	}

	// 3. 统计时段内付费转化注册的用户数
	paidConversionUsers, err := s.funnelRepo.CountPaidConversionUsersInTimeRange(ctx, startDate, endDate)
	if err != nil {
		logger.Error(ctx, "获取付费转化用户数失败", map[string]interface{}{"error": err.Error()})
		return nil, err
	}

	// 4. 统计时段内次月续费注册的用户数
	nextMonthRenewalUsers, err := s.funnelRepo.CountRenewalUsersInTimeRange(ctx, startDate, endDate)
	if err != nil {
		logger.Error(ctx, "获取次月续费用户数失败", map[string]interface{}{"error": err.Error()})
		return nil, err
	}

	result := &FunnelStatistics{
		NewInstallUsers:       newInstallUsers,
		FreeTrialUsers:        freeTrialUsers,
		PaidConversionUsers:   paidConversionUsers,
		NextMonthRenewalUsers: nextMonthRenewalUsers,
	}

	return result, nil
}
