package utils

import (
	"fmt"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/dromara/carbon/v2"

	"tmshopify/config"
	"tmshopify/internal/app/definition"
	"tmshopify/pkg/domain"
)

type StatusConfig struct {
	EnStatus   string                  `json:"en_status"`
	CnStatus   string                  `json:"cn_status"`
	Status     config.StatusNodeNumber `json:"status"`
	StatusName string                  `json:"status_name"`
	Img        string                  `json:"img"`
}

func GetStatusKey(customStatus []config.StatusNodeNumber) []config.StatusNodeNumber {
	statusSet := config.OrderedNodeStatusNumber
	var returnData []config.StatusNodeNumber
	returnData = append(returnData, statusSet)
	if len(customStatus) != 0 {
		for _, value := range customStatus {
			returnData = append(returnData, value)
		}
	}
	returnData = append(returnData, config.OrderReadyStatusNumber)
	return returnData
}

var retailRegex = regexp.MustCompile(`from the(.*)as of now`)
var pickupAtRegex = regexp.MustCompile(`pick-up at the ([a-zA-Z \d]+).`)
var arrivedRegex = regexp.MustCompile(`arrived\s(\w+)`)

// GetMapLocation 获取地图api的url
func GetMapLocation(TrackInfo []definition.ReturnInfo, country string) definition.MapLocation {
	var mapLocation definition.MapLocation
	if len(TrackInfo) > 0 && country == "" {
		errorArr := []string{"CN"}
		var detail string
		//  LOS ANGELES,US【美国洛杉矶】
		for _, v := range TrackInfo {
			detail = strings.TrimSpace(v.LocationMap)
			if detail == "" && v.LocationMap != "" {
				detail = strings.TrimSpace(v.Location)
			}
			if !strings.Contains(detail, "LOS ANGELES") {
				detail = strings.ReplaceAll(detail, " ", ",")
			}
			if detail == "" && v.TrackingDetail != "" {
				detail = strings.TrimSpace(v.TrackingDetail)
			}
			// 处理 "from the...as of now"
			if matches := getMatches(retailRegex, detail); len(matches) > 0 {
				detail = strings.TrimSpace(strings.ReplaceAll(matches[0], "retail outlet", ""))
			}

			// 处理 "pick-up at the ..."
			if matches := getMatches(pickupAtRegex, detail); len(matches) > 0 {
				detail = strings.TrimSpace(matches[0])
			}

			// 移除 "SAN " 前缀
			if strings.HasPrefix(detail, "SAN ") {
				detail = strings.TrimPrefix(detail, "SAN ")
			}

			// 如果 detail 为空，则尝试从 "arrived ..." 中提取信息
			if detail == "" {
				//getMatches(`(?<=arrived\s)\w+`, detail)
				match := arrivedRegex.FindString(detail)
				if match != "" {
					// 使用 SplitN 提取 "arrived " 后面的单词
					parts := strings.SplitN(match, " ", 2)
					if len(parts) > 1 && strings.ToLower(parts[1]) != "in" {
						detail = parts[1]
					}
				}
			}

			// 英国地址处理
			if detail == "United,Kingdom" {
				detail = "United Kingdom"
			}

			checkpointStatus := strings.TrimSpace(v.CheckpointDeliveryStatus)
			statusDescription := strings.TrimSpace(v.TrackingDetail)

			for _, errorStr := range errorArr {
				if strings.Contains(detail, errorStr) {
					detail = ""
					break
				}
			}

			detail = strings.ReplaceAll(detail, ",", " ")

			if detail != "" {
				mapLocation.Info = definition.LocationInfo{
					Status:      checkpointStatus,
					Description: statusDescription,
					Location:    detail,
				}
				break
			}
		}
		count := len(TrackInfo)
		if detail == "" && count < 2 && count > 0 {
			originalCountry := strings.TrimSpace(TrackInfo[count-1].Location)
			statusDescription := strings.TrimSpace(TrackInfo[count-1].TrackingDetail)
			checkpointStatus := strings.TrimSpace(TrackInfo[count-1].CheckpointDeliveryStatus)

			originalCountry = strings.ReplaceAll(originalCountry, " ", "-")
			originalCountry = strings.ReplaceAll(originalCountry, ",", "-")

			if originalCountry != "" {
				for _, errorStr := range errorArr {
					if strings.Contains(originalCountry, errorStr) {
						originalCountry = ""
						break
					}
				}

				originalCountry = strings.ReplaceAll(originalCountry, "-", " ")

				mapLocation.Info = definition.LocationInfo{
					Status:      checkpointStatus,
					Description: statusDescription,
					Location:    originalCountry,
				}
				return mapLocation
			}
		}
	}
	if country != "" {
		destination := country
		mapLocation.Destination = destination
	}
	return mapLocation
}

func getMatches(re *regexp.Regexp, input string) []string {
	matches := re.FindStringSubmatch(input)
	if len(matches) > 1 {
		return matches[1:]
	}
	return []string{} // 返回一个空切片而不是 nil
}

func HandleTimeFormatBySet(timestamp int64, date int, timeValue int, isHiddenYear, isHiddenTime bool) string {
	formatStr := HandleTimeFormatSet(date, timeValue, isHiddenYear, isHiddenTime)

	return time.Unix(timestamp, 0).Format(formatStr)
}

func GetStatusSummary(translation map[string]string, status config.StatusNodeNumber, key string) string {
	if key == "" {
		key = "name"
	}
	arr := []map[string]interface{}{
		{"key": "ordered", "name": "Ordered", "status": config.OrderedNodeStatusNumber},
		{"key": "order_ready", "name": "Order Ready", "status": config.OrderReadyStatusNumber},
		{"key": "shipped", "name": "Shipped", "status": config.ShippedStatusNumber},
		{"key": "transit", "name": "In Transit", "status": config.TransitStatusNumber},
		{"key": "pickup", "name": "Out for Delivery", "status": config.PickupStatusNumber},
		{"key": "delivered", "name": "Delivered", "status": config.DeliveredStatusNumber},
		{"key": "pending", "name": "Pending", "status": config.DeliveredStatusNumber},
		{"key": "expired", "name": "Expired", "status": config.ExpiredStatusNumber},
		{"key": "undelivered", "name": "Failed Attempt", "status": config.FailAttemptStatusNumber},
		{"key": "exception", "name": "Exception", "status": config.ExceptionStatusNumber},
		{"key": "info_receive", "name": "Info Received", "status": config.InfoReceiveStatusNumber},
	}

	for _, item := range arr {
		if status == item["status"] {
			if translation == nil || translation[item["key"].(string)] == "" {
				return item["name"].(string)
			}
			return translation[item["key"].(string)]
		}
	}

	for _, item := range arr {
		if status == item["status"] {
			return item[key].(string)
		}
	}

	return ""
}

func GetCheckpointStatus(status config.StatusNodeNumber) string {
	arr := map[config.StatusNodeNumber]string{
		config.TransitStatusNumber:     "transit",
		config.PickupStatusNumber:      "pickup",
		config.DeliveredStatusNumber:   "delivered",
		config.OrderedNodeStatusNumber: "blank",
		config.OrderReadyStatusNumber:  "blank",
	}
	if _, exist := arr[status]; !exist {
		return "blank"
	}
	return arr[status]
}

func DelSpecialWord(trackingDetail string) string {
	if trackingDetail == "" {
		return trackingDetail
	}

	// 去掉开头的特殊符号
	trackingDetail = strings.TrimLeft(trackingDetail, ",，")

	// 去掉结尾的句号
	trackingDetail = strings.TrimRight(trackingDetail, "\\.,，")

	return trackingDetail
}

func ChangeInfoStatus(statusNumber config.StatusNodeNumber, info []definition.ReturnInfo) []definition.ReturnInfo {
	if len(info) == 0 {
		return info
	}
	changeAll := 0
	// 针对订单整体状态不是 delivered 的情况 将 info 信息里的 delivered pickup 都改成 transit
	if statusNumber == config.TransitStatusNumber {
		changeAll = 1
	}

	// 首条运输信息状态
	var firstStatus string
	var firstDelivered, firstPickup int
	for key, value := range info {
		checkpointStatus := strings.TrimSpace(value.CheckpointDeliveryStatus)
		if changeAll != 0 {
			// 针对订单整体状态不是 delivered 或 pickup 的情况 将 info 信息里的 delivered pickup 都改成 transit
			if checkpointStatus == "delivered" {
				info[key].CheckpointDeliveryStatus = "transit"
				info[key].CheckpointDeliverySubstatus = "transit001"
			}
		} else {
			// 第一条状态
			if key == 0 {
				firstStatus = checkpointStatus
			}
			// 已有 delivered , 后面的 delivered 状态改为 transit
			if firstDelivered != 0 && checkpointStatus == "delivered" {
				info[key].CheckpointDeliveryStatus = "transit"
				info[key].CheckpointDeliverySubstatus = "transit001"
			}
			if checkpointStatus == "delivered" {
				firstDelivered = 1
			}
			// 如果第一条不是签收, 后面的 delivered 签收都改为 transit
			if firstStatus != "" && firstStatus != "delivered" && checkpointStatus == "delivered" {
				info[key].CheckpointDeliveryStatus = "transit"
				info[key].CheckpointDeliverySubstatus = "transit001"
			}
			// 已有 pickup , 后面的 pickup 状态改为 transit
			if firstPickup != 0 && checkpointStatus == "pickup" {
				info[key].CheckpointDeliveryStatus = "transit"
				info[key].CheckpointDeliverySubstatus = "transit001"
			}
			if checkpointStatus == "pickup" {
				firstPickup = 1
			}
		}
	}
	return info
}

func GetStatusConfig(userId int) map[string]StatusConfig {
	status := make(map[string]StatusConfig)
	status["blank"] = StatusConfig{
		Status: config.BlankStatusNumber,
		Img:    "//" + config.Get().ServerDomain + "/static/images/deliver_status/blank.svg",
	}
	for key, value := range domain.StatusList {
		keyword := domain.StatusKey[key]
		var img string
		if userId == 46679 && (key == config.DeliveredStatusNumber || key == config.FailAttemptStatusNumber || key == config.ExceptionStatusNumber ||
			key == config.PickupStatusNumber || key == config.TransitStatusNumber) {
			img = "//" + config.Get().ServerDomain + "/shopify/status/" + keyword + "_grayscale.png"
		} else {
			img = "//" + config.Get().ServerDomain + "/static/images/deliver_status/" + keyword + ".svg"
		}
		status[keyword] = StatusConfig{
			Status:     key,
			StatusName: value,
			Img:        img,
		}
	}
	return status
}

func CmpTrackInfoDate(info []definition.ReturnInfo) {
	sort.Slice(info, func(i, j int) bool {
		// 将日期字符串转换为时间对象
		timeI := carbon.Parse(info[i].CheckpointDate)
		timeJ := carbon.Parse(info[j].CheckpointDate)
		// 比较时间
		return timeI.Gt(timeJ)
	})
}

func ParseTimeManual(input string) (time.Time, error) {
	firstParse := carbon.Parse(input)
	if firstParse.Error == nil {
		return firstParse.StdTime(), firstParse.Error
	}
	// 处理特殊格式：2025-03-18T09:21:45GMT+1
	// 检查是否包含 GMT 标识
	if strings.Contains(input, "GMT") {
		parts := strings.SplitN(input, "GMT", 2)
		datePart := parts[0]
		tzPart := parts[1] // 如 +1, +01, +0100

		// 统一处理时区格式
		tzPart = strings.TrimSpace(tzPart)
		if tzPart == "" {
			tzPart = "+0000" // GMT 默认为 +0000
		} else {
			// 标准化时区格式
			// +1    -> +0100
			// +01   -> +0100
			// +1:00 -> +0100
			tzPart = strings.ReplaceAll(tzPart, ":", "")
			if len(tzPart) == 2 { // +1 -> +01
				tzPart = tzPart[:1] + "0" + tzPart[1:]
			}
			if len(tzPart) == 3 { // +01 -> +0100
				tzPart = tzPart + "00"
			}
		}

		// 构建标准格式字符串
		formattedDateTime := datePart + tzPart

		// 尝试使用不同的布局格式进行解析
		layouts := []string{
			"2006-01-02T15:04:05-0700",
			"2006-01-02T15:04:05Z-0700",
			"2006-01-02T15:04:05.000-0700",
		}

		var parseErr error
		for _, layout := range layouts {
			t, err := time.Parse(layout, formattedDateTime)
			if err == nil {
				return t, nil
			}
			parseErr = err
		}

		// 如果其他布局都失败，返回最后一个错误
		return time.Time{}, fmt.Errorf("could not parse time: %s (%v)", input, parseErr)
	}

	// 尝试通用时间格式解析
	layouts := []string{
		time.RFC3339,
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02 15:04:05Z07:00",
		"2006-01-02T15:04:05Z07:00",
		"01/02/2006 15:04:05",
		"2006/01/02 15:04:05",
	}

	var parseErr error
	for _, layout := range layouts {
		t, err := time.Parse(layout, input)
		if err == nil {
			return t, nil
		}
		parseErr = err
	}

	return time.Time{}, fmt.Errorf("could not parse time: %s (%v)", input, parseErr)

}

func HandleTimeFormatSet(date int, timeValue int, isHiddenYear, isHiddenTime bool) string {
	var format []string

	// 默认值
	if date == 0 && timeValue == 0 {
		date = 1
		if isHiddenYear || isHiddenTime {
			timeValue = 0
		} else {
			timeValue = 1
		}
	} else {
		date++
		timeValue++
	}

	switch date {
	case 1:
		if isHiddenYear {
			format = append(format, "Jan 02")
		} else {
			format = append(format, "Jan 02, 2006")
		}
	case 2:
		format = append(format, "Jan 02")
	case 3:
		if isHiddenYear {
			format = append(format, "Jan 02st")
		} else {
			format = append(format, "Jan 02st 2006")
		}
	case 4:
		if isHiddenYear {
			format = append(format, "1/2")
		} else {
			format = append(format, "1/2/2006")
		}
	case 5:
		if isHiddenYear {
			format = append(format, "2/1")
		} else {
			format = append(format, "2/1/2006")
		}
	case 6:
		if isHiddenYear {
			format = append(format, "02.01")
		} else {
			format = append(format, "02.01.2006")
		}
	case 7:
		if isHiddenYear {
			format = append(format, "01-02")
		} else {
			format = append(format, "2006-01-02")
		}
	}
	switch timeValue {
	case 1:
		format = append(format, "03:04 PM")
	case 2:
		format = append(format, "15:04")
	case 3:
		// Do nothing for case 3 (empty time format)
	default:
		// Do nothing for default case
	}

	formatStr := ""
	if len(format) > 1 {
		formatStr = fmt.Sprintf("%s %s", format[0], format[1])
	} else if len(format) == 1 {
		formatStr = format[0]
	}
	return formatStr
}
