package graphql

import "time"

const QueryOrderFulfillmentItems = "query GetOrderById($orderId: ID!) { order(id: $orderId) { fulfillments(first: 50) {  id name fulfillmentLineItems(first: 50) { nodes { id quantity lineItem { name id quantity sku title variantTitle image{ url } originalTotalSet { presentmentMoney { amount currencyCode } shopMoney { amount currencyCode } } discountedTotalSet { presentmentMoney { amount currencyCode } shopMoney { amount currencyCode } } product { id handle featuredMedia { preview { image { url } } } } variant { id } } } } } } }"

type OrderFulfillments struct {
	Fulfillments []Fulfillment `json:"fulfillments"`
}

type Address struct {
	Address1                string   `json:"address1,omitempty"`
	City                    string   `json:"city,omitempty"`
	Address2                string   `json:"address2,omitempty"`
	Company                 string   `json:"company,omitempty"`
	Country                 string   `json:"country,omitempty"`
	CoordinatesValidated    bool     `json:"coordinatesValidated,omitempty"`
	CountryCode             string   `json:"countryCode,omitempty"`
	CountryCodeV2           string   `json:"countryCodeV2,omitempty"`
	FirstName               string   `json:"firstName,omitempty"`
	Formatted               []string `json:"formatted,omitempty"`
	FormattedArea           string   `json:"formattedArea,omitempty"`
	Id                      string   `json:"id,omitempty"`
	LastName                string   `json:"lastName,omitempty"`
	Longitude               float64  `json:"longitude,omitempty"`
	Latitude                float64  `json:"latitude,omitempty"`
	Name                    string   `json:"name,omitempty"`
	Province                string   `json:"province,omitempty"`
	Phone                   string   `json:"phone,omitempty"`
	ProvinceCode            string   `json:"provinceCode,omitempty"`
	TimeZone                string   `json:"timeZone,omitempty"`
	ValidationResultSummary string   `json:"validationResultSummary,omitempty"`
	Zip                     string   `json:"zip,omitempty"`
}

type Fulfillment struct {
	DisplayStatus        string      `json:"displayStatus"`
	Status               string      `json:"status"`
	ID                   string      `json:"id"`
	InTransitAt          time.Time   `json:"inTransitAt"`
	Name                 string      `json:"name"`
	RequiresShipping     bool        `json:"requiresShipping"`
	EstimatedDeliveryAt  interface{} `json:"estimatedDeliveryAt"`
	CreatedAt            time.Time   `json:"createdAt"`
	DeliveredAt          time.Time   `json:"deliveredAt"`
	LegacyResourceId     string      `json:"legacyResourceId"`
	TotalQuantity        int         `json:"totalQuantity"`
	UpdatedAt            time.Time   `json:"updatedAt"`
	FulfillmentLineItems struct {
		Nodes []FulfillmentLineItem `json:"nodes"`
	} `json:"fulfillmentLineItems"`
}
