package order

import (
	"tmshopify/store/database"
	orderModel "tmshopify/store/models/order"
	"tmshopify/store/repo/interface/order"
)

var _ order.EstimateRepo = (*EstimateRepoImpl)(nil)

type EstimateRepoImpl struct{}

func (e EstimateRepoImpl) GetEstimateByTrackingIndex(userId int, fulfillmentId string, trackingNumber string) (orderModel.Estimate, error) {
	var estimate orderModel.Estimate
	err := database.DB.Model(estimate).Where("user_id = ? AND fulfillment_id = ? AND track_number = ? ", userId, fulfillmentId, trackingNumber).First(&estimate).Error
	return estimate, err
}
