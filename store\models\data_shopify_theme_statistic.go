package models

import "time"

type ShopifyThemeStatistics struct {
	CreateDate   time.Time `gorm:"column:create_date;type:date;comment:当天日期;NOT NULL" json:"create_date"`
	Type         int       `gorm:"column:type;type:int(1);default:1;comment:类型(1:edd插件,2:trackpage extension插件);NOT NULL" json:"type"`
	TotalInstall int       `gorm:"column:total_install;type:int(11);default:0;comment:总安装用户数;NOT NULL" json:"total_install"`
	Model
}

func (ShopifyThemeStatistics) TableName() string {
	return "data_shopify_theme_statistics"
}
