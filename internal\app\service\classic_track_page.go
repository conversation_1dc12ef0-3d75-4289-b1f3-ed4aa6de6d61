package service

import (
	"encoding/json"
	"fmt"
	"html"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"tmshopify/config"
	"tmshopify/internal/app/definition"
	"tmshopify/internal/app/utils"
	"tmshopify/pkg/domain"
	"tmshopify/store/database"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type ClassicTrackPageService struct {
	TrackPageService  *TrackPageService
	OrderTrackRepo    *impl.OrderTrack
	CourierRepo       *impl.Courier
	ThemesSettingRepo *impl.ThemesSetting
	ThemeCssRepo      *impl.ThemeCss
}

func (c *ClassicTrackPageService) BackData() map[string]interface{} {
	trackRequestDomain := c.TrackPageService.trackGlobalData.TrackRequestDomain

	if c.isPreviewMode(trackRequestDomain) {
		c.previewTracking()
		return c.returnData()
	}

	if trackRequestDomain.TrackNumber != "" {
		c.numberTracking()
	} else if c.isEmailOrderTracking(trackRequestDomain) {
		c.orderEmailTracking("", false, false)
	} else if c.isPhoneOrderTracking(trackRequestDomain) {
		c.orderPhoneTracking()
	} else if c.isOrderNameTracking(trackRequestDomain) {
		c.orderTracking()
	}

	if c.TrackPageService.trackGlobalData.IsPlugin {
		return c.PluginData()
	}

	return c.returnData()
}

func (c *ClassicTrackPageService) isPreviewMode(trackRequestDomain domain.TrackRequestDomain) bool {
	return trackRequestDomain.TrackNumber == domain.PreviewNumber ||
		(trackRequestDomain.TrackNumber == "" && trackRequestDomain.Email == domain.PreviewEmail)
}

func (c *ClassicTrackPageService) isEmailOrderTracking(trackRequestDomain domain.TrackRequestDomain) bool {
	return trackRequestDomain.OrderName != "" && trackRequestDomain.Email != ""
}

func (c *ClassicTrackPageService) isPhoneOrderTracking(trackRequestDomain domain.TrackRequestDomain) bool {
	return trackRequestDomain.OrderName != "" && trackRequestDomain.Phone != ""
}

func (c *ClassicTrackPageService) isOrderNameTracking(trackRequestDomain domain.TrackRequestDomain) bool {
	return trackRequestDomain.OrderName != "" && c.TrackPageService.trackGlobalData.TrackThemeEntity.ClassicSetting.OrderLookup.OrderNumber == "3"
}

func (c *ClassicTrackPageService) previewTracking() {
	globalData := &c.TrackPageService.trackGlobalData
	originInfoStr := `{"destinationcountry":"US","destination_track_number":"USPSTRACKING0001","destination_track_courier":"usps","scheduled_delivery_date":"2021-07-21","trackinfo":[{"tracking_detail":"Out of delivery","checkpoint_date":"2021-07-20 18:55:48","location":"Durham, US","checkpoint_delivery_status":"pickup","checkpoint_delivery_substatus":"pickup001"},{"tracking_detail":"Parcel arrived at a carrier facility","checkpoint_date":"2021-07-20 13:06:18","location":"Durham, US","checkpoint_delivery_status":"blank","checkpoint_delivery_substatus":"transit001"},{"tracking_detail":"Package departed an Amazon facility","checkpoint_date":"2021-07-20 03:15:23","location":"Richmond, Virginia, US","checkpoint_delivery_status":"blank","checkpoint_delivery_substatus":"transit001"},{"tracking_detail":"Package arrived at an Amazon facility","checkpoint_date":"2021-07-19 20:46:01","location":"Richmond, Virginia, US","checkpoint_delivery_status":"blank","checkpoint_delivery_substatus":"transit001"},{"tracking_detail":"Shipment has left seller facility and is in transit to carrier","checkpoint_date":"2021-07-19 11:21:15","location":"","checkpoint_delivery_status":"transit","checkpoint_delivery_substatus":"transit001","ItemNode":"ItemReceived"}],"stausDataNum":3,"function":"swiship-usa_web_1_200","tracklang":"en","statusInfo":"Delivered to safe place,Rolesville, US,2021-07-21 03:25:18","lastUpdateTime":"2021-07-21 03:25:18","firstUpdateTime":"2021-07-19 11:21:15","stayTimeLength":7,"ItemReceived":"2021-07-19 11:21:15","checkpoint_delivery_substatus":"pickup001","substatusTime":1626830718,"lang":9,"fromSearchSpendTime":11602.228879928589,"destinationcountryname":"US","support":1,"itemTimeLength":2,"infoState":2,"phonenum":"+31 (0) 900 0990","companyName":"Amazon FBA Swiship","companyCode":"swiship-usa"}`

	var originInfo definition.TrackingInfoData
	if err := json.Unmarshal([]byte(originInfoStr), &originInfo); err != nil {
		logrus.Error("Error decoding origin info:", err)
		return
	}

	var destinationInfo definition.TrackingInfoData

	globalData.OrderRecord.CustomStatusTime = map[config.StatusNodeNumber]int{}
	globalData.OrderRecord.Product = "1234"
	c.TrackPageService.trackGlobalData.RootData.Product = "1234"
	globalData.OrderRecord.OrderCreateTime = 1626578475
	globalData.OrderRecord.OrderFulfillTime = 1626664875
	globalData.OrderRecord.RecipientCountryCode = "US"
	trackNumber := definition.TrackNumberData{
		Courier:     "swiship-usa",
		TrackStatus: 3,
		TrackInfos: definition.TrackInfos{
			OriginInfo:      originInfo,
			DestinationInfo: destinationInfo,
		},
	}
	trackNumber.OrderFulfillTime = 1626664875
	trackNumber.Destination = "US"
	singleTrackData := c.HandleTrackInfo(trackNumber)

	// 检查地图是否显示
	mapShow := c.TrackPageService.trackGlobalData.TrackThemeEntity.ClassicSetting.TrackingHistory.Map != "" &&
		c.TrackPageService.trackGlobalData.TrackThemeEntity.ClassicSetting.TrackingHistory.Map != "none"

	if mapShow {
		singleTrackData.ShippingMap = definition.HandleShippingAddressResult{
			Lat:      35.996653,
			Lon:      -78.9018053,
			Location: "Durham, US",
		}
	}

	imgInfo := []definition.OrderProductImgData{
		{
			TitleImg: "https://cdn.shopify.com/s/files/1/0261/4014/7794/products/<EMAIL>?v=1616749295",
			Title:    "x1 Wedding Bride Bouquet",
			Variant:  "",
			Quantity: 1,
		},
	}

	singleTrackData.Title = imgInfo
	singleTrackData.OrderCreateTime = utils.HandleTimeFormatBySet(int64(globalData.OrderRecord.OrderCreateTime), globalData.TrackThemeEntity.ModernSetting.TrackingInfo.DateFormat, 2, false, false)
	singleTrackData.TrackNumber = domain.PreviewNumber
	singleTrackData.OrderCreateTime = utils.HandleTimeFormatBySet(int64(globalData.OrderRecord.OrderCreateTime), globalData.TrackThemeEntity.ModernSetting.TrackingInfo.DateFormat, 2, false, false)
	singleTrackData.Notes = "Order note preview"
	singleTrackData.Countryname = "US"
	singleTrackData.Destinationcountry = "HK"
	eddService := NewEddService()
	calculateEntity := eddService.GetCalculateEntity(globalData.EddSetting, TrackCalculateData{
		UserId:           globalData.User.ID,
		FulfillmentId:    "0",
		TrackNumber:      trackNumber.TrackNumber,
		Courier:          trackNumber.Courier,
		Destination:      globalData.OrderRecord.RecipientCountryCode,
		OrderCreateTime:  globalData.OrderRecord.OrderCreateTime,
		OrderFulfillTime: globalData.OrderRecord.OrderFulfillTime,
	})
	dataShow := eddService.CalculateEddByOrderAndDestinationByEntities(globalData.User.ID,
		calculateEntity, globalData.EddSetting)
	if dataShow.StartShow != 0 && dataShow.EndShow != 0 {
		startShow := utils.HandleTimeFormatBySet(dataShow.StartShow, globalData.EddSetting.DateFormat, 2, false, false)
		endShow := utils.HandleTimeFormatBySet(dataShow.EndShow, globalData.EddSetting.DateFormat, 2, false, false)
		singleTrackData.ShippingTimeCon = fmt.Sprintf("%s - %s", startShow, endShow)
	}
	if len(singleTrackData.ShippingTimeCon) > 0 {
		singleTrackData.ShippingTimeShow = 1
	}
	globalData.TrackingData = append(globalData.TrackingData, singleTrackData)
}

func (c *ClassicTrackPageService) numberTracking() {
	//现根据此订单查询订单名称
	orderId, _ := c.OrderTrackRepo.GetOrderId(c.TrackPageService.trackGlobalData.User.ID, c.TrackPageService.trackGlobalData.TrackRequestDomain.TrackNumber)
	c.orderEmailTracking(orderId, true, false)
}

func (c *ClassicTrackPageService) orderPhoneTracking() {
	c.orderEmailTracking("", false, true)
}

func (c *ClassicTrackPageService) orderTracking() {
	// 根据 Order name 查询 order id
	orderId, _ := c.OrderTrackRepo.GetOrderIdByOrderName(c.TrackPageService.trackGlobalData.User.ID, c.TrackPageService.trackGlobalData.TrackRequestDomain.OrderName)
	c.orderEmailTracking(orderId, true, false)
}

func (c *ClassicTrackPageService) orderEmailTracking(orderName string, isOrderId, isPhoneTrack bool) {
	if orderName == "" {
		orderName = c.TrackPageService.trackGlobalData.TrackRequestDomain.OrderName
	}
	userId := c.TrackPageService.trackGlobalData.User.ID
	c.TrackPageService.HandleOrderData(userId, orderName,
		c.TrackPageService.trackGlobalData.TrackRequestDomain.Email,
		c.TrackPageService.trackGlobalData.TrackRequestDomain.TrackNumber,
		c.TrackPageService.trackGlobalData.TrackThemeEntity.ClassicSetting.TrackingHistory.Map,
		c.TrackPageService.trackGlobalData.TrackRequestDomain.Phone,
		models.ThemeClassic, isOrderId, isPhoneTrack)

	numberMessage := c.TrackPageService.trackGlobalData.NumberMessage
	if isOrderId && numberMessage != nil && numberMessage[0].OrderName != "" {
		c.TrackPageService.trackGlobalData.OrderRecord.OrderName = numberMessage[0].OrderName
	}
	c.TrackPageService.FusionOrderAndNumberMessage(c, numberMessage)
}

func (c *ClassicTrackPageService) HandleTrackInfo(trackNumber definition.TrackNumberData) definition.TrackingData {
	orderFulfillTime := trackNumber.OrderFulfillTime
	globalData := &c.TrackPageService.trackGlobalData
	orderRecord := globalData.OrderRecord
	lang := globalData.TrackRequestDomain.Lang
	lang = "en"
	var courierCode string
	if trackNumber.Courier != "" {
		courierCode = trackNumber.Courier
	}

	expressInfo := c.CourierRepo.GetCompanyInfoByCodeAndLang(courierCode, lang)

	companyCode, carrierName := courierCode, courierCode
	if expressInfo.CompanyName != "" {
		carrierName = expressInfo.CompanyName
	}
	if expressInfo.CompanyCode != "" {
		companyCode = expressInfo.CompanyCode
	}
	var img string
	if companyCode != "" {
		img = "//" + config.TrackConfig.TmCdnDomain + "/images/icons/express/" + companyCode + ".png?time=" + config.TrackConfig.AssetsVersion
	}
	if companyCode == "cainiao" {
		img = "//" + config.TrackConfig.TmCdnDomain + "/images/icons/express/" + companyCode + "_tmshopify.png?time=" + config.TrackConfig.AssetsVersion
	}
	var url string
	if expressInfo.TrackUrl != "" {
		url = strings.ReplaceAll(expressInfo.TrackUrl, "******", trackNumber.TrackNumber)
		url = strings.ReplaceAll(url, "{tn}", trackNumber.TrackNumber)
	}
	if trackNumber.TrackInfos.OriginInfo.TrackingLink != "" {
		url = trackNumber.TrackInfos.OriginInfo.TrackingLink
	}
	//获取单号状态
	statusNumber := config.StatusNodeNumber(trackNumber.TrackStatus)
	//组装信息
	trackInfo := trackNumber.TrackInfos
	//读取物流信息
	originInfo := trackInfo.OriginInfo.TrackInfo
	destinationInfo := trackInfo.DestinationInfo.TrackInfo

	//如果自定义了订单状态，优先显示自定义订单状态
	var info []definition.ReturnInfo
	if len(globalData.CustomStatusTime) != 0 {
		for k, item := range globalData.CustomStatusTime {
			checkpointDate := time.Unix(int64(item), 0).Format(time.DateTime)
			trackingDetail := utils.GetStatusSummary(globalData.TrackThemeEntity.Translation, k, "")
			newInfo := definition.ReturnInfo{
				TrackInfo: definition.TrackInfo{
					CheckpointDate:           checkpointDate,
					TrackingDetail:           trackingDetail,
					CheckpointDeliveryStatus: utils.GetCheckpointStatus(k),
				}}
			info = append(info, newInfo)
		}
		length := len(info)

		//排序反转
		for i, j := 0, length-1; i < j; i, j = i+1, j-1 {
			// 交换切片中对应位置的元素
			info[i], info[j] = info[j], info[i]
		}
	} else {
		for _, t := range destinationInfo {
			if globalData.User.ID == 54596 {
				info = append(info, definition.ReturnInfo{
					TrackInfo: definition.TrackInfo{
						CheckpointDate:              t.CheckpointDate,
						TrackingDetail:              t.TrackingDetail,
						CheckpointDeliveryStatus:    t.CheckpointDeliveryStatus,
						CheckpointDeliverySubstatus: t.CheckpointDeliverySubstatus,
						Location:                    "",
					}})
			} else {
				info = append(info, definition.ReturnInfo{
					TrackInfo: definition.TrackInfo{
						CheckpointDate:              t.CheckpointDate,
						TrackingDetail:              t.TrackingDetail,
						CheckpointDeliveryStatus:    t.CheckpointDeliveryStatus,
						CheckpointDeliverySubstatus: t.CheckpointDeliverySubstatus,
						Location:                    t.Location,
					}})
			}
		}
		for _, t := range originInfo {
			if globalData.User.ID == 54596 {
				info = append(info, definition.ReturnInfo{
					TrackInfo: definition.TrackInfo{
						CheckpointDate:              t.CheckpointDate,
						TrackingDetail:              t.TrackingDetail,
						CheckpointDeliveryStatus:    t.CheckpointDeliveryStatus,
						CheckpointDeliverySubstatus: t.CheckpointDeliverySubstatus,
						Location:                    "",
					}})
			} else {
				info = append(info, definition.ReturnInfo{
					TrackInfo: definition.TrackInfo{
						CheckpointDate:              t.CheckpointDate,
						TrackingDetail:              t.TrackingDetail,
						CheckpointDeliveryStatus:    t.CheckpointDeliveryStatus,
						CheckpointDeliverySubstatus: t.CheckpointDeliverySubstatus,
						Location:                    t.Location,
					}})
			}
		}
		info = c.TrackPageService.SensitiveFiltering(info)
		if len(info) > 0 {
			utils.CmpTrackInfoDate(info)
		}
	}
	var addMapInfo definition.MapLocation
	//如果已经签收 直接拿shippingaddres 如果有的话 减少无谓的请求
	if statusNumber == config.DeliveredStatusNumber &&
		globalData.ShippingAddressResult.Lat != 0 &&
		globalData.ShippingAddressResult.Lon != 0 {
		addMapInfo = definition.MapLocation{
			Lat: globalData.ShippingAddressResult.Lat,
			Lon: globalData.ShippingAddressResult.Lon,
		}
	} else if globalData.TrackThemeEntity.ClassicSetting.TrackingHistory.Map == "location" &&
		statusNumber != config.PendingStatusNumber && statusNumber != config.InfoReceiveStatusNumber {
		//设置地图显示时并且不是目的地址的时候才获得地图经纬度
		if len(info) != 0 {
			addMapInfo = utils.GetMapLocation(info, "")
			//意大利用户要求
			if addMapInfo.Info.Location != "" && globalData.TrackSettingEntity.UserId == 27496 {
				addMapInfo.Info.Location = addMapInfo.Info.Location + ",Italy"
			}
		} else if statusNumber == config.DeliveredStatusNumber && trackNumber.Destination != "" {
			country := trackNumber.Destination
			addMapInfo = utils.GetMapLocation([]definition.ReturnInfo{}, country)
		} else {
			var tempInfo []definition.ReturnInfo
			for _, t := range originInfo {
				tempInfo = append(tempInfo, definition.ReturnInfo{
					TrackInfo: definition.TrackInfo{
						CheckpointDate:           t.CheckpointDate,
						TrackingDetail:           t.TrackingDetail,
						CheckpointDeliveryStatus: t.CheckpointDeliveryStatus,
					}})
			}
			addMapInfo = utils.GetMapLocation(tempInfo, "")
		}
	}

	//是否显示时效 快递公司预计到达时间 如果存在则显示到页面 start
	var shippingTimeShow int
	shippingTimeOpen := globalData.EddSetting.Switch
	//快递信息有预计到达时间
	scheduledDeliveryDate := trackInfo.ExpectedDeliveryTime
	//获取最后一条物流信息 shipping_time 用户设置时效
	var infoLastTimeStr, infoLastStatus string
	if !scheduledDeliveryDate.IsZero() || shippingTimeOpen {
		if len(info) > 0 {
			infoLast := info[0]
			infoLastTimeStr = infoLast.CheckpointDate
			infoLastStatus = infoLast.CheckpointDeliveryStatus
		}
	}
	var infoLastTime int64
	if infoLastTimeStr != "" {
		infoTime, _ := time.Parse(time.DateOnly, infoLastTimeStr)
		infoLastTime = infoTime.Unix()
	}
	var shippingTimeCon string
	var statusShipping int64
	//时效数据获取 是否设置时效
	if infoLastStatus != "delivered" {
		if !scheduledDeliveryDate.IsZero() {
			deliveryDate := scheduledDeliveryDate.Unix()
			statusShipping = deliveryDate - infoLastTime
			if statusShipping > 0 {
				shippingTimeShow = 1
				shippingTimeCon = utils.HandleTimeFormatBySet(deliveryDate, globalData.EddSetting.DateFormat, 2, false, false)
			}
		} else {
			eddService := NewEddService()
			calculateEntity := eddService.GetCalculateEntity(globalData.EddSetting, TrackCalculateData{
				UserId:           orderRecord.UserId,
				OrderCreateTime:  orderRecord.OrderCreateTime,
				OrderFulfillTime: orderFulfillTime,
				Destination:      orderRecord.RecipientCountryCode,
				Courier:          trackNumber.Courier,
				TrackNumber:      trackNumber.TrackNumber,
				FulfillmentId:    trackNumber.FulfillmentId,
			})
			dataShow := eddService.CalculateEddByOrderAndDestinationByEntities(orderRecord.UserId,
				calculateEntity, globalData.EddSetting)
			if shippingTimeOpen && dataShow.StartShow != 0 && dataShow.EndShow != 0 {
				statusShipping = dataShow.EndShow - infoLastTime
				if statusShipping > 0 {
					shippingTimeShow = 1
					// 显示内容
					startShow := utils.HandleTimeFormatBySet(dataShow.StartShow, globalData.EddSetting.DateFormat, 2, false, false)
					endShow := utils.HandleTimeFormatBySet(dataShow.EndShow, globalData.EddSetting.DateFormat, 2, false, false)
					shippingTimeCon = fmt.Sprintf("%s - %s", startShow, endShow)
				}
			}
		}
	}
	//是否显示时效 快递公司预计到达时间如果存在则显示到页面 end

	//时效显示与内容

	//发件国家电话
	phone := trackInfo.OriginInfo.CourierPhone
	if c.TrackPageService.trackGlobalData.User.ID == 45233 {
		phone = "************"
	} else if expressInfo.CompanyTel != "" {
		phone = expressInfo.CompanyTel
	}
	//用户判断错误签收时间,最后一条信息不是 delivered 情况
	var statusNode map[config.StatusNodeNumber]definition.ReturnInfo
	var statusNum definition.StatusNum
	// 如果 track info 的 created_at 时间比物流轨迹的更新时间要晚，则使用最早一条的物流轨迹时间作为 Shipped 状态的时间
	if len(info) != 0 {
		firstInfoTime, err := utils.ParseTimeManual(info[len(info)-1].CheckpointDate)
		if err == nil {
			earliestTrackInfoTime := int(firstInfoTime.Unix())
			if earliestTrackInfoTime < trackNumber.TrackInfoCreatedAt {
				trackNumber.TrackInfoCreatedAt = earliestTrackInfoTime
			}
		}
	}
	// Shipped 状态的时间如果小于 OrderFulfillTime (Order Ready 节点)， 则使用 OrderFulfillTime
	if trackNumber.TrackInfoCreatedAt < c.TrackPageService.trackGlobalData.OrderRecord.OrderFulfillTime && c.TrackPageService.trackGlobalData.OrderRecord.OrderFulfillTime != 0 {
		trackNumber.TrackInfoCreatedAt = c.TrackPageService.trackGlobalData.OrderRecord.OrderFulfillTime
	}
	info = utils.ChangeInfoStatus(statusNumber, info)
	info, statusNode = c.TrackPageService.HandleInfoStatus(info, trackNumber, true)
	statusNode, statusNum = c.TrackPageService.HandleTrackingNode(statusNode, trackNumber.TrackInfoCreatedAt)
	var status string
	for key, value := range utils.GetStatusConfig(0) {
		if value.Status == statusNumber {
			status = globalData.TrackThemeEntity.Translation["status_"+key]
			if status == "" {
				status = value.StatusName
			}
		}
	}

	var data definition.TrackingData
	data.StatusNode = statusNode
	data.StatusNum = statusNum
	data.Trackinfo = info
	data.Countryname = trackInfo.OriginalCountry
	data.Destinationcountry = trackInfo.DestinationCountry
	data.StausDataNum = statusNumber
	_, exist := statusNode[2]
	if statusNumber == 1 && len(statusNode) >= 2 && exist && statusNode[2].CheckpointStatus != "" {
		data.StausDataNum = 2
	}
	if trackInfo.OriginInfo.DestinationTrackNumber != "" && trackInfo.OriginInfo.DestinationTrackCourier != "cainiao" {
		destinationTrack := struct {
			Name        string `json:"name"`
			Code        string `json:"code"`
			Url         string `json:"url"`
			Img         string `json:"img"`
			TrackNumber string `json:"track_number"`
		}{
			TrackNumber: trackInfo.OriginInfo.DestinationTrackNumber,
		}
		if len(trackInfo.OriginInfo.DestinationCourier) > 0 && orderRecord.UserId == 2194 {
			destinationTrack.Code = trackInfo.OriginInfo.DestinationCourier
			destinationTrack.Name = trackInfo.OriginInfo.DestinationCourier
			destinationTrack.Url = trackInfo.DestinationInfo.TrackingLink
		} else {
			destinationTrack.Code = trackInfo.OriginInfo.DestinationTrackCourier
			destinationTrack.Name = trackInfo.OriginInfo.DestinationTrackCourier
			expressInfo := c.CourierRepo.GetCompanyInfoByCodeAndLang(destinationTrack.Code, lang)

			if expressInfo.CompanyName != "" {
				destinationTrack.Name = expressInfo.CompanyName
			}
			destinationTrack.Img = "//" + config.TrackConfig.TmCdnDomain + "/images/icons/express/" + destinationTrack.Code + ".png?time=" + config.TrackConfig.AssetsVersion
			if expressInfo.TrackUrl != "" {
				destinationTrack.Url = strings.ReplaceAll(expressInfo.TrackUrl, "******", destinationTrack.TrackNumber)
				destinationTrack.Url = strings.ReplaceAll(destinationTrack.Url, "{tn}", destinationTrack.TrackNumber)
				destinationTrack.Url = strings.ReplaceAll(destinationTrack.Url, "{tpc}", orderRecord.RecipientZip)
				destinationTrack.Url = strings.ReplaceAll(destinationTrack.Url, "{tdc_ios2}", orderRecord.RecipientCountryCode)
			} else {
				destinationTrack.Url = expressInfo.CompanyUrl
			}
		}
		data.DestinationCourier = destinationTrack
	}
	data.ShippingTimeShow = shippingTimeShow
	data.ShippingTimeCon = shippingTimeCon
	data.Map = addMapInfo
	data.CompanyCode = companyCode
	data.CarrierName = carrierName
	data.Courier = struct {
		Name string `json:"name"`
		Code string `json:"code"`
		Url  string `json:"url"`
		Img  string `json:"img"`
	}{Name: carrierName, Code: companyCode, Url: url, Img: img}
	data.Img = img
	data.Url = url
	data.Status = status
	data.OriginPhone = phone
	return data
}

func (c *ClassicTrackPageService) returnData() map[string]interface{} {
	trackGlobalData := c.TrackPageService.trackGlobalData
	// 预填充预览单号
	for _, v := range domain.AdvanceFillPreview {
		if v == trackGlobalData.TrackRequestDomain.Shop {
			trackGlobalData.TrackRequestDomain.Email = domain.PreviewEmail
			trackGlobalData.TrackRequestDomain.TrackNumber = domain.PreviewNumber
			trackGlobalData.TrackRequestDomain.OrderName = domain.PreviewOrder
			trackGlobalData.RootData.IsNoTrack = 1
			break
		}
	}
	//用户特殊处理
	c.TrackPageService.doSomeSetSpecialUser()
	var htmlExtend string
	configData := make(map[string]interface{})
	// 追加默认 css
	addCss := ".tm_tracking_content .tm_tracking_one {text-align: center;}.tm_tracking_content input {display: inline-block !important;}"

	configData["track_setting"] = map[string]interface{}{
		"api_url":         c.TrackPageService.trackGlobalData.TrackSettingEntity.ApiUrl,
		"api_track_url":   c.TrackPageService.trackGlobalData.TrackSettingEntity.ApiTrackUrl,
		"track_url":       c.TrackPageService.trackGlobalData.TrackSettingEntity.TrackUrl,
		"shipping_time":   c.TrackPageService.trackGlobalData.EddSetting,
		"show_copy_right": c.TrackPageService.trackGlobalData.TrackSettingEntity.ShowCopyRight,
	}

	configData["theme_setting"] = map[string]interface{}{
		"user_id":       c.TrackPageService.trackGlobalData.TrackThemeEntity.UserId,
		"theme_code":    c.TrackPageService.trackGlobalData.TrackThemeEntity.ThemeCode,
		"custom_style":  c.TrackPageService.trackGlobalData.TrackThemeEntity.CustomStyle,
		"top_html":      c.TrackPageService.trackGlobalData.TrackThemeEntity.TopHtml,
		"bottom_html":   c.TrackPageService.trackGlobalData.TrackThemeEntity.BottomHtml,
		"theme_setting": c.TrackPageService.trackGlobalData.TrackThemeEntity.ClassicSetting,
		"translation":   c.TrackPageService.trackGlobalData.TrackThemeEntity.Translation,
	}
	configData["shop_name"] = c.TrackPageService.trackGlobalData.User.Name
	if configData["shop_name"] == "" {
		configData["shop_name"] = strings.Split(c.TrackPageService.trackGlobalData.TrackRequestDomain.Shop, ".")[0]
	}
	configData["status_config"] = utils.GetStatusConfig(c.TrackPageService.trackGlobalData.TrackThemeEntity.UserId)
	configData["shop"] = c.TrackPageService.trackGlobalData.TrackRequestDomain.Shop
	numberTrack := c.TrackPageService.trackGlobalData.TrackThemeEntity.ClassicSetting.OrderLookup.Number
	orderTrack := c.TrackPageService.trackGlobalData.TrackThemeEntity.ClassicSetting.OrderLookup.Order
	if !orderTrack && numberTrack {
		addCss = addCss + " .tm_tracking_content input {text-align:center;}"
	}
	if c.TrackPageService.trackGlobalData.TrackThemeEntity.ClassicSetting.General.Width == 800 {
		addCss = addCss + ".tm_tracking_content .progress-bar-style .progress-bar-node:last-child > span>span, " +
			".tm_tracking_content .progress-bar-style .progress-bar-node:nth-of-type(1) > span>span{max-width: 100px;word-wrap: break-word;}"
	}
	if orderTrack && numberTrack {
		addCss = addCss + " .tm_tracking_content .tm_tracking_form_div h1.tm_tracking_title{text-align:center;}"
	}

	htmlExtend = fmt.Sprintf("%s<style>%s</style>", htmlExtend, html.UnescapeString(addCss))

	// 获取针对 shopify 模板设置信息
	// 针对用户主题 设置 button input a 标签样式

	themeArr, _ := c.ThemesSettingRepo.GetThemesSettingArr()

	configData["theme_arr"] = themeArr
	// 获取针对主题模板添加样式
	themeCssRs, _ := database.RS.Get(config.CacheKey.ThemeCss)
	themeCss := string(themeCssRs)
	if themeCss == "" {
		themeCssA, _ := c.ThemeCssRepo.GetThemeCss()
		themeCss = themeCssA.ModCss
		if themeCss != "" {
			_ = database.RS.Set(config.CacheKey.ThemeCss, themeCss)
		}
	}
	configData["mod_css"] = themeCss

	// shopify 主题模板样式加载
	if themeCss != "" {
		htmlExtend = fmt.Sprintf("%s<style>%s</style>", htmlExtend, html.UnescapeString(themeCss))
	}
	// 自定义 css
	if c.TrackPageService.trackGlobalData.TrackThemeEntity.CustomStyle != "" {
		htmlExtend = fmt.Sprintf("%s<style>%s</style>", htmlExtend, c.TrackPageService.trackGlobalData.TrackThemeEntity.CustomStyle)
	}
	if !config.Get().TrackTest {
		htmlExtend = htmlExtend + "<script>window.moneyFormat = {{ shop.money_format | json }}</script>"
	} else {
		htmlExtend = htmlExtend + "<script>window.moneyFormat = '￥{{amount}}';</script>"
	}
	// 添加 seo 设置 {% assign my_variable = false %}
	userName := c.TrackPageService.trackGlobalData.User.Name

	trackSeo := c.TrackPageService.trackGlobalData.TrackSettingEntity.TrackSeo
	defaultTitle := "Track order status"
	defaultDesc := "Track delivery status of your packages. Powered by TrackingMore."
	if userName != "" {
		defaultTitle = "Track order status - " + userName
		defaultDesc = "Track delivery status of your packages - " + userName + ". Powered by TrackingMore."
	}
	seoTitle := strings.TrimSpace(trackSeo.SeoTitle)
	if seoTitle == "" {
		seoTitle = defaultTitle
	}
	seoTitle = html.UnescapeString(seoTitle)
	seoDescription := strings.TrimSpace(trackSeo.SeoDesc)
	if seoDescription == "" {
		seoDescription = defaultDesc
	}
	seoDescription = html.UnescapeString(seoDescription)

	// 不需要引入 jq 的店铺 由于已经引入
	pageData := c.TrackPageService.trackGlobalData.RootData
	pageData.ConfigData = configData
	pageData.Tracking = c.TrackPageService.trackGlobalData.TrackingData

	userId := c.TrackPageService.trackGlobalData.User.ID
	if userId == 39227 || userId == 0 || userId == 10903 || userId == 38697 {
		pageData.ConfigData["hide_status"] = []string{"transit"}
	}
	if userId == 27506 || userId == 0 || userId == 58414 {
		pageData.ConfigData["hide_status"] = []string{"pickup"}
	}

	orderName := c.TrackPageService.trackGlobalData.TrackRequestDomain.OrderName
	if orderName == "" {
		orderName = c.TrackPageService.trackGlobalData.OrderRecord.OrderName
	}
	orderEmail := c.TrackPageService.trackGlobalData.OrderRecord.CustomerEmail
	if orderEmail == "" {
		orderEmail = c.TrackPageService.trackGlobalData.TrackRequestDomain.Email
	}
	orderPhone := c.TrackPageService.trackGlobalData.OrderRecord.CustomerPhone
	if orderPhone == "" {
		orderPhone = c.TrackPageService.trackGlobalData.TrackRequestDomain.Phone
	}
	return map[string]interface{}{
		"html_extend":     htmlExtend,
		"add_css":         addCss,
		"seo_title":       seoTitle,
		"seo_description": seoDescription,
		"css":             c.TrackPageService.trackGlobalData.TrackRequestDomain.Url.Css,
		"js":              c.TrackPageService.trackGlobalData.TrackRequestDomain.Url.Js,
		"data":            pageData,
		"shop":            c.TrackPageService.trackGlobalData.TrackRequestDomain.Shop,
		"lang":            c.TrackPageService.trackGlobalData.TrackRequestDomain.Lang,
		"preview":         c.TrackPageService.trackGlobalData.TrackRequestDomain.Preview,
		"order_name":      orderName,
		"email":           orderEmail,
		"phone":           orderPhone,
		"track_number":    c.TrackPageService.trackGlobalData.TrackRequestDomain.TrackNumber,
		"top_html":        c.TrackPageService.trackGlobalData.TrackThemeEntity.TopHtml,
		"bottom_html":     c.TrackPageService.trackGlobalData.TrackThemeEntity.BottomHtml,
		"is_embed":        c.TrackPageService.trackGlobalData.TrackRequestDomain.IsEmbed,
		"phone_email":     c.TrackPageService.trackGlobalData.TrackRequestDomain.PhoneEmail,
	}
}

func (c *ClassicTrackPageService) PluginData() map[string]interface{} {
	orderName := c.TrackPageService.trackGlobalData.TrackRequestDomain.OrderName
	if orderName == "" {
		orderName = c.TrackPageService.trackGlobalData.OrderRecord.OrderName
	}
	orderEmail := c.TrackPageService.trackGlobalData.OrderRecord.CustomerEmail
	if orderEmail == "" {
		orderEmail = c.TrackPageService.trackGlobalData.TrackRequestDomain.Email
	}
	orderPhone := c.TrackPageService.trackGlobalData.OrderRecord.CustomerPhone
	if orderPhone == "" {
		orderPhone = c.TrackPageService.trackGlobalData.TrackRequestDomain.Phone
	}
	return map[string]interface{}{
		"error":        c.TrackPageService.trackGlobalData.RootData.Error,
		"order_number": orderName,
		"email":        orderEmail,
		"phone":        orderPhone,
		"track_number": c.TrackPageService.trackGlobalData.TrackRequestDomain.TrackNumber,
		"theme_setting": map[string]interface{}{
			"settings":     c.TrackPageService.trackGlobalData.TrackThemeEntity.ClassicSetting,
			"translation":  c.TrackPageService.trackGlobalData.TrackThemeEntity.Translation,
			"custom_style": c.TrackPageService.trackGlobalData.TrackThemeEntity.CustomStyle,
		},
		"status_config":   utils.GetStatusConfig(c.TrackPageService.trackGlobalData.TrackThemeEntity.UserId),
		"status_key_arr":  utils.GetStatusKey([]config.StatusNodeNumber{}),
		"tracking":        c.TrackPageService.trackGlobalData.TrackingData,
		"shop":            c.TrackPageService.trackGlobalData.TrackRequestDomain.Shop,
		"api_url":         c.TrackPageService.trackGlobalData.TrackSettingEntity.ApiUrl,
		"product":         c.TrackPageService.trackGlobalData.RootData.Product,
		"show_copy_right": c.TrackPageService.trackGlobalData.TrackSettingEntity.ShowCopyRight,
	}
}

func NewClassicTrackPageService() *ClassicTrackPageService {
	return &ClassicTrackPageService{
		TrackPageService: NewTrackPageService(),
		OrderTrackRepo:   &impl.OrderTrack{},
		CourierRepo:      &impl.Courier{},
	}
}
