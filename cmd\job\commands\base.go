package commands

import (
	"log"

	"tmshopify/internal/infras/config"
	"tmshopify/internal/jobs/application"
	"tmshopify/internal/providers"
)

func initService() *application.SyncOrderService {
	db, err := config.NewDB("db_conf")
	if err != nil {
		log.Fatalf("db init error:%v", err)
	}

	redisClient, err := config.NewRedis("redis_conf")
	if err != nil {
		log.Fatalf("redis init error:%v", err)
	}

	/*ossClient, err := config.NewAliyunOss("aliyun_conf.oss")
	if err != nil {
		log.Fatalf("db init error:%v", err)
	}*/

	// 初始化repos
	repos := providers.NewRepositories(db, redisClient)
	return application.NewSyncOrderService(repos)
}
