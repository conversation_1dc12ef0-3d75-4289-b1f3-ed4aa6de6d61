package models

import "github.com/shopspring/decimal"

type OrderProduct struct {
	OrderId       string          `gorm:"column:order_id;type:varchar(255);default:0;NOT NULL" json:"order_id"`
	ProductId     string          `gorm:"column:product_id;type:varchar(255);default:0;NOT NULL" json:"product_id"`
	VariantId     string          `gorm:"column:variant_id;type:varchar(255);default:0;NOT NULL" json:"variant_id"`
	FulfillmentId string          `gorm:"column:fulfillment_id;type:varchar(255);default:0;NOT NULL" json:"fulfillment_id"`
	LineItemId    string          `gorm:"column:line_item_id;type:varchar(255);default:0;NOT NULL" json:"line_item_id"`
	Quantity      int             `gorm:"column:quantity;type:int(11);default:0;comment:商品数量;NOT NULL" json:"quantity"`
	Name          string          `gorm:"column:name;type:varchar(255);comment:发货名称;NOT NULL" json:"name"`
	Title         string          `gorm:"column:title;type:varchar(500);comment:产品标题;NOT NULL" json:"title"`
	Price         decimal.Decimal `gorm:"column:price;type:decimal(11,2);default:0.00;NOT NULL" json:"price"`
	Image         string          `gorm:"column:image;type:varchar(255);comment:图片访问路径;NOT NULL" json:"image"`
	Sku           string          `gorm:"column:sku;type:varchar(255);NOT NULL" json:"sku"`
	Handle        string          `gorm:"column:handle;type:varchar(255);NOT NULL" json:"handle"`
	Model
	Partition
}

func (OrderProduct) TableName() string {
	return "order_products"
}
