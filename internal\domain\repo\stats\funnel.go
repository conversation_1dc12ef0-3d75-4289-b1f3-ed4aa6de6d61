package stats

import (
	"context"
	"time"

	"tmshopify/internal/domain/entity"
)

// FunnelRepository 付费转化漏斗统计Repository接口
type FunnelRepository interface {
	// CountNewInstallUsersInTimeRange 统计时段内新安装且60天内注册的用户数
	CountNewInstallUsersInTimeRange(ctx context.Context, startTime, endTime time.Time) (int64, error)

	// CountTrialUsersInTimeRange 统计时段内开启免费试用且60天内注册的用户数
	CountTrialUsersInTimeRange(ctx context.Context, startTime, endTime time.Time) (int64, error)

	// CountPaidConversionUsersInTimeRange 统计时段内付费转化且60天内注册的用户数
	CountPaidConversionUsersInTimeRange(ctx context.Context, startTime, endTime time.Time) (int64, error)

	// CountRenewalUsersInTimeRange 统计时段内次月续费且60天内注册的用户数
	CountRenewalUsersInTimeRange(ctx context.Context, startTime, endTime time.Time) (int64, error)

	// GetUsersWithin60DaysAndStatus 获取注册60天内且指定状态的付费转化记录
	GetUsersWithin60DaysAndStatus(ctx context.Context, status int) ([]*entity.DataPaidConversionLog, error)

	// UpdatePaidConversionLog 更新付费转化记录
	UpdatePaidConversionLog(ctx context.Context, log *entity.DataPaidConversionLog) error

	// GetPaidConversionLogByUserID 根据用户ID获取付费转换记录
	GetPaidConversionLogByUserID(ctx context.Context, userID int64) (*entity.DataPaidConversionLog, error)

	// CreatePaidConversionLog 创建付费转换记录
	CreatePaidConversionLog(ctx context.Context, log *entity.DataPaidConversionLog) error
}
