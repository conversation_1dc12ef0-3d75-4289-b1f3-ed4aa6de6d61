package models

type ChargeGroupStores struct {
	UserId        int    `gorm:"column:user_id;type:INT(10) UNSIGNED;not null;" json:"user_id"`
	StoreName     string `gorm:"column:store_name;type:VARCHAR(255);comment:用户店铺名;not null;" json:"store_name"`                          // 用户店铺名
	MainUserId    int    `gorm:"column:main_user_id;type:INT(10) UNSIGNED;comment:主店铺用户id;not null;" json:"main_user_id"`                // 主店铺用户id
	ConnectStatus int    `gorm:"column:connect_status;type:TINYINT(3) UNSIGNED;comment:是否连接;not null;default:1;" json:"connect_status"`  // 是否连接
	SyncStatus    int    `gorm:"column:sync_status;type:TINYINT(3) UNSIGNED;comment:是否开启同步;not null;default:1;" json:"sync_status"`      // 是否开启同步
	ProDisabled   int    `gorm:"column:pro_disabled;type:TINYINT(3) UNSIGNED;comment:是否主账户非pro;not null;default:0;" json:"pro_disabled"` // 是否主账户非pro
	Consume       int    `gorm:"column:consume;type:INT(10) UNSIGNED;comment:子店铺消费额度;not null;default:0;" json:"consume"`                // 子店铺消费额度
	Model
}

func (ChargeGroupStores) TableName() string {
	return "charge_group_stores"
}
