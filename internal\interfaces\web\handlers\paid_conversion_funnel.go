package handlers

import (
	"net/http"
	"time"

	"tmshopify/internal/application/stats"
	statsRepo "tmshopify/internal/domain/repo/stats"
	"tmshopify/server/response"

	"github.com/gin-gonic/gin"
)

type PaidConversionFunnelHandler struct {
	funnelService *stats.FunnelService
}

func NewPaidConversionFunnelHandler(funnelRepo statsRepo.FunnelRepository) *PaidConversionFunnelHandler {
	return &PaidConversionFunnelHandler{
		funnelService: stats.NewFunnelService(funnelRepo),
	}
}

// FunnelSummaryRequest 付费转化漏斗汇总请求参数
type FunnelSummaryRequest struct {
	StartTime string `json:"start_time" binding:"required"`
	EndTime   string `json:"end_time" binding:"required"`
}

// GetFunnelSummary 获取付费转化漏斗汇总数据
func (h *PaidConversionFunnelHandler) GetFunnelSummary(c *gin.Context) {
	var req FunnelSummaryRequest

	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		response.Fail(c, &response.FailResponse{
			Code:    http.StatusBadRequest,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	// 解析时间并调整为完整的日期范围
	startTime, err := time.Parse("2006-01-02", req.StartTime)
	if err != nil {
		response.Fail(c, &response.FailResponse{
			Code:    http.StatusBadRequest,
			Message: "开始时间格式错误: " + err.Error(),
		})
		return
	}

	endTime, err := time.Parse("2006-01-02", req.EndTime)
	if err != nil {
		response.Fail(c, &response.FailResponse{
			Code:    http.StatusBadRequest,
			Message: "结束时间格式错误: " + err.Error(),
		})
		return
	}

	// 时间范围标准化：确保统计按完整天数进行
	// 用户可能输入任意时间点，但漏斗统计需要按完整天数计算以保证数据一致性
	// 例如：用户输入 "2024-01-15 14:30:25" 会被调整为 "2024-01-15 00:00:00"
	//      用户输入 "2024-01-17 09:45:10" 会被调整为 "2024-01-17 23:59:59"
	// 这样确保查询的是完整的日期范围，避免遗漏或重复统计数据
	startTime = time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, startTime.Location())
	endTime = time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 23, 59, 59, 0, endTime.Location())

	// 调用service获取数据
	data, err := h.funnelService.GetFunnelStatistics(c.Request.Context(), startTime, endTime)
	if err != nil {
		response.Fail(c, &response.FailResponse{
			Code:    http.StatusInternalServerError,
			Message: "获取漏斗数据失败: " + err.Error(),
		})
		return
	}

	// 构建返回结果
	summary := map[string]interface{}{
		"total_new_install_users":        data.NewInstallUsers,
		"total_free_trial_users":         data.FreeTrialUsers,
		"total_paid_conversion_users":    data.PaidConversionUsers,
		"total_next_month_renewal_users": data.NextMonthRenewalUsers,
	}

	response.Success(c, &response.SuccessResponse{
		Code: http.StatusOK,
		Data: summary,
	})
}
