package models

import "tmshopify/config"

type OrderRecords struct {
	Partition
	OrderId              string                  `gorm:"column:order_id;type:char(40);comment:订单 id;NOT NULL" json:"order_id"`
	OrderName            string                  `gorm:"column:order_name;type:char(100);comment:订单名称;NOT NULL" json:"order_name"`
	CustomerName         string                  `gorm:"column:customer_name;type:char(100);comment:客户名称;NOT NULL" json:"customer_name"`
	CustomerEmail        string                  `gorm:"column:customer_email;type:char(255);comment:客户邮箱;NOT NULL" json:"customer_email"`
	CustomerPhone        string                  `gorm:"column:customer_phone;type:char(50);comment:客户电话号码;NOT NULL" json:"customer_phone"`
	Product              string                  `gorm:"column:product;type:varchar(225);comment:产品 id 逗号隔开;NOT NULL" json:"product"`
	Currency             string                  `gorm:"column:currency;type:char(10);comment:币种;NOT NULL" json:"currency"`
	OrderNote            string                  `gorm:"column:order_note;type:text;comment:订单备注信息" json:"order_note"`
	OrderSource          string                  `gorm:"column:order_source;type:char(100);comment:订单来源;NOT NULL" json:"order_source"`
	OrderCreateTime      int                     `gorm:"column:order_create_time;type:int(11);default:0;comment:订单创建时间;NOT NULL" json:"order_create_time"`
	FulfillmentStatus    int                     `gorm:"column:fulfillment_status;type:tinyint(2);default:0;comment:发货状态;NOT NULL" json:"fulfillment_status"`
	FinancialStatus      int                     `gorm:"column:financial_status;type:tinyint(2);default:0;comment:支付状态;NOT NULL" json:"financial_status"`
	RecipientPhone       string                  `gorm:"column:recipient_phone;type:varchar(50);comment:收件人手机号;NOT NULL" json:"recipient_phone"`
	RecipientZip         string                  `gorm:"column:recipient_zip;type:varchar(50);comment:收件人邮编;NOT NULL" json:"recipient_zip"`
	RecipientCountryCode string                  `gorm:"column:recipient_country_code;type:varchar(10);comment:收件人国家简码;NOT NULL" json:"recipient_country_code"`
	ShippingAddress      string                  `gorm:"column:shipping_address;type:text;comment:收件人地址" json:"shipping_address"`
	CustomTrackStatus    config.StatusNodeNumber `gorm:"column:custom_track_status;type:int(4) unsigned;default:0;comment:订单自定义状态;NOT NULL" json:"custom_track_status"`
	CustomStatusTime     string                  `gorm:"column:custom_status_time;type:char(255);comment:订单自定义状态时间 (json);NOT NULL" json:"custom_status_time"`
	IsDelete             int                     `gorm:"column:is_delete;type:tinyint(1);default:0;comment:是否删除;NOT NULL" json:"is_delete"`
	DataSource           int                     `gorm:"column:data_source;type:tinyint(1);default:0;comment:数据添加来源 (1同步 2手动添加 3导入 4API 5其他);NOT NULL" json:"data_source"`
	WhetherPay           int                     `gorm:"column:whether_pay;type:tinyint(1);default:0;comment:是否付费;NOT NULL" json:"whether_pay"`
	OrderTracks          []OrderTracks           `gorm:"foreignKey:OrderId;references:OrderId;" json:"order_tracks"`
	Model
}

func (OrderRecords) TableName() string {
	return "order_records"
}
