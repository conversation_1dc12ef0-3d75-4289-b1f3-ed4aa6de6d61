package service

import (
	"errors"
	"time"
	"tmshopify/pkg/auth"
	"tmshopify/pkg/mail"
	"tmshopify/store/database"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"

	"golang.org/x/crypto/bcrypt"
)

type Admin struct {
	adminUserRepo *impl.AdminUser
}

func (a *Admin) ChangePassword(user models.AdminUsers, newPassword string) (models.AdminUsers, error) {

	hash, err := bcrypt.GenerateFromPassword([]byte(newPassword), 12)
	if err != nil {
		return user, err
	}
	if err := a.adminUserRepo.Update(user.IDtoString(), map[string]interface{}{
		"password":   string(hash),
		"created_at": time.Now(),
	}); err != nil {
		return user, err
	}
	return user, nil
}

func (a *Admin) SendEmailCheck(email string) error {
	if str, _ := database.RS.Get("adminCodeSend:" + email); string(str) != "" {
		return errors.New("验证码发送过于频繁")
	}
	var user models.AdminUsers

	if err := database.DB.First(&user, "email = ?", email).Error; err != nil {
		return errors.New("用户不存在")
	}
	err := mail.NewMailgun().SendVerifyCode(email, "", "")
	if err != nil {
		return err
	}
	err = database.RS.SetWithExpire("adminCodeSend:"+email, "1", time.Minute)
	if err != nil {
		return err
	}
	return nil
}
func (a *Admin) SendResetLink(email string) error {
	if str, _ := database.RS.Get("adminResetLinkSend:" + email); string(str) != "" {
		return errors.New("验证码发送过于频繁")
	}
	var user models.AdminUsers

	if err := database.DB.First(&user, "email = ?", email).Error; err != nil {
		return errors.New("用户不存在")
	}
	token, _ := auth.New().CreateUserTokenWithExpire(user.ID, "admin", time.Minute*15)
	err := mail.NewMailgun().SendResetLink(email, token.Token)
	if err != nil {
		return err
	}
	return nil
}

func (a *Admin) DealAdminUserData(user models.AdminUsers) map[string]interface{} {
	// 只保留slug字段，可以创建一个新的切片来存储
	var roleSlugs []struct {
		RoleName string `json:"roleName"`
		Value    string `json:"value"`
	}
	for _, role := range user.Roles {
		roleSlugs = append(roleSlugs, struct {
			RoleName string `json:"roleName"`
			Value    string `json:"value"`
		}{RoleName: role.Name, Value: role.Slug})
	}

	return map[string]interface{}{
		"email":    user.Email,
		"username": user.Username,
		"name":     user.Name,
		"id":       user.ID,
		"avatar":   user.Avatar,
		"roles":    roleSlugs,
	}
}

func NewAdmin() *Admin {
	return &Admin{
		adminUserRepo: &impl.AdminUser{},
	}
}
