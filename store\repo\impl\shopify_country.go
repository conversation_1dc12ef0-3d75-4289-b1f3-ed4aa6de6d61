package impl

import (
	"tmshopify/pkg/utils/paginate"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type ShopifyCountry struct {
	Helper
}

// GetShopifyCountryIdAndContinent 查询id和continent
func (s *ShopifyCountry) GetShopifyCountryIdAndContinent(code string) (struct {
	Id        int
	Continent string
}, error) {
	var res struct {
		Id        int
		Continent string
	}
	err := database.DB.Model(&models.ShopifyCountries{}).Where("code = ?", code).Scan(&res).Error

	return res, err
}

func (s *ShopifyCountry) SearchCountryByNameField(name string) []models.ShopifyCountries {
	var countries []models.ShopifyCountries
	database.DB.Model(&models.ShopifyCountries{}).Where("name like ? or code like ?", "%"+name+"%", "%"+name+"%").Limit(10).Find(&countries)
	return countries
}

func (s *ShopifyCountry) Count() int64 {
	var count int64
	database.DB.Model(&models.ShopifyCountries{}).Count(&count)
	return count
}

func (s *ShopifyCountry) Paginate(paginateParam *paginate.Param) ([]models.ShopifyCountries, int64, error) {
	var items []models.ShopifyCountries
	total := s.Count()

	result := database.DB.Scopes(paginate.ORMScope(paginateParam)).Order("id desc").Find(&items)

	return items, total, result.Error
}
