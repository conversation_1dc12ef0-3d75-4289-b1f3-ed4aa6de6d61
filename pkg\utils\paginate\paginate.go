package paginate

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Param struct {
	Page     int `json:"page,omitempty"`
	PageSize int `json:"pageSize,omitempty"`
}

func RequestParam(c *gin.Context) *Param {
	page, _ := strconv.Atoi(c.PostForm("page"))
	if page <= 0 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.PostForm("pageSize"))
	if pageSize <= 0 {
		pageSize = 15
	}

	return &Param{
		Page:     page,
		PageSize: pageSize,
	}
}
func RequestJsonParam(c *gin.Context) *Param {
	var param Param
	err := c.ShouldBind(&param)
	if err != nil {
		return &Param{
			Page:     1,
			PageSize: 15,
		}
	}
	if param.Page <= 0 {
		param.Page = 1
	}

	if param.PageSize <= 0 {
		param.PageSize = 15
	}

	return &param
}

func ORMScope(param *Param) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if param.Page == 0 {
			param.Page = 1
		}
		if param.PageSize == 0 {
			param.PageSize = 20
		}

		offset := (param.Page - 1) * param.PageSize

		return db.Offset(offset).Limit(param.PageSize)
	}
}
