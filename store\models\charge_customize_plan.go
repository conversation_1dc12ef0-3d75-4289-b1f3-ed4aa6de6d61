package models

import "gorm.io/gorm"

type ChargeCustomizePlans struct {
	UserId      int            `gorm:"column:user_id;type:int(11);NOT NULL" json:"user_id"`
	Name        string         `gorm:"column:name;type:varchar(255);NOT NULL" json:"name"`
	Price       float64        `gorm:"column:price;type:decimal(10,2);default:0.00;comment:套餐金额;NOT NULL" json:"price"`
	Plan        int            `gorm:"column:plan;type:tinyint(4);default:0;comment:套餐等级，和原等级相同时将替换原配置;NOT NULL" json:"plan"`
	Count       uint           `gorm:"column:count;type:int(11) unsigned;default:0;comment:套餐额度;NOT NULL" json:"count"`
	Level       uint           `gorm:"column:level;type:tinyint(4) unsigned;default:0;comment:权限等级;NOT NULL" json:"level"`
	SupportType uint           `gorm:"column:support_type;type:tinyint(4) unsigned;default:0;comment:支持的套餐类型，默认0，1为只支持年套餐订阅，2为只支持月套餐订阅;NOT NULL" json:"support_type"`
	Version     Version        `gorm:"column:version;type:char(6);default:202306;comment:套餐版本;NOT NULL" json:"version"`
	Unit        float64        `gorm:"column:unit;type:decimal(3,3);default:0.004;comment:单价,用作展示;NOT NULL" json:"unit"`
	Note        string         `gorm:"column:note;type:text;NOT NULL" json:"note"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at"`
	PlanType    int            `gorm:"column:plan_type;type:tinyint(4) unsigned;default:0;comment:套餐类型:0自定义套餐;1系统套餐;NOT NULL" json:"plan_type"`
	Model
}

func (ChargeCustomizePlans) TableName() string {
	return "charge_customize_plans"
}
