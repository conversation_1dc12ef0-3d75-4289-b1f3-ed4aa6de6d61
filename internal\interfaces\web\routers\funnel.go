package routers

import (
	"tmshopify/internal/interfaces/web/handlers"
	"tmshopify/internal/providers"

	"github.com/gin-gonic/gin"
)

// RegisterPaidConversionFunnelRoute 注册付费转化漏斗路由
func RegisterPaidConversionFunnelRoute(router *gin.RouterGroup, repos *providers.Repositories) {
	handler := handlers.NewPaidConversionFunnelHandler(repos.FunnelRepo)

	adminGroup := router.Group("/analytics")
	{
		adminGroup.POST("/paid_conversion_funnel", handler.GetFunnelSummary)
	}
}
