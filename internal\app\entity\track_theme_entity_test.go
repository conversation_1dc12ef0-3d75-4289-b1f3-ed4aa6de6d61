package entity

import (
	"encoding/json"
	"testing"

	"tmshopify/config"
)

func TestClassThemeUnmarshal(t *testing.T) {
	setting := "{\"google_translate\":true,\"general\":{\"width\":1200,\"font\":\"SF Pro Text\",\"estimated_show\":2,\"order_lookup\":true,\"progress_bar\":true,\"tracking_history\":true,\"detail_info\":true,\"featured_products\":true,\"border\":false,\"text_color\":\"#1a1d1e\",\"coupon_sell\":false,\"shipment_review\":true,\"primary_color\":\"#eb8129\",\"auto_language\":false},\"order_lookup\":{\"order\":true,\"number\":true,\"button_color\":\"#0e0606\",\"text_above\":\"\",\"text_below\":\"\",\"placeholder\":true,\"border_coder\":\"\",\"color\":\"#f7f7f7\",\"input_color\":\"#0a111d\",\"border\":true,\"border_color\":\"#150d0d\",\"order_number\":\"3\",\"merge_track\":false},\"progress_bar\":{\"date_format\":2,\"time_format\":0,\"color\":\"#962e2e\",\"show_shipped\":true,\"show_order_ready\":true,\"before_order_ready_status\":[{\"icon\":null,\"checked\":true,\"days\":1,\"description\":\"1\",\"name\":\"Packing\",\"cutsom_icon\":null}],\"before_shipped_status\":[{\"icon\":null,\"checked\":true,\"days\":5,\"description\":\"1\",\"name\":\"test\",\"custom_icon\":null},{\"icon\":null,\"checked\":true,\"days\":1,\"description\":\"1\",\"name\":\"test2\",\"cutsom_icon\":null}]},\"tracking_history\":{\"date_format\":2,\"time_format\":0,\"background_color\":\"#d5dee0\",\"color\":\"#7c2121\",\"map\":\"location\",\"timeline_color\":\"#334fb4\"},\"detail_info\":{\"courier\":{\"switch\":true,\"first_mile\":{\"switch\":true,\"icon\":true,\"to_courier_website\":true},\"last_mile\":{\"switch\":true,\"icon\":true,\"to_courier_website\":true}},\"number\":{\"switch\":true,\"first_mile\":true,\"last_mile\":true},\"line_items\":true,\"note\":true},\"featured_products\":{\"before_tracking\":{\"collection\":\"0\",\"selected\":0,\"open\":true,\"product_list\":[{\"product_id\":8154227802275,\"product_img\":\"https:\\/\\/cdn.shopify.com\\/s\\/files\\/1\\/0616\\/2258\\/3459\\/files\\/DCMPL_MM8X_2S_P_87_W3_A.jpg?v=1701849255\"}]},\"after_tracking\":{\"collection\":\"311817044131\",\"selected\":0,\"open\":false,\"position\":3,\"product_list\":[{\"product_id\":7784315879587,\"product_img\":\"https:\\/\\/cdn.shopify.com\\/s\\/files\\/1\\/0616\\/2258\\/3459\\/files\\/82BFC4C8-9F19-4e15-9CCB-296943353E01.png?v=1705978171\"},{\"product_id\":7784316043427,\"product_img\":\"https:\\/\\/cdn.shopify.com\\/s\\/files\\/1\\/0616\\/2258\\/3459\\/files\\/wallhaven-6okwmq.png?v=1705978193\"},{\"product_id\":8154227802275,\"product_img\":\"https:\\/\\/cdn.shopify.com\\/s\\/files\\/1\\/0616\\/2258\\/3459\\/files\\/DCMPL_MM8X_2S_P_87_W3_A.jpg?v=1701849255\"}]}},\"coupon_sell\":{\"display\":\"on_page\",\"main_title\":\"30% OFF Christmass\",\"description\":\"For every order higher than $150 + FREE SHIPPING\",\"coupon_code\":\"30OFFTEST\",\"button_text\":\"Get discount NOW!\",\"button_link\":\"https:\\/\\/tuyutian.myshopify.com\\/apps\\/trackingmore\",\"background_color\":\"white\"},\"shipment_review\":{\"star_color\":\"#ff1800\",\"lang\":\"fr\"},\"map\":{\"location\":false,\"destination\":true}}"
	var classicTheme config.ClassicTheme
	err := json.Unmarshal([]byte(setting), &classicTheme)
	if err != nil {
		t.Fatal("UnmarshalJSON error: ", err.Error())
	}
	t.Log(classicTheme)
}
