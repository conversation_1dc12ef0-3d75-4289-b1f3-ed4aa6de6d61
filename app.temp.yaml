app_conf:
  app_debug: true # 是否开启调试模式
  http_port: 10002
  http_monitor_port: 8090 # pprof性能监控和prometheus监控端口，这是通过http服务访问
  graceful_wait: 5s # 平滑退出等待时间，单位s
  log_level: info
  db:
    prefix:
  oss:
    bucket_name: upload-public-shenzhen
    app_id:
    presign_expires: 10 # 预签名URL的有效期
  auth:
    jwt_secret: "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"
    jwt_expire_day: 2
  shopify_api_key: "4eac2d5386e7278461cda60a1db83bbe"
  shopify_api_secret: "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"
  client_scope: "read_orders,read_all_orders,write_orders,write_fulfillments,read_script_tags,write_script_tags,read_shipping,read_products,read_customers,read_themes,write_assigned_fulfillment_orders,write_merchant_managed_fulfillment_orders,write_third_party_fulfillment_orders"

# redis配置
redis_conf:
  Address: "*************:6379" # host:port address.
  Password:
  MaxIdle: 150
  Active: 150
  IdleTimeout: 200s
  Prefix: "tmshopify_database_"

# mysql配置
db_conf:
  Dsn: "shopify:shopify123456@tcp(*************:3306)/shopify_test?charset=utf8mb4&parseTime=True&loc=Local"
  UsePool: true
  MaxIdleConn: 100  #设置连接池的空闲数大小
  MaxOpenConn: 600  #最大open connection个数
  MaxLifetime: 1800s
  ShowSql: true

aliyun_conf:
  oss:
    region: cn-shenzhen
    id: id
    secret: secret
    token:
    endpoint: endpoint

google_sheets:
  credentials:
    type: xxx
    project_id: xxx
    private_key_id: xxx
    private_key: xxx
    client_email: xxx
    client_id: xxx
    auth_uri: xxx
    token_uri: xxx
    auth_provider_cert_url: xxx
    client_cert_url: xxx
    universe_domain: xxx
