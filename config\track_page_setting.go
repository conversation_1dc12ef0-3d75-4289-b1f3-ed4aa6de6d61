package config

type Estimate struct {
	CalType        int      `json:"cal_type"`
	Switch         bool     `json:"switch"`
	DateFormat     int      `json:"date_format"`
	TimeFormat     int      `json:"time_format"`
	CutoffTime     string   `json:"cutoff_time"`
	ProcessingTime int      `json:"processing_time"`
	WorkDays       []string `json:"work_days"`
	EddRange       []struct {
		ZoneName string        `json:"zone_name"`
		Region   []interface{} `json:"region"`
		MinRange int           `json:"minRange"`
		MaxRange int           `json:"maxRange"`
	} `json:"edd_range"`
}

const EstimateCalFulfill = 1
const EstimateCalOrder = 0

var DefaultEstimate = Estimate{
	CalType:        EstimateCalOrder,
	Switch:         false,
	DateFormat:     0,
	TimeFormat:     0,
	CutoffTime:     "18:00",
	ProcessingTime: 1,
	WorkDays: []string{
		"Mon", "Tue", "Wed", "Thu", "Fri",
	},
	EddRange: []struct {
		ZoneName string        `json:"zone_name"`
		Region   []interface{} `json:"region"`
		MinRange int           `json:"minRange"`
		MaxRange int           `json:"maxRange"`
	}{
		{
			ZoneName: "All countries",
			Region:   []interface{}{"Africa", "Asia", "Central America", "Europe", "North America", "Oceania", "Other", "South America"},
			MinRange: 10,
			MaxRange: 20,
		},
	},
}
