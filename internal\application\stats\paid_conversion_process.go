package stats

import (
	"context"
	"time"

	"tmshopify/internal/domain/entity"
	"tmshopify/internal/domain/repo/charges"
	"tmshopify/internal/domain/repo/stats"
	"tmshopify/internal/domain/repo/users"
	"tmshopify/internal/providers"
	"tmshopify/store/models"

	"github.com/channelwill/athena/logger"
)

const (
	StatusNotDone = 0
	StatusDone    = 1
)

// ConversionProcessService 付费转化状态处理服务
type ConversionProcessService struct {
	funnelRepo stats.FunnelRepository
	userRepo   users.UserRepository
	chargeRepo charges.ChargeRepository
}

// NewConversionProcessService 创建付费转化状态处理服务
func NewConversionProcessService(repos *providers.Repositories) *ConversionProcessService {
	return &ConversionProcessService{
		funnelRepo: repos.FunnelRepo,
		userRepo:   repos.UserRepo,
		chargeRepo: repos.ChargeRepo,
	}
}

// ProcessUserConversionStates 处理用户付费转化状态机
func (s *ConversionProcessService) ProcessUserConversionStates(ctx context.Context) error {
	logger.Info(ctx, "开始处理用户付费转化状态机")

	// 第一步：数据同步 - 从users表同步用户到data_paid_conversion_logs表
	err := s.syncUsersToConversionLogs(ctx)
	if err != nil {
		logger.Error(ctx, "数据同步失败", map[string]interface{}{"error": err.Error()})
		return err
	}

	// 第二步：状态机处理 - 处理已同步的用户记录
	// 不用分页，因为数据量小
	// 不需要再筛选60天，因为第一步已经筛选过了
	users, err := s.funnelRepo.GetUsersWithin60DaysAndStatus(ctx, StatusNotDone)
	if err != nil {
		return err
	}

	logger.Info(ctx, "获取到待处理用户", map[string]interface{}{
		"count": len(users),
	})

		userIDs := make([]int64, 0)
		for _, user := range users {
			userIDs = append(userIDs, int64(user.UserID))
		}

		// 获取用户所有的充值记录(60天内，数量小，不用分页)
		userChargeRecords, err := s.chargeRepo.GetChargeRecordsByUserIDs(ctx, userIDs)
		if err != nil {
			return err
		}

		err = s.processUserState(ctx, userChargeRecords)
		if err != nil {
			logger.Error(ctx, "处理用户状态失败", map[string]interface{}{
				"error": err.Error(),
			})
			return err
		}

	logger.Info(ctx, "用户付费转化状态机处理完成")
	return nil
}

// syncUsersToConversionLogs 从users表同步用户到data_paid_conversion_logs表
func (s *ConversionProcessService) syncUsersToConversionLogs(ctx context.Context) error {
	logger.Info(ctx, "开始数据同步：从users表同步注册60天内的用户到data_paid_conversion_logs表")

	// 计算60天前的时间点
	sixtyDaysAgo := time.Now().AddDate(0, 0, -60)
	logger.Info(ctx, "同步范围", map[string]interface{}{
		"sixty_days_ago": sixtyDaysAgo.Format("2006-01-02 15:04:05"),
	})

	const batchSize = 1000
	cols := []string{"id", "created_at", "is_delete", "redact"}

	totalProcessed := 0
	cursor := 0

	for {
		// 获取当前批次
		userList, err := s.userRepo.GetActiveUsersCreatedAfter(ctx, cursor, batchSize, sixtyDaysAgo, cols...)
		if err != nil {
			return err
		}

		// 检查是否还有数据
		if len(userList) == 0 {
			break
		}

		// 处理当前批次
		batchResult := s.processBatch(ctx, userList)
		totalProcessed += batchResult.SuccessCount

		// 更新游标（无论成功失败都推进）
		cursor = s.updateCursor(userList)

		// 记录批次处理结果
		logger.Debug(ctx, "批次处理完成", map[string]interface{}{
			"batch_size":      len(userList),
			"success_count":   batchResult.SuccessCount,
			"failed_count":    batchResult.FailedCount,
			"current_cursor":  cursor,
			"total_processed": totalProcessed,
		})

		// 如果这是最后一批（数量少于batchSize）
		if len(userList) < batchSize {
			break
		}
	}

	logger.Info(ctx, "数据同步完成", map[string]interface{}{
		"total_processed": totalProcessed,
	})
	return nil
}

// BatchResult 批次处理结果
type BatchResult struct {
	SuccessCount int
	FailedCount  int
}

// updateCursor 更新游标到批次的最后一个用户ID
func (s *ConversionProcessService) updateCursor(users []*models.Users) int {
	if len(users) == 0 {
		return 0
	}
	return int(users[len(users)-1].ID)
}

// processBatchWithMetrics 处理批次
func (s *ConversionProcessService) processBatch(ctx context.Context, users []*models.Users) BatchResult {
	var result BatchResult
	var failedUsers []int64

	for _, user := range users {
		if err := s.syncSingleUser(ctx, user); err != nil {
			result.FailedCount++
			failedUsers = append(failedUsers, int64(user.ID))
			logger.Error(ctx, "同步单个用户失败", map[string]interface{}{
				"user_id": user.ID,
				"error":   err.Error(),
			})
		} else {
			result.SuccessCount++
		}
	}

	// 如果有失败的用户，记录汇总信息
	if len(failedUsers) > 0 {
		logger.Warn(ctx, "批次处理中有失败的用户", map[string]interface{}{
			"failed_user_ids": failedUsers,
			"failed_count":    len(failedUsers),
		})
	}

	return result
}

// syncSingleUser 同步单个用户数据
func (s *ConversionProcessService) syncSingleUser(ctx context.Context, user *models.Users) error {
	// 检查用户是否已经存在于data_paid_conversion_logs表中
	_, err := s.funnelRepo.GetPaidConversionLogByUserID(ctx, int64(user.ID))
	if err == nil {
		// 记录已存在，跳过
		return nil
	}

	// 如果是找不到记录的错误，说明需要创建新记录
	if err != nil {
		// 其他错误，返回
		return err
	}

	// 创建新的付费转换记录
	newLog := &entity.DataPaidConversionLog{
		UserID:      int64(user.ID),
		Status:      StatusNotDone,         // 初始状态为采集中
		TrialTime:   0,                     // 试用时间初始为0
		PaymentTime: 0,                     // 付费时间初始为0
		RenewalTime: 0,                     // 续费时间初始为0
		CreateTime:  user.CreatedAt.Unix(), // 使用用户创建时间作为记录创建时间
	}

	err = s.funnelRepo.CreatePaidConversionLog(ctx, newLog)
	if err != nil {
		return err
	}

	logger.Debug(ctx, "成功创建付费转换记录", map[string]interface{}{
		"user_id":     user.ID,
		"create_time": newLog.CreateTime,
	})

	return nil
}

// processUserState 处理单个用户的状态机流程
func (s *ConversionProcessService) processUserState(ctx context.Context, userChargeRecords []*entity.ChargeRecords) error {
	logger.Info(ctx, "开始处理用户状态...")

	// 按用户ID分组处理充值记录
	userRecordsMap := make(map[int64][]*entity.ChargeRecords)
	for _, record := range userChargeRecords {
		userRecordsMap[record.UserID] = append(userRecordsMap[record.UserID], record)
	}

	for userID, records := range userRecordsMap {
		err := s.processUserStateByUserID(ctx, userID, records)
		if err != nil {
			logger.Error(ctx, "处理用户状态失败", map[string]interface{}{
				"user_id": userID,
				"error":   err.Error(),
			})
			continue
		}
	}

	return nil
}

// processUserStateByUserID 处理单个用户的状态机流程
func (s *ConversionProcessService) processUserStateByUserID(ctx context.Context, userID int64, userChargeRecords []*entity.ChargeRecords) error {
	// 获取用户的付费转换记录
	userLog, err := s.funnelRepo.GetPaidConversionLogByUserID(ctx, userID)
	if err != nil {
		logger.Error(ctx, "获取用户付费转换记录失败", map[string]interface{}{
			"user_id": userID,
			"error":   err.Error(),
		})
		return err
	}

	// 1. 检查是否有新安装用户记录（status=0）
	hasNewInstall := false
	for _, record := range userChargeRecords {
		if record.Status == 0 {
			hasNewInstall = true
			break
		}
	}

	if !hasNewInstall {
		// 没有新安装记录，跳过这个用户
		return nil
	}

	// 2. 查找第一个免费试用记录（status=1且有免费试用天数）
	var trialRecord *entity.ChargeRecords
	for _, record := range userChargeRecords {
		if record.Status == 1 && record.FreeTrialDays > 0 {
			recordTime := record.ActivatedAt.Unix()
			if trialRecord == nil || recordTime < trialRecord.ActivatedAt.Unix() {
				trialRecord = record
			}
		}
	}

	// 如果有试用记录，更新试用时间
	if trialRecord != nil && userLog.TrialTime == 0 {
		userLog.TrialTime = trialRecord.ActivatedAt.Unix()
		logger.Info(ctx, "更新用户试用时间", map[string]interface{}{
			"user_id":    userID,
			"trial_time": time.Unix(trialRecord.ActivatedAt.Unix(), 0).Format("2006-01-02 15:04:05"),
		})
	}

	// 3. 查找第一个付费记录（试用期结束后的记录）
	var paymentRecord *entity.ChargeRecords
	currentTime := time.Now().Unix()
	for _, record := range userChargeRecords {
		if record.Status == 1 && record.FreeTrialDays > 0 {
			trialEndTime := record.ActivatedAt.Unix() + int64(record.FreeTrialDays*24*3600)
			if trialEndTime < currentTime {
				if paymentRecord == nil || record.ActivatedAt.Unix() < paymentRecord.ActivatedAt.Unix() {
					paymentRecord = record
				}
			}
		}
	}

	// 如果有付费记录，更新付费时间
	if paymentRecord != nil && userLog.PaymentTime == 0 {
		paymentTime := paymentRecord.ActivatedAt.Unix() + int64(paymentRecord.FreeTrialDays*24*3600)
		userLog.PaymentTime = paymentTime
		logger.Info(ctx, "更新用户付费时间", map[string]interface{}{
			"user_id":      userID,
			"payment_time": time.Unix(paymentTime, 0).Format("2006-01-02 15:04:05"),
		})
	}

	// 4. 查找次月续费记录（付费后30天的记录）
	var renewalRecord *entity.ChargeRecords
	if paymentRecord != nil {
		paymentTime := paymentRecord.ActivatedAt.Unix() + int64(paymentRecord.FreeTrialDays*24*3600)
		renewalTime := paymentTime + int64(30*24*3600)
		
		for _, record := range userChargeRecords {
			if record.Status == 1 && record.ActivatedAt.Unix() > renewalTime {
				if renewalRecord == nil || record.ActivatedAt.Unix() < renewalRecord.ActivatedAt.Unix() {
					renewalRecord = record
				}
			}
		}

		// 如果有续费记录，更新续费时间
		if renewalRecord != nil && userLog.RenewalTime == 0 {
			userLog.RenewalTime = renewalRecord.ActivatedAt.Unix()
			logger.Info(ctx, "更新用户续费时间", map[string]interface{}{
				"user_id":      userID,
				"renewal_time": time.Unix(renewalRecord.ActivatedAt.Unix(), 0).Format("2006-01-02 15:04:05"),
			})
		}
	}

	// 5. 记录 status = 1（处理完成）
	userLog.Status = StatusDone
	err = s.updateUser(ctx, userLog)
	if err != nil {
		logger.Error(ctx, "更新用户状态失败", map[string]interface{}{
			"user_id": userID,
			"error":   err.Error(),
		})
		return err
	}

	logger.Info(ctx, "用户状态处理完成", map[string]interface{}{
		"user_id":      userID,
		"trial_time":   userLog.TrialTime,
		"payment_time": userLog.PaymentTime,
		"renewal_time": userLog.RenewalTime,
	})

	return nil
}

// updateUser 更新用户记录
func (s *ConversionProcessService) updateUser(ctx context.Context, user *entity.DataPaidConversionLog) error {
	return s.funnelRepo.UpdatePaidConversionLog(ctx, user)
}
