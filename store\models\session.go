package models

import "time"

type Sessions struct {
	SessionId         string    `gorm:"column:session_id;type:varchar(191);NOT NULL" json:"session_id"`
	Shop              string    `gorm:"column:shop;type:varchar(255);NOT NULL" json:"shop"`
	IsOnline          int       `gorm:"column:is_online;type:tinyint(1);NOT NULL" json:"is_online"`
	State             string    `gorm:"column:state;type:varchar(255);NOT NULL" json:"state"`
	Scope             string    `gorm:"column:scope;type:varchar(255)" json:"scope"`
	AccessToken       string    `gorm:"column:access_token;type:varchar(255)" json:"access_token"`
	ExpiresAt         time.Time `gorm:"column:expires_at;type:datetime" json:"expires_at"`
	UserId            int64     `gorm:"column:user_id;type:bigint(20)" json:"user_id"`
	UserFirstName     string    `gorm:"column:user_first_name;type:varchar(255)" json:"user_first_name"`
	UserLastName      string    `gorm:"column:user_last_name;type:varchar(255)" json:"user_last_name"`
	UserEmail         string    `gorm:"column:user_email;type:varchar(255)" json:"user_email"`
	UserEmailVerified int       `gorm:"column:user_email_verified;type:tinyint(1)" json:"user_email_verified"`
	AccountOwner      int       `gorm:"column:account_owner;type:tinyint(1)" json:"account_owner"`
	Locale            string    `gorm:"column:locale;type:varchar(255)" json:"locale"`
	Collaborator      int       `gorm:"column:collaborator;type:tinyint(1)" json:"collaborator"`
	Uid               int64     `gorm:"column:uid;type:bigint(20);comment:关联用户系统Id" json:"uid"`
	Model
}

func (Sessions) TableName() string {
	return "sessions"
}
