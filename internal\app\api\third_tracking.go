package api

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"time"
	"tmshopify/config"
	"tmshopify/internal/app/request"
	"tmshopify/internal/app/service"
	"tmshopify/pkg/domain"
	"tmshopify/pkg/utils/helper"
	"tmshopify/pkg/utils/validator"
	"tmshopify/server/response"
	"tmshopify/store/database"
	"tmshopify/store/repo/impl"
)

type ThirdTracking struct {
}

func (t *ThirdTracking) ThirdpartInstall(ctx *gin.Context) {
	result := validator.Validate(ctx, &request.WilldeskInstall{})
	if len(result) > 0 {
		response.UnprocessableEntity(ctx, result)
		return
	}
	shop := ctx.Query("shop")
	install := false
	key := config.CacheKey.ShopInstallCache + shop
	if cache, _ := database.RS.Get(key); string(cache) == "" {
		data, err := service.NewThirdTracking().GetInstallData(shop)
		install = data.ID != 0
		if install {
			_ = database.RS.SetWithExpire(key, "1", time.Hour*3)
		} else {
			_ = database.RS.SetWithExpire(key, "2", time.Hour*3)
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.Success(ctx, &response.SuccessResponse{
				Data: gin.H{
					"install": false,
				},
			})
			return
		}
		if err != nil {
			response.Success(ctx, &response.SuccessResponse{
				Data: gin.H{
					"install": false,
				},
			})
			return
		}
	} else {
		if string(cache) == "1" {
			install = true
		} else {
			install = false
		}
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: gin.H{
			"install": install,
		},
	})
}

type trackingParam struct {
	Email     string `json:"email" form:"email"`
	Shop      string `json:"shop" form:"shop"`
	OrderName string `json:"order_name" form:"order_name"`
	Tracking  string `json:"tracking" form:"tracking"`
}

func (t *ThirdTracking) ThirdpartTracking(ctx *gin.Context) {
	var params trackingParam
	if err := ctx.ShouldBind(&params); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	if len(params.Shop) <= 0 {
		response.BadRequest(ctx, "Error shop")
		return
	}
	var data service.Data
	var err error
	if len(params.Tracking) > 0 {
		isTrue := helper.TrackNumberRule(params.Tracking)
		if isTrue != true {
			response.BadRequest(ctx, "tracking format is incorrect")
			return
		}
		data, err = service.NewThirdTracking().GetTrackingData(params.Shop, params.Tracking)
	} else {
		if len(params.OrderName) <= 0 {
			response.BadRequest(ctx, "Error order name")
			return
		}
		data, err = service.NewThirdTracking().GetOrderTrackingData(params.Shop, params.OrderName, params.Email)
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		response.NotFound(ctx, "order tracking not found")
		return
	}
	if err != nil {
		response.Fail(ctx, err)
		return
	}
	if data.TrackingNumber == "" {
		response.NotFound(ctx, "tracking does not exist")
		return
	}

	response.Success(ctx, &response.SuccessResponse{
		Data: gin.H{
			"tracking":        data.Tracking,
			"tracking_number": data.TrackingNumber,
			"order_name":      data.OrderName,
		},
	})
}

func (t *ThirdTracking) GorgiasTracking(ctx *gin.Context) {
	token := ctx.GetHeader("tmshopify-api-key")
	email := ctx.Query("email")
	if email == "" || token == "" {
		response.BadRequest(ctx, "Please pass correct parameters")
		return
	}
	user, err := service.NewUserService().GetUserByToken(token)
	if user.ID == 0 || err != nil {
		response.BadRequest(ctx, "Store does not exist")
		return
	}
	var userRepo *impl.User
	permission := userRepo.GetPermissionByUser(user)
	if !permission.VerifyPermission(domain.GorgiasIntegration) {
		response.NotFound(ctx, "No permission")
		return
	}
	data := service.NewThirdTracking().GetGorgiasTracking(user, email)

	response.Success(ctx, &response.SuccessResponse{
		Data: gin.H{
			"response_time": time.Now().UTC().Format(time.RFC3339),
			"trackings":     data,
		},
	})
}
