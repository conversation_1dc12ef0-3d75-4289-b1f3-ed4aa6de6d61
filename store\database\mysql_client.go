package database

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm/logger"
	"time"
	"tmshopify/config"
	"tmshopify/pkg/utils/helper"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var DB *gorm.DB

type SqlWriter struct {
}

func (m SqlWriter) Printf(format string, v ...interface{}) {
	logstash := fmt.Sprintf(format, v...)
	logrus.Warn(logstash)
}

func InitDB() (*gorm.DB, error) {
	conf := config.Get()
	log := &SqlWriter{}
	var slowTime = 10 * time.Second
	if conf.SlowSql > 0 {
		slowTime = time.Duration(conf.SlowSql) * time.Second
	}
	if helper.IsLocal() {
		slowTime = time.Millisecond
	}
	db, err := gorm.Open(mysql.Open(conf.DSN), &gorm.Config{
		DisableForeignKeyConstraintWhenMigrating: true,
		Logger: logger.New(log, logger.Config{
			SlowThreshold: slowTime,
			//设置日志级别，只有Warn以上才会打印sql
			LogLevel: logger.Warn,
		}),
	})

	if err == nil {
		if conf.SQLDebug {
			DB = db.Debug()
		} else {
			DB = db
		}

		return db, err
	}
	return nil, err
}
