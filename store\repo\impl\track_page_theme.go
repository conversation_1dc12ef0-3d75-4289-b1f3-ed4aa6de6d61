package impl

import (
	"encoding/json"
	"tmshopify/config"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type TrackPageTheme struct {
	Helper
}

func (t *TrackPageTheme) FirstByThemeId(themeId int) (models.TrackPageThemes, error) {
	var trackPageThemes models.TrackPageThemes
	result := database.DB.Model(&trackPageThemes).Select("settings,translation").First(&trackPageThemes, themeId)
	return trackPageThemes, result.Error
}

func (t *TrackPageTheme) CreateByUserIdAndCode(userId int, themeCode models.Theme) models.TrackPageThemes {
	var m models.TrackPageThemes

	result := database.DB.Model(models.TrackPageThemes{}).Where("user_id = ?", userId).First(&m)

	if result.Error != nil {
		var translation []byte
		var defaultSettings []byte
		switch themeCode {
		case models.ThemeModern:
			translation, _ = json.Marshal(config.ModernTranslation["en"])
			defaultConfig := config.DefaultModernTheme
			defaultSettings, _ = json.Marshal(defaultConfig)
		default:
			translation, _ = json.Marshal(config.ClassicTranslation["en"])
			defaultConfig := config.DefaultClassicTheme
			defaultSettings, _ = json.Marshal(defaultConfig)
		}
		m = models.TrackPageThemes{
			UserId:      uint(userId),
			Settings:    string(defaultSettings),
			ThemeCode:   themeCode,
			Translation: string(translation),
			CustomStyle: "",
			TopHtml:     "",
			BottomHtml:  "",
		}
		result = database.DB.Model(models.TrackPageThemes{}).Create(&m)

	}
	return m
}

func (t *TrackPageTheme) GetClassicTheme(userId int) (models.TrackPageThemes, error) {
	var m models.TrackPageThemes
	result := database.DB.Model(models.TrackPageThemes{}).Where("user_id = ? and theme_code = ?", userId, models.ThemeClassic).First(&m)
	return m, result.Error
}
