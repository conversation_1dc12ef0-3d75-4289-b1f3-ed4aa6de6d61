package impl

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"tmshopify/pkg/utils/paginate"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type Courier struct {
	Helper
}

type ExpressInfo struct {
	CompanyCode    string `json:"company_code"`
	CompanyName    string `json:"company_name"`
	IsVerify       int    `json:"is_verify"`
	IsSupport      int    `json:"is_support"`
	CompanySort    string `json:"company_sort"`
	CompanyBigArea string `json:"company_big_area"`
}

// 获取快递简码信息
func (c *Courier) GetCourierAndName(lang string) map[string]string {
	// 获取快递信息
	redisKey := "courier_array"
	if lang != "en" {
		redisKey += "_" + lang
	}

	// 从redis获取快递简码数据
	courierArrayJSON := getCourierFromRedis(redisKey)
	courierArray := make(map[string]string)

	if courierArrayJSON != "" && courierArrayJSON != "[]" {
		// 存在缓存，解码JSON
		err := json.Unmarshal([]byte(courierArrayJSON), &courierArray)
		if err != nil {
			fmt.Println("Error decoding JSON:", err)
		}
	} else {
		// 不存在缓存，则获取新的快递简码信息
		where := map[string]interface{}{
			"lang":       "en",
			"is_verify":  1,
			"is_support": 1,
		}
		expressArr := getTrackingCompanyData(where, 0)
		for _, value := range expressArr {
			courierArray[value.CompanyCode] = value.CompanyName
		}
		dataJSON, err := json.Marshal(courierArray)
		if err != nil {
			fmt.Println("Error encoding JSON:", err)
		}
		// 向redis设置快递简码数据
		setCourierToRedis(redisKey, string(dataJSON))
		// 将 courierArray 赋值给 courierArray，因为 JSON 解码后 map[string]string 会变成 map[string]interface{}
		courierArray = courierArray
	}

	return courierArray
}

// 从redis获取快递简码数据
func getCourierFromRedis(redisKey string) string {
	get, err := database.RS.Get(redisKey)
	if err != nil {
		return ""
	}
	return string(get)
}

// 向redis设置快递简码数据
func setCourierToRedis(redisKey string, data string) {
	expireTime := 7 * 24 * time.Hour
	err := database.RS.SetWithExpire(redisKey, data, expireTime)
	if err != nil {
		return
	}
}

// 获取物流商数据
func getTrackingCompanyData(where map[string]interface{}, isArea int) []models.Couriers {
	var data []models.Couriers
	// 判断是否存在缓存
	redisKey := "TrackingCompanyData_en"
	str := getTrackingCompanyFromRedis(redisKey)
	if str != "" {
		var dataArr []models.Couriers
		err := json.Unmarshal([]byte(str), &dataArr)
		if err == nil && len(dataArr) > 0 {
			data = dataArr
		} else {
			// 数据结构异常，重新查询
			data, _ = Get("en")
			setTrackingCompanyToRedis(redisKey, data)
		}
	} else {
		// 数据异常，重新查询
		data, _ = Get("en")
		setTrackingCompanyToRedis(redisKey, data)
	}

	// 数据重组
	var dataCheck []models.Couriers
	isVerify := where["is_verify"]
	isSupport := where["is_support"]
	expressType := where["company_sort"]

	for _, val := range data {
		// 是否通过了验证
		if val.CompanyCode == "szyn" {
			val.IsVerify = 1
		}

		if isVerify != 0 && val.IsVerify != isVerify {
			continue
		}

		// 是否支持查询
		if isSupport != 0 && val.IsSupport != isSupport {
			continue
		}

		// 是否是全球邮政
		if expressType != "" && val.CompanySort != expressType {
			continue
		}

		// 是否需要划分地域
		if isArea == 0 {
			dataCheck = append(dataCheck, val)
		}

		if isArea != 0 && val.CompanyBigArea != 0 {
			dataCheck = append(dataCheck, val)
		}
	}

	if len(dataCheck) > 0 {
		data = dataCheck
	}

	return data
}

// 从redis获取物流商数据
func getTrackingCompanyFromRedis(redisKey string) string {
	get, err := database.RS.Get(redisKey)
	if err != nil {
		return ""
	}
	return string(get)
}

// 向redis设置物流商数据
func setTrackingCompanyToRedis(redisKey string, data []models.Couriers) {
	result, err := json.Marshal(data)
	if err != nil {
		fmt.Println("Error encoding JSON:", err)
		return
	}
	err = database.RS.Set(redisKey, string(result))
	if err != nil {
		return
	}
}

func Get(lang string) ([]models.Couriers, error) {
	var items []models.Couriers
	result := database.DB.Where("lang = ?", lang).Find(&items)
	return items, result.Error
}

func (c *Courier) GetCompanyInfoByCodeAndLang(code, lang string) models.Couriers {
	var courier models.Couriers
	if len(code) == 0 {
		return courier
	}
	if lang == "" {
		lang = "en"
	}
	redisKey := fmt.Sprintf("company_info_%s_%s", code, lang)

	desRulerStr, err := database.RS.Get(redisKey)

	if len(desRulerStr) != 0 && err == nil {
		err = json.Unmarshal(desRulerStr, &courier)
		if err == nil {
			return courier
		}
	}
	where := map[string]interface{}{
		"lang":         lang,
		"company_code": code,
		"is_verify":    1,
		"is_support":   1,
	}
	result := database.DB.Model(courier).Where(where).First(&courier)

	if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		jsonCourier, _ := json.Marshal(courier)
		err = database.RS.SetWithExpire(redisKey, string(jsonCourier), 90*24*time.Hour)
		if err != nil {
			_ = database.RS.SetWithExpire(redisKey, string(jsonCourier), 90*24*time.Hour)
		}
	}
	return courier
}

func (c *Courier) SearchCourierByNameField(name string) []models.Couriers {
	var couriers []models.Couriers
	database.DB.Model(&models.Couriers{}).Where("company_name like ? or company_code like ?", "%"+name+"%", "%"+name+"%").Limit(10).Find(&couriers)
	return couriers
}

func (c *Courier) Count() int64 {
	var count int64
	database.DB.Model(&models.Couriers{}).Count(&count)
	return count
}

func (c *Courier) Paginate(paginateParam *paginate.Param) ([]models.Couriers, int64, error) {
	var items []models.Couriers
	total := c.Count()

	result := database.DB.Scopes(paginate.ORMScope(paginateParam)).Order("id desc").Find(&items)

	return items, total, result.Error
}
