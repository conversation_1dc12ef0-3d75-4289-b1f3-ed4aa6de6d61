package database

import (
	"errors"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"
	"tmshopify/config"

	"github.com/gomodule/redigo/redis"
)

type Redis struct {
	prefix string
	Pool   *redis.Pool
}

var RS *Redis

func InitRedis() {
	Pool := &redis.Pool{
		Dial: func() (conn redis.Conn, e error) {
			c, err := redis.Dial("tcp", config.Get().RedisHost)
			if err != nil {
				return nil, err
			}

			if config.Get().RedisPassword != "" {
				if _, err := c.Do("AUTH", config.Get().RedisPassword); err != nil {
					c.Close()
					return nil, err
				}
			}
			return c, err
		},
		TestOnBorrow: func(c redis.Conn, t time.Time) error {
			_, err := c.Do("PING")
			return err
		},
		MaxIdle:         config.Get().RedisMaxIdle,
		MaxActive:       config.Get().RedisActive,
		IdleTimeout:     config.Get().RedisIdleTimeout,
		Wait:            false,
		MaxConnLifetime: 0,
	}
	prefix := config.Get().RedisPrefix
	RS = &Redis{
		prefix: prefix,
		Pool:   Pool,
	}
	cleanupHook()
}

func cleanupHook() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)
	signal.Notify(c, syscall.SIGTERM)
	signal.Notify(c, syscall.SIGKILL)
	go func() {
		<-c
		RS.Pool.Close()
		os.Exit(0)
	}()
}

func (r *Redis) Ping() error {
	conn := r.Pool.Get()
	defer conn.Close()

	_, err := redis.String(conn.Do("PING"))
	if err != nil {
		return fmt.Errorf("cannot 'PING' db: %v", err)
	}
	return nil
}

func (r *Redis) Get(key string) ([]byte, error) {
	conn := r.Pool.Get()
	defer conn.Close()
	var data []byte

	key = r.prefix + key
	data, err := redis.Bytes(conn.Do("GET", key))

	if err != nil && !errors.Is(err, redis.ErrNil) {
		return data, fmt.Errorf("error getting key %s: %v", key, err)
	}
	return data, err
}

func (r *Redis) Set(key string, value string) error {
	conn := r.Pool.Get()
	defer conn.Close()

	key = r.prefix + key
	_, err := conn.Do("SET", key, []byte(value))
	if err != nil {
		v := value
		if len(v) > 15 {
			v = v[0:12] + "..."
		}
		return fmt.Errorf("error setting key %s to %s: %v", key, v, err)
	}
	return err
}

func (r *Redis) Exists(key string) (bool, error) {

	conn := r.Pool.Get()
	defer conn.Close()
	key = r.prefix + key
	ok, err := redis.Bool(conn.Do("EXISTS", key))
	if err != nil {
		return ok, fmt.Errorf("error checking if key %s exists: %v", key, err)
	}
	return ok, err
}

func (r *Redis) Delete(key string) error {

	conn := r.Pool.Get()
	defer conn.Close()
	key = r.prefix + key
	_, err := conn.Do("DEL", key)
	return err
}

func (r *Redis) Incr(counterKey string) (int, error) {
	conn := r.Pool.Get()
	defer conn.Close()

	return redis.Int(conn.Do("INCR", counterKey))
}

// SetNotExist 设置数据到redis中（string）
func (r *Redis) SetNotExist(key string, value string, expireSecond int) bool {
	conn := r.Pool.Get()
	defer conn.Close()
	key = r.prefix + key
	val, err := conn.Do("SET", key, []byte(value), "EX", expireSecond, "NX")
	if err != nil || val == nil {
		return false
	}
	return true
}

// SetWithExpire 设置数据到redis中（string）
func (r *Redis) SetWithExpire(key string, value string, expireSecond time.Duration) error {
	conn := r.Pool.Get()
	defer conn.Close()
	key = r.prefix + key
	_, err := conn.Do("Set", key, []byte(value), "EX", expireSecond.Seconds())
	if err != nil {
		v := value
		if len(v) > 15 {
			v = v[0:12] + "..."
		}
		return fmt.Errorf("error setting key %s to %s: %v", key, v, err)
	}
	return err
}

// HSet 设置数据到redis中（hash）
func (r *Redis) HSet(key string, field string, value string) error {
	conn := r.Pool.Get()
	defer conn.Close()
	key = r.prefix + key
	_, err := conn.Do("HSet", key, field, value)
	return err
}

// HGet 获取hash数据（hash）
func (r *Redis) HGet(key string, field string) (string, error) {
	conn := r.Pool.Get()
	defer conn.Close()
	key = r.prefix + key
	val, err := conn.Do("HGet", key, field)
	if err != nil || val == nil {
		return "", err
	}
	return string(val.([]byte)), nil
}

func (r *Redis) SAdd(key string, value string) (bool, error) {
	conn := r.Pool.Get()
	defer conn.Close()
	key = r.prefix + key
	return redis.Bool(conn.Do("SADD", key, value))
}

func (r *Redis) SPop(key string) (string, error) {
	conn := r.Pool.Get()
	defer conn.Close()
	key = r.prefix + key
	val, err := conn.Do("SPOP", key)
	if err != nil {
		return "", err
	}
	return string(val.([]byte)), nil
}

// SetExpire 设置key过期时间
func (r *Redis) SetExpire(key string, expireSecond time.Duration) (bool, error) {
	conn := r.Pool.Get()
	defer conn.Close()
	key = r.prefix + key
	return redis.Bool(conn.Do("EXPIRE", key, expireSecond.Seconds()))
}
