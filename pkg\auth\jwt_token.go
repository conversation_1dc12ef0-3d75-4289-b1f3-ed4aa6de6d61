package auth

import (
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"

	"tmshopify/config"
)

type JwtCustomClaims struct {
	ID    int    `json:"id,omitempty"`
	Guard string `json:"guard,omitempty"`
	jwt.RegisteredClaims
}

type Token struct {
	Token     string `json:"token"`
	ExpiresAt int64  `json:"expires_at"`
}

type JwtToken struct {
}

func New() *JwtToken {
	return &JwtToken{}
}

func (j *JwtToken) CreateUserToken(ID int, guard string) (*Token, error) {
	expiresAt := jwt.NewNumericDate(time.Now().Add(24 * time.Hour * time.Duration(config.Get().JwtExpireDay)))
	claims := &JwtCustomClaims{
		ID,
		guard,
		jwt.RegisteredClaims{
			ExpiresAt: expiresAt,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(config.Get().JwtSecret))

	return &Token{Token: tokenString, ExpiresAt: expiresAt.Unix()}, err
}

func (j *JwtToken) CreateUserTokenWithExpire(ID int, guard string, expireTime time.Duration) (*Token, error) {
	expiresAt := jwt.NewNumericDate(time.Now().Add(expireTime))
	claims := &JwtCustomClaims{
		ID,
		guard,
		jwt.RegisteredClaims{
			ExpiresAt: expiresAt,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(config.Get().JwtSecret))

	return &Token{Token: tokenString, ExpiresAt: expiresAt.Unix()}, err
}

func (j *JwtToken) ParseToken(token string) (*JwtCustomClaims, error) {
	tokenClaims, err := jwt.ParseWithClaims(token, &JwtCustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(config.Get().JwtSecret), nil
	})
	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*JwtCustomClaims); ok && tokenClaims.Valid {
			return claims, nil
		}
	}
	return nil, err
}

func (j *JwtToken) JwtClaims(c *gin.Context) (*JwtCustomClaims, error) {
	authHeader := c.GetHeader("Authorization")

	// 安全地截取Bearer token，处理可能的多个空格
	if len(authHeader) == 0 {
		return nil, fmt.Errorf("missing authorization header")
	}

	// 检查是否以Bearer开头（忽略大小写）
	const bearerPrefix = "bearer"
	authLower := strings.ToLower(authHeader)
	if !strings.HasPrefix(authLower, bearerPrefix) {
		return nil, fmt.Errorf("invalid authorization header format, must start with Bearer")
	}

	// 截取Bearer后面的部分并去除所有空白字符
	token := strings.TrimSpace(authHeader[len(bearerPrefix):])
	if token == "" {
		return nil, fmt.Errorf("empty token")
	}

	claims, err := j.ParseToken(token)
	return claims, err
}

func (j *JwtToken) JwtUserId(c *gin.Context) int {
	claims, _ := j.JwtClaims(c)
	return claims.ID
}

func (j *JwtToken) JwtUserGuard(c *gin.Context) string {
	claims, _ := j.JwtClaims(c)
	return claims.Guard
}

type JwtShopify struct {
	Iss  string `json:"iss"`
	Sub  string `json:"sub"`
	Aud  string `json:"aud"`
	Jti  string `json:"jti"`
	Prv  string `json:"prv"`
	Dest string `json:"dest,omitempty"`
	Role int    `json:"role,omitempty"`
	jwt.RegisteredClaims
}

func (j *JwtToken) ParseShopifyToken(token string, jwtSecret string) (*JwtShopify, error) {
	tokenClaims, err := jwt.ParseWithClaims(token, &JwtShopify{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(jwtSecret), nil
	}, jwt.WithLeeway(600*time.Second))
	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*JwtShopify); ok && tokenClaims.Valid {
			return claims, nil
		}
	}
	return nil, err
}

func (j *JwtToken) JwtShopifyClaims(c *gin.Context, jwtSecret string) (*JwtShopify, string, error) {
	authHeader := c.GetHeader("Authorization")

	// 安全地截取Bearer token，处理可能的多个空格
	if len(authHeader) == 0 {
		return nil, "", fmt.Errorf("missing authorization header")
	}

	const bearerPrefix = "Bearer"
	if !strings.HasPrefix(authHeader, bearerPrefix) {
		return nil, "", fmt.Errorf("invalid authorization header format, must start with Bearer")
	}

	// 截取Bearer后面的部分并去除所有空白字符
	token := strings.TrimSpace(authHeader[len(bearerPrefix):])
	if token == "" {
		return nil, "", fmt.Errorf("empty token")
	}

	claims, err := j.ParseShopifyToken(token, jwtSecret)
	return claims, token, err
}
