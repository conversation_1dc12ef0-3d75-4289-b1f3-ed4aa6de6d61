package impl

import (
	"github.com/dromara/carbon/v2"

	"tmshopify/store/database"
	"tmshopify/store/models"
)

type PromotionClickLog struct {
	Helper
}

func (p *PromotionClickLog) GetPromotionClickLogByDate(startTime, endTime *carbon.Carbon) ([]models.DataPromotionClickLogs, error) {
	var clickLogs []models.DataPromotionClickLogs
	result := database.DB.Where("create_date BETWEEN ? AND ?", startTime.ToDateString(), endTime.ToDateString()).Select("app_name, SUM(clicks) as clicks, source").Group("app_name, source").Order("source").Find(&clickLogs)
	return clickLogs, result.Error
}
