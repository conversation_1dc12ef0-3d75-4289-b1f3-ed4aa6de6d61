package auth

import "time"

type Users struct {
	ID              int64     `json:"id" gorm:"column:id"`                           // 主键ID
	Name            string    `json:"name" gorm:"column:name"`                       // 用户名
	Token           string    `json:"token" gorm:"column:token"`                     // token
	Vendor          string    `json:"vendor" gorm:"column:vendor"`                   // 店铺拥有者
	StoreName       string    `json:"store_name" gorm:"column:store_name"`           // shopify domain
	StoreRealName   string    `json:"store_real_name" gorm:"column:store_real_name"` // 店铺真实域名
	Phone           string    `json:"phone" gorm:"column:phone"`                     // 手机号
	Email           string    `json:"email" gorm:"column:email"`                     // 邮箱
	Plans           int64     `json:"plans" gorm:"column:plans"`                     // 套餐等级
	PlanID          int64     `json:"plan_id" gorm:"column:plan_id"`                 // 套餐ID
	Total           int64     `json:"total" gorm:"column:total"`                     // 用户套餐总量
	Consume         int64     `json:"consume" gorm:"column:consume"`                 // 用户套餐使用
	OldTotal        int64     `json:"old_total" gorm:"column:old_total"`             // 用户旧套餐总量
	OldConsume      int64     `json:"old_consume" gorm:"column:old_consume"`         // 用户旧套餐使用
	FreeOrderUse    int64     `json:"free_order_use" gorm:"column:free_order_use"`
	TimeZone        string    `json:"time_zone" gorm:"column:time_zone"`
	Country         string    `json:"country" gorm:"column:country"`
	Province        string    `json:"province" gorm:"column:province"`
	City            string    `json:"city" gorm:"column:city"`
	Address         string    `json:"address" gorm:"column:address"`
	Zip             string    `json:"zip" gorm:"column:zip"`
	Longitude       string    `json:"longitude" gorm:"column:longitude"`
	Latitude        string    `json:"latitude" gorm:"column:latitude"`
	PrimaryLocale   string    `json:"primary_locale" gorm:"column:primary_locale"`
	Currency        string    `json:"currency" gorm:"column:currency"`
	ShopifyPlanName string    `json:"shopify_plan_name" gorm:"column:shopify_plan_name"`
	ShopCreateTime  time.Time `json:"shop_create_time" gorm:"column:shop_create_time"`
	Redact          int64     `json:"redact" gorm:"column:redact"` // 是否删除了
	AutoSyncOn      int64     `json:"auto_sync_on" gorm:"column:auto_sync_on"`
	Rating          int64     `json:"rating" gorm:"column:rating"` // 用户shopify评论星级(0未评论)
	IsFirstSync     int64     `json:"is_first_sync" gorm:"column:is_first_sync"`
	IsTokenError    int64     `json:"is_token_error" gorm:"column:is_token_error"`
	SyncDay         int64     `json:"sync_day" gorm:"column:sync_day"`               // 单号同步天数：1、7、30、60
	IsActive        int64     `json:"is_active" gorm:"column:is_active"`             // 是否活跃用户；0，否；1，是
	IsDelete        int64     `json:"is_delete" gorm:"column:is_delete"`             // 卸载店铺
	LastLoginTime   time.Time `json:"last_login_time" gorm:"column:last_login_time"` // 最后登录时间
	EmailVerifiedAt time.Time `json:"email_verified_at" gorm:"column:email_verified_at"`
	Password        string    `json:"password" gorm:"column:password"`
	CreatedAt       time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt       time.Time `json:"updated_at" gorm:"column:updated_at"`
	OldID           int64     `json:"old_id" gorm:"column:old_id"` // 旧用户体系id
}

func (u Users) TableName() string {
	return "users"
}
