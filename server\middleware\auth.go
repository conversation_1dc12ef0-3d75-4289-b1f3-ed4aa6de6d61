package middleware

import (
	"fmt"
	"net/http"
	"tmshopify/server/response"
	"tmshopify/store/database"
	"tmshopify/store/models"

	"github.com/gin-gonic/gin"
)

func Auth() gin.HandlerFunc {
	return func(c *gin.Context) {
		UserId := c.MustGet("UserId").(int)
		var user models.AdminUsers
		if err := database.DB.Preload("Roles").Where("id=?", UserId).First(&user).Error; err != nil {
			response.Fail(c, &response.FailResponse{
				Code:    http.StatusUnauthorized,
				Message: fmt.Sprintf("用户不存在：%d", UserId),
			})
			c.Abort()
		}
		c.Set("User", user)
		c.Next()
	}
}
