package impl

import (
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type AdminUser struct {
	Helper
}

func (a *AdminUser) Update(id string, fields map[string]interface{}) error {
	return a.UpdateByMap(&models.AdminUsers{}, id, fields)
}
func (a *AdminUser) FirstById(id int64) (*models.AdminUsers, error) {
	var m *models.AdminUsers

	result := database.DB.First(&m, id)

	return m, result.Error
}
