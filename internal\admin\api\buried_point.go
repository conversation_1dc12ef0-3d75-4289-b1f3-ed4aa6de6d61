package api

import (
	"github.com/gin-gonic/gin"
	"tmshopify/internal/admin/service"
	"tmshopify/server/response"
)

type BuriedPoint struct {
}

func (b *BuriedPoint) BuriedPointLog(ctx *gin.Context) {
	data, err := service.NewBuriedPoint().GetBuriedPointStatistic()
	if err != nil {
		response.Fail(ctx, err)
		return
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: gin.H{
			"items": data,
		},
	})
}
