package models

type TrackPageSettings struct {
	TrackLink             string          `gorm:"column:track_link;type:varchar(100);default:apps/trackingmore;comment:查询链接;NOT NULL" json:"track_link"`
	UserId                int             `gorm:"column:user_id;type:int(11);comment:用户;NOT NULL" json:"user_id"`
	ThemeId               int             `gorm:"column:theme_id;type:int(10) unsigned;comment:当前主题id;NOT NULL" json:"theme_id"`
	EstimatedDeliveryTime string          `gorm:"column:estimated_delivery_time;type:text;comment:Estimated Setting cal_type 0 Order created time 1 Order fulfilled time;NOT NULL" json:"estimated_delivery_time"`
	SeoTitle              string          `gorm:"column:seo_title;type:varchar(255);NOT NULL" json:"seo_title"`
	SeoDesc               string          `gorm:"column:seo_desc;type:varchar(255);NOT NULL" json:"seo_desc"`
	HideSetting           string          `gorm:"column:hide_setting;type:mediumtext;comment:json 数组 [{ origin: test,hide_type:隐藏0或替换1,replace_type:字符替换0或整行1,replace:,sort:0}]" json:"hide_setting"`
	ShowCopyRight         int             `gorm:"column:show_copy_right;type:tinyint(4);default:1;comment:是否展示tm 宣传;NOT NULL" json:"show_copy_right"`
	IsNoJq                int             `gorm:"column:is_no_jq;type:tinyint(4);default:0;comment:是否引入jq" json:"is_no_jq"`
	ActiveTheme           TrackPageThemes `gorm:"foreignKey:ThemeId" json:"active_theme"`
	User                  Users           `gorm:"foreignKey:UserId" json:"user"`
	Model
}

func (TrackPageSettings) TableName() string {
	return "track_page_settings"
}
