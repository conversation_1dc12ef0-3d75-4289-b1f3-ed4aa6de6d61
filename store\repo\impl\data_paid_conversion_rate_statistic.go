package impl

import (
	"time"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type PaidConversionRateStatistic struct {
	Helper
}

func (p *PaidConversionRateStatistic) GetSubscriberConversionRate(startTime time.Time, endTime time.Time) ([]struct {
	Date            string
	MonthNewPaid    int
	MonthNewInstall int
}, error) {
	var column []struct {
		Date            string
		MonthNewPaid    int
		MonthNewInstall int
	}
	results := database.DB.Model(&models.PaidConversionRateStatistics{}).
		Select("date_format(create_date,\"%Y-%m-%d\") as Date, month_new_paid as MonthNewPaid, month_new_install as MonthNewInstall").
		Where("create_date between ? and ?", startTime, endTime).
		Order("create_date").
		Scan(&column)

	return column, results.Error
}
