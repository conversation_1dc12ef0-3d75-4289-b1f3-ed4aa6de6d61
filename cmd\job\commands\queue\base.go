package queue

import (
	"github.com/redis/go-redis/v9"
	"google.golang.org/api/sheets/v4"
	"gorm.io/gorm"

	"tmshopify/internal/application/users"
	uc "tmshopify/internal/infras/cache/users"
	"tmshopify/internal/infras/google"
	"tmshopify/internal/infras/persistence/orders"
	up "tmshopify/internal/infras/persistence/users"
	"tmshopify/store/repo/impl"
)

func initUserService(db *gorm.DB, redisClient redis.UniversalClient, service *sheets.Service) *users.UserService {
	// repository
	userRepo := up.NewUserRepository(db)
	userCacheRepo := uc.NewUserCacheRepository(redisClient)
	ordersRepo := orders.NewOrderRepository(db)
	googleRepo := google.NewSheetsRepository(redisClient, service)
	userRepository := &impl.User{}

	return users.NewUserService(userRepo, userCacheRepo, ordersRepo, googleRepo, userRepository)
}
