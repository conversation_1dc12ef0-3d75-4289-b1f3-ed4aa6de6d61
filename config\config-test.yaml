addr: :10001
app_addr: :10002
webhook_addr: :10003
gin_mode: "debug"
#gin_mode: "release"
app_env: "local"
dsn: "shopify:shopify123456@tcp(**************:3306)/shopify_test?charset=utf8mb4&parseTime=True&loc=Local"
slow_sql: 1
max_idle_conn: 100
sql_debug: true
test_shop: "tuyutian.myshopify.com"
track_test: false
server_domain: "tms.trackingmore.net"

jwt_secret: "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"
jwt_expire_day: 2

redis_host: "**************:6379"
redis_password:
redis_max_idle: 150
redis_active: 150
redis_idle_timeout: 200s
redis_prefix: "tmshopify_database_"

shopify_api_key: "4eac2d5386e7278461cda60a1db83bbe"
shopify_api_secret: "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"

mailgun_domain: "mail.trackingmore.net"
mailgun_api_key: "**************************************************"
mailgun_endpoint: "api.mailgun.net"
mail_from_name: "TrackingMore"
mail_from_address: "<EMAIL>"

debug_code: "DS63F"