package main

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"log"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"strings"
	"tmshopify/config"
	"tmshopify/pkg/utils/helper"
	"tmshopify/server"
	"tmshopify/server/middleware"
	"tmshopify/store/database"
	"tmshopify/store/logger"
)

// 最终方案-全兼容
func getCurrentAbPath() string {
	dir := getCurrentAbPathByExecutable()
	if strings.Contains(dir, getTmpDir()) {
		return getCurrentAbPathByCaller()
	}
	return dir
}

// 获取系统临时目录，兼容go run
func getTmpDir() string {
	dir := os.Getenv("TEMP")
	if dir == "" {
		dir = os.Getenv("TMP")
	}
	res, _ := filepath.EvalSymlinks(dir)
	return res
}

// 获取当前执行文件绝对路径
func getCurrentAbPathByExecutable() string {
	exePath, err := os.Executable()
	if err != nil {
		log.Fatal(err)
	}
	res, _ := filepath.EvalSymlinks(filepath.Dir(exePath))
	return res
}

// 获取当前执行文件绝对路径（go run）
func getCurrentAbPathByCaller() string {
	var abPath string
	_, filename, _, ok := runtime.Caller(0)
	if ok {
		abPath = path.Dir(filename)
	}
	return abPath
}
func main() {
	if err := config.Load("config/config.yaml"); err != nil {
		fmt.Println(err.Error())
		fmt.Println("Failed to load configuration")
		return
	}
	logger.InitLogger("./logs/admin")
	_, err := database.InitDB()
	if err != nil {
		fmt.Println("err open databases")
		return
	}

	database.InitRedis()
	gin.SetMode(config.Get().GinMode)

	router := gin.Default()
	router.Use(middleware.Cors("Admin"))
	server.InitAdminRouter(router)

	_ = router.Run(config.Get().Addr)
	defer func() {
		if err := recover(); err != nil {
			// 构建错误详情
			errDetails := fmt.Sprintf("Admin init Panic info: %v, request: %s", err, "main application")
			fmt.Println(errDetails)           // 本地打印
			go helper.CallWilding(errDetails) // 异步推送错误信息，例如 Slack、邮件等
		}
	}()
}
