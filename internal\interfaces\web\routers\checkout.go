package routers

import (
	"github.com/gin-gonic/gin"

	"tmshopify/internal/app/api"
	"tmshopify/internal/infras/config"
	"tmshopify/internal/providers"
)

func RegisterCheckoutRoute(routeGroup *gin.RouterGroup, repos *providers.Repositories, appConfig *config.AppConfig) {
	checkoutGroup := routeGroup.Group("/checkout")

	Checkout := new(api.Checkout)
	checkoutGroup.GET("/version", Checkout.GetVersion)
	checkoutGroup.GET("/setting", Checkout.GetSetting)
	checkoutGroup.GET("/order_status_widget", Checkout.GetOrderStatus)

}
