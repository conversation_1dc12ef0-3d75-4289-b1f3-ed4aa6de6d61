package service

import (
	"encoding/json"
	"fmt"
	"time"

	"tmshopify/config"
	"tmshopify/internal/app/definition"
	"tmshopify/internal/app/utils"
	"tmshopify/pkg/domain"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type ModernTrackPageService struct {
	TrackPageService *TrackPageService
	OrderTrackRepo   *impl.OrderTrack
	CourierRepo      *impl.Courier
}

func (c *ModernTrackPageService) BackData() map[string]interface{} {
	trackRequestDomain := c.TrackPageService.trackGlobalData.TrackRequestDomain
	//检查是否是预览模式
	if trackRequestDomain.TrackNumber == domain.PreviewNumber || (trackRequestDomain.TrackNumber == "" && trackRequestDomain.Email == domain.PreviewEmail) {
		c.previewTracking()
		return c.returnData()
	}
	//如果存在单号
	if trackRequestDomain.TrackNumber != "" {
		c.numberTracking()
		return c.returnData()
	}
	//否则检查是否有邮箱和订单号
	if trackRequestDomain.OrderName != "" && trackRequestDomain.Email != "" {
		c.orderEmailTracking("", false, false)
		return c.returnData()
	}
	//否则检查是否有电话和订单号
	if trackRequestDomain.OrderName != "" && trackRequestDomain.Phone != "" {
		c.orderPhoneTracking()
		return c.returnData()
	}
	if trackRequestDomain.OrderName != "" && trackRequestDomain.Email == "" && trackRequestDomain.Phone == "" && c.TrackPageService.trackGlobalData.TrackThemeEntity.ModernSetting.OrderLookup.OrderNumber == "3" {
		c.orderTracking()
		return c.returnData()
	}
	c.previewTracking()
	return c.returnData()
}

func (c *ModernTrackPageService) returnData() map[string]interface{} {
	orderName := c.TrackPageService.trackGlobalData.TrackRequestDomain.OrderName
	email := c.TrackPageService.trackGlobalData.TrackRequestDomain.Email
	if len(orderName) == 0 {
		orderName = c.TrackPageService.trackGlobalData.OrderRecord.OrderName
	}
	if len(email) == 0 {
		email = c.TrackPageService.trackGlobalData.OrderRecord.CustomerEmail
	}
	return map[string]interface{}{
		"data": map[string]interface{}{
			"config":       c.TrackPageService.trackGlobalData.TrackingData,
			"preview":      c.TrackPageService.trackGlobalData.TrackRequestDomain.Preview,
			"order_name":   orderName,
			"email":        email,
			"track_number": c.TrackPageService.trackGlobalData.TrackRequestDomain.TrackNumber,
			"error":        c.TrackPageService.trackGlobalData.RootData.Error,
		},
	}

}

func (c *ModernTrackPageService) numberTracking() {
	//现根据此订单查询订单名称
	orderId, _ := c.OrderTrackRepo.GetOrderId(c.TrackPageService.trackGlobalData.User.ID, c.TrackPageService.trackGlobalData.TrackRequestDomain.TrackNumber)
	c.orderEmailTracking(orderId, true, false)
}

func (c *ModernTrackPageService) orderPhoneTracking() {
	c.orderEmailTracking("", false, true)
}

func (c *ModernTrackPageService) orderTracking() {
	// 根据 Order name 查询 order id
	orderId, _ := c.OrderTrackRepo.GetOrderIdByOrderName(c.TrackPageService.trackGlobalData.User.ID, c.TrackPageService.trackGlobalData.TrackRequestDomain.OrderName)
	c.orderEmailTracking(orderId, true, false)
}

func (c *ModernTrackPageService) orderEmailTracking(orderName string, isOrderId, isPhoneTrack bool) {
	if orderName == "" {
		orderName = c.TrackPageService.trackGlobalData.TrackRequestDomain.OrderName
	}
	userId := c.TrackPageService.trackGlobalData.User.ID
	c.TrackPageService.HandleOrderData(userId, orderName,
		c.TrackPageService.trackGlobalData.TrackRequestDomain.Email,
		c.TrackPageService.trackGlobalData.TrackRequestDomain.TrackNumber,
		c.TrackPageService.trackGlobalData.TrackThemeEntity.ClassicSetting.TrackingHistory.Map,
		c.TrackPageService.trackGlobalData.TrackRequestDomain.Phone,
		models.ThemeModern, isOrderId, isPhoneTrack)

	numberMessage := c.TrackPageService.trackGlobalData.NumberMessage
	if isOrderId && numberMessage != nil && numberMessage[0].OrderName != "" {
		c.TrackPageService.trackGlobalData.OrderRecord.OrderName = numberMessage[0].OrderName
	}
	c.TrackPageService.FusionOrderAndNumberMessage(c, numberMessage)
}

func (c *ModernTrackPageService) HandleTrackInfo(trackNumber definition.TrackNumberData) definition.TrackingData {
	orderFulfillTime := trackNumber.OrderFulfillTime
	globalData := &c.TrackPageService.trackGlobalData
	orderRecord := globalData.OrderRecord
	lang := globalData.TrackRequestDomain.Lang
	lang = "en"
	var courierCode string
	if trackNumber.Courier != "" {
		courierCode = trackNumber.Courier
	}

	expressInfo := c.CourierRepo.GetCompanyInfoByCodeAndLang(courierCode, lang)

	var nullExpressInfo models.Couriers
	if expressInfo == nullExpressInfo {
		expressInfo = c.CourierRepo.GetCompanyInfoByCodeAndLang(courierCode, lang)
	}
	companyCode, carrierName := courierCode, courierCode
	if expressInfo.CompanyName != "" {
		carrierName = expressInfo.CompanyName
	}
	if expressInfo.CompanyCode != "" {
		companyCode = expressInfo.CompanyCode
	}
	//获取单号状态
	statusNumber := config.StatusNodeNumber(trackNumber.TrackStatus)
	//组装信息
	trackInfo := trackNumber.TrackInfos
	//读取物流信息
	originInfo := trackInfo.OriginInfo.TrackInfo
	destinationInfo := trackInfo.DestinationInfo.TrackInfo

	//如果自定义了订单状态，优先显示自定义订单状态
	var info []definition.ReturnInfo
	if len(globalData.CustomStatusTime) != 0 {
		for k, item := range globalData.CustomStatusTime {
			checkpointDate := utils.HandleTimeFormatBySet(int64(item),
				globalData.TrackThemeEntity.ModernSetting.TrackingInfo.DateFormat,
				globalData.TrackThemeEntity.ModernSetting.TrackingInfo.TimeFormat,
				false, false)
			trackingDetail := utils.GetStatusSummary(globalData.TrackThemeEntity.Translation, k, "")
			newInfo := definition.ReturnInfo{
				TrackInfo: definition.TrackInfo{
					CheckpointDate:           checkpointDate,
					TrackingDetail:           trackingDetail,
					CheckpointDeliveryStatus: utils.GetCheckpointStatus(k),
				}}
			info = append(info, newInfo)
		}
		length := len(info)

		//排序反转
		for i, j := 0, length-1; i < j; i, j = i+1, j-1 {
			// 交换切片中对应位置的元素
			info[i], info[j] = info[j], info[i]
		}
	} else {
		for _, t := range destinationInfo {
			info = append(info, definition.ReturnInfo{
				TrackInfo: definition.TrackInfo{
					CheckpointDate:              t.CheckpointDate,
					TrackingDetail:              t.TrackingDetail,
					CheckpointDeliveryStatus:    t.CheckpointDeliveryStatus,
					CheckpointDeliverySubstatus: t.CheckpointDeliverySubstatus,
					Location:                    t.Location,
				},
			})
		}
		for _, t := range originInfo {
			info = append(info, definition.ReturnInfo{
				TrackInfo: definition.TrackInfo{
					CheckpointDate:              t.CheckpointDate,
					TrackingDetail:              t.TrackingDetail,
					CheckpointDeliveryStatus:    t.CheckpointDeliveryStatus,
					CheckpointDeliverySubstatus: t.CheckpointDeliverySubstatus,
					Location:                    t.Location,
				}})
		}
		info = c.TrackPageService.SensitiveFiltering(info)
		if len(info) > 0 {
			utils.CmpTrackInfoDate(info)
		}
	}

	//是否显示时效 快递公司预计到达时间 如果存在则显示到页面 start
	var shippingTimeShow int

	//快递信息有预计到达时间
	scheduledDeliveryDate := trackInfo.ExpectedDeliveryTime
	//获取最后一条物流信息 shipping_time 用户设置时效
	var infoLastTimeStr, infoLastStatus string
	if !scheduledDeliveryDate.IsZero() || globalData.EddSetting.Switch {
		if len(info) > 0 {
			infoLast := info[0]
			infoLastTimeStr = infoLast.CheckpointDate
			infoLastStatus = infoLast.CheckpointDeliveryStatus
		}
	}
	var infoLastTime int64
	if infoLastTimeStr != "" {
		infoTime, _ := time.Parse(time.DateOnly, infoLastTimeStr)
		infoLastTime = infoTime.Unix()
	}
	var shippingTimeCon string
	var statusShipping int64
	//时效数据获取 是否设置时效
	if infoLastStatus != "delivered" {
		if !scheduledDeliveryDate.IsZero() {
			deliveryDate := scheduledDeliveryDate.Unix()
			statusShipping = deliveryDate - infoLastTime
			if statusShipping > 0 {
				shippingTimeShow = 1
				shippingTimeCon = utils.HandleTimeFormatBySet(deliveryDate,
					globalData.EddSetting.DateFormat, 2, true, false)
			}
		} else {
			eddService := NewEddService()
			calculateEntity := eddService.GetCalculateEntity(globalData.EddSetting, TrackCalculateData{
				UserId:           orderRecord.UserId,
				OrderCreateTime:  orderRecord.OrderCreateTime,
				OrderFulfillTime: orderFulfillTime,
				Destination:      orderRecord.RecipientCountryCode,
				Courier:          trackNumber.Courier,
				TrackNumber:      trackNumber.TrackNumber,
				FulfillmentId:    trackNumber.FulfillmentId,
			})
			dataShow := eddService.CalculateEddByOrderAndDestinationByEntities(orderRecord.UserId,
				calculateEntity, globalData.EddSetting)
			if globalData.EddSetting.Switch && dataShow.StartShow != 0 && dataShow.EndShow != 0 {
				statusShipping = dataShow.EndShow - infoLastTime
				if statusShipping > 0 {
					shippingTimeShow = 1
					// 显示内容
					startShow := utils.HandleTimeFormatBySet(dataShow.StartShow, globalData.EddSetting.DateFormat, 2, true, false)
					endShow := utils.HandleTimeFormatBySet(dataShow.EndShow, globalData.EddSetting.DateFormat, 2, true, false)
					shippingTimeCon = fmt.Sprintf("%s - %s", startShow, endShow)
				}
			}
		}
	}
	//是否显示时效 快递公司预计到达时间如果存在则显示到页面 end
	//用户判断错误签收时间,最后一条信息不是 delivered 情况
	var statusNum definition.StatusNum
	info = utils.ChangeInfoStatus(statusNumber, info)
	info, _ = c.TrackPageService.HandleInfoStatus(info, trackNumber, false)
	var data definition.TrackingData
	data.StatusNum = statusNum
	data.Trackinfo = info
	data.Countryname = trackInfo.OriginalCountry
	data.Destinationcountry = trackInfo.DestinationCountry
	data.StausDataNum = statusNumber
	data.ShippingTimeShow = shippingTimeShow
	data.ShippingTimeCon = shippingTimeCon
	data.CompanyCode = companyCode
	data.CarrierName = carrierName
	return data
}

func (c *ModernTrackPageService) previewTracking() {
	globalData := &c.TrackPageService.trackGlobalData
	originInfoStr := `{"destinationcountry":"US","scheduled_delivery_date":"2021-07-21","trackinfo":[{"tracking_detail":"Out of delivery","checkpoint_date":"2021-07-20 18:55:48","location":"Durham, US","checkpoint_delivery_status":"pickup","checkpoint_delivery_substatus":"pickup001"},{"tracking_detail":"Parcel arrived at a carrier facility","checkpoint_date":"2021-07-20 13:06:18","location":"Durham, US","checkpoint_delivery_status":"blank","checkpoint_delivery_substatus":"transit001"},{"tracking_detail":"Package departed an Amazon facility","checkpoint_date":"2021-07-20 03:15:23","location":"Richmond, Virginia, US","checkpoint_delivery_status":"blank","checkpoint_delivery_substatus":"transit001"},{"tracking_detail":"Package arrived at an Amazon facility","checkpoint_date":"2021-07-19 20:46:01","location":"Richmond, Virginia, US","checkpoint_delivery_status":"blank","checkpoint_delivery_substatus":"transit001"},{"tracking_detail":"Shipment has left seller facility and is in transit to carrier","checkpoint_date":"2021-07-19 11:21:15","location":"","checkpoint_delivery_status":"transit","checkpoint_delivery_substatus":"transit001","ItemNode":"ItemReceived"}],"stausDataNum":3,"function":"swiship-usa_web_1_200","tracklang":"en","statusInfo":"Delivered to safe place,Rolesville, US,2021-07-21 03:25:18","lastUpdateTime":"2021-07-21 03:25:18","firstUpdateTime":"2021-07-19 11:21:15","stayTimeLength":7,"ItemReceived":"2021-07-19 11:21:15","checkpoint_delivery_substatus":"pickup001","substatusTime":1626830718,"lang":9,"fromSearchSpendTime":11602.228879928589,"destinationcountryname":"US","support":1,"itemTimeLength":2,"infoState":2,"phonenum":"+31 (0) 900 0990","companyName":"Amazon FBA Swiship","companyCode":"swiship-usa"}`

	var originInfo definition.TrackingInfoData
	if err := json.Unmarshal([]byte(originInfoStr), &originInfo); err != nil {
		return
	}

	globalData.OrderRecord.CustomStatusTime = map[config.StatusNodeNumber]int{}

	globalData.OrderRecord.Product = "1234"
	c.TrackPageService.trackGlobalData.RootData.Product = "1234"
	globalData.OrderRecord.OrderCreateTime = 1626578475
	globalData.OrderRecord.OrderFulfillTime = 1626664875
	globalData.OrderRecord.RecipientCountryCode = "US"

	trackNumber := definition.TrackNumberData{
		Courier:     "swiship-usa",
		TrackStatus: 3,
		TrackInfos: definition.TrackInfos{
			OriginInfo: originInfo,
		},
	}
	trackNumber.OrderFulfillTime = 1626664875
	trackNumber.Destination = "US"
	singleTrackData := c.HandleTrackInfo(trackNumber)
	imgInfo := []definition.OrderProductImgData{
		{
			TitleImg: "https://cdn.shopify.com/s/files/1/0261/4014/7794/products/<EMAIL>?v=1616749295",
			Title:    "x1 Wedding Bride Bouquet",
			Variant:  "",
			Quantity: 1,
		},
	}

	singleTrackData.Title = imgInfo
	singleTrackData.OrderCreateTime = utils.HandleTimeFormatBySet(int64(globalData.OrderRecord.OrderCreateTime), globalData.TrackThemeEntity.ModernSetting.TrackingInfo.DateFormat, 2, false, false)
	singleTrackData.TrackNumber = domain.PreviewNumber
	singleTrackData.OrderCreateTime = utils.HandleTimeFormatBySet(int64(globalData.OrderRecord.OrderCreateTime), globalData.TrackThemeEntity.ModernSetting.TrackingInfo.DateFormat, 2, false, false)
	singleTrackData.Notes = "Order note preview"
	singleTrackData.TrackNumber = domain.PreviewNumber
	singleTrackData.Countryname = "US"
	singleTrackData.Destinationcountry = "HK"

	fromTime := globalData.OrderRecord.OrderCreateTime
	if globalData.EddSetting.CalType == 1 {
		fromTime = globalData.OrderRecord.OrderCreateTime
	}
	if globalData.EddSetting.Switch && fromTime != 0 {
		eddService := NewEddService()
		calculateEntity := eddService.GetCalculateEntity(globalData.EddSetting, TrackCalculateData{
			UserId:           globalData.User.ID,
			OrderCreateTime:  trackNumber.OrderCreateTime,
			OrderFulfillTime: trackNumber.OrderFulfillTime,
			Destination:      trackNumber.Destination,
			Courier:          trackNumber.Courier,
			TrackNumber:      trackNumber.TrackNumber,
			FulfillmentId:    trackNumber.FulfillmentId,
		})
		dataShow := eddService.CalculateEddByOrderAndDestinationByEntities(globalData.User.ID,
			calculateEntity, globalData.EddSetting)

		if dataShow.StartShow != 0 && dataShow.EndShow != 0 {
			startShow := utils.HandleTimeFormatBySet(dataShow.StartShow, globalData.EddSetting.DateFormat, 2, false, false)
			endShow := utils.HandleTimeFormatBySet(dataShow.EndShow, globalData.EddSetting.DateFormat, 2, false, false)
			singleTrackData.ShippingTimeCon = fmt.Sprintf("%s - %s", startShow, endShow)
		}
	}
	if len(singleTrackData.ShippingTimeCon) > 0 {
		singleTrackData.ShippingTimeShow = 1
	}
	globalData.TrackingData = append(globalData.TrackingData, singleTrackData)
}

func NewModernTrackPageService() *ModernTrackPageService {
	return &ModernTrackPageService{
		TrackPageService: NewTrackPageService(),
		OrderTrackRepo:   &impl.OrderTrack{},
		CourierRepo:      &impl.Courier{},
	}
}
