package models

type Theme string

const (
	ThemeClassic Theme = "classic"
	ThemeModern  Theme = "modern"
)

type TrackPageThemes struct {
	UserId      uint   `gorm:"column:user_id;type:int(10) unsigned;NOT NULL" json:"user_id"`
	ThemeCode   Theme  `gorm:"column:theme_code;type:varchar(255);comment:主题代号;NOT NULL" json:"theme_code"`
	Settings    string `gorm:"column:settings;type:text;NOT NULL" json:"settings"`
	Translation string `gorm:"column:translation;type:mediumtext;comment:json 页面翻译键值" json:"translation"`
	CustomStyle string `gorm:"column:custom_style;type:mediumtext;comment:自定义样式" json:"custom_style"`
	TopHtml     string `gorm:"column:top_html;type:mediumtext" json:"top_html"`
	BottomHtml  string `gorm:"column:bottom_html;type:mediumtext" json:"bottom_html"`
	Model
}

func (TrackPageThemes) TableName() string {
	return "track_page_themes"
}
