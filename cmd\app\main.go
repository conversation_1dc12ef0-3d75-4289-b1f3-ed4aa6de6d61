package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/channelwill/athena/logger"
	"github.com/channelwill/athena/monitor"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	oldConfig "tmshopify/config"
	"tmshopify/internal/infras/config"
	"tmshopify/internal/interfaces/web/routers"
	"tmshopify/internal/providers"
	"tmshopify/pkg/shopify"
	"tmshopify/store/database"
	storeLogger "tmshopify/store/logger"
)

func main() {
	pid := os.Getpid()
	fmt.Printf("current service pid: %d\n", pid)
	if err := oldConfig.Load("config/config.yaml"); err != nil {
		fmt.Println(err.Error())
		fmt.Println("Failed to load configuration")
		return
	}
	storeLogger.InitLogger("./logs/app")
	// 加载配置和初始化资源
	appConf := config.InitAppConfig()
	// 日志输出采用zap框架实现日志json格式输出
	logger.Default(
		logger.WriteToFile(true), logger.WithStdout(true), // 将日志写到stdout
		logger.WithAddCaller(true), logger.WithLogLevel(appConf.GetLogLevel()),
	)

	logger.Info(context.Background(), "starting server", zap.Int("pid", pid))

	db, err := config.NewDB("db_conf")
	if err != nil {
		log.Fatalf("db init error:%v", err)
	}
	// 兼容老接口的单例对象，否则改造成本太大
	database.DB = db
	// 初始化app设置
	shopify.Setup()
	redisClient, err := config.NewRedis("redis_conf")
	if err != nil {
		log.Fatalf("redis init error:%v", err)
	}
	// 兼容老接口的单例对象，否则改造成本太大
	database.RS = redisClient

	// 初始化repos
	repos := providers.NewRepositories(db, redisClient)

	// 初始化路由规则
	router := gin.New()
	// 注册路由规则
	routers.InitRouters(router, repos, appConf)

	// 服务server设置
	server := &http.Server{
		Handler:           router,
		Addr:              fmt.Sprintf("0.0.0.0:%d", appConf.HttpPort),
		IdleTimeout:       20 * time.Second, // tcp idle time
		ReadHeaderTimeout: 10 * time.Second,
		ReadTimeout:       10 * time.Second,
		WriteTimeout:      15 * time.Second,
	}

	// 在独立携程中运行
	log.Println("server run on: ", appConf.HttpPort)
	go func() {
		if err := server.ListenAndServe(); err != nil {
			if !errors.Is(err, http.ErrServerClosed) {
				log.Println(context.Background(), "server close error", map[string]interface{}{
					"trace_error": err.Error(),
				})

				log.Println("server close error:", err)
				return
			}

			log.Println("server will exit...")
		}
	}()

	// 初始化prometheus和pprof
	// 访问地址：http://localhost:8090/metrics
	// 访问地址：http://localhost:8090/debug/pprof/
	monitor.InitMonitor(appConf.HttpMonitorPort, true)

	// server平滑重启
	ch := make(chan os.Signal, 1)
	// We'll accept graceful shutdowns when quit via SIGINT (Ctrl+C)
	// receive signal to exit main goroutine.
	signal.Notify(ch, syscall.SIGINT, syscall.SIGTERM)

	// Block until we receive our signal.
	sig := <-ch

	log.Println("exit signal: ", sig.String())

	// Create a deadline to wait for.
	ctx, cancel := context.WithTimeout(context.Background(), appConf.GracefulWait)
	defer cancel()
	// Doesn't block if no connections, but will otherwise wait
	// until the timeout deadline.
	// Optionally, you could run server.Shutdown in a goroutine and block on
	// if your application should wait for other services
	// to finalize based on context cancellation.
	done := make(chan struct{}, 1)
	go func() {
		defer close(done)

		_ = server.Shutdown(ctx)
	}()

	select {
	case <-done:
		log.Println("stop server success")
	case <-ctx.Done():
		log.Println("stop server context timeout")
	}

	log.Println("server shutdown success")
}
