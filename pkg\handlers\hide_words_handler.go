package handlers

import (
	"encoding/json"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	"tmshopify/config"
	"tmshopify/internal/app/definition"
	"tmshopify/internal/app/entity"
	"tmshopify/internal/app/utils"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

const (
	HIDE                  = "0"
	REPLACE               = "1"
	WORD                  = "0"
	SENTENCE              = "1"
	EmptyString           = ""
	NotFoundStatus        = "notfound"
	LocationStartPrefixes = ", -，"
)

type HideWordsHandler struct {
	HideSetting []entity.HideSetting
}

func (h *HideWordsHandler) HandleHideWords(info []definition.ReturnInfo) []definition.ReturnInfo {
	var shouldSaveStatus definition.ReturnInfo
	for k := len(info) - 1; k >= 0; k-- {
		item := info[k]
		processedText, location := h.processItem(item)
		if strReplace := h.processHideSettings(processedText); strReplace == EmptyString {
			if !strings.Contains(item.CheckpointDeliverySubstatus, NotFoundStatus) {
				shouldSaveStatus = item
			}
			nullTrackInfo := definition.ReturnInfo{}
			if shouldSaveStatus != nullTrackInfo {
				h.updateAdjacentStatus(info, k, shouldSaveStatus)
			}
			info = append(info[:k], info[k+1:]...)
			continue
		} else {
			h.updateReturnInfo(&info[k], strReplace, location)
		}
	}
	return info
}

func (h *HideWordsHandler) processItem(item definition.ReturnInfo) (processedText string, location string) {
	// 优化文本显示 tracking_detail
	trackingDetail := removeChineseCharacters(strings.TrimSpace(item.TrackingDetail))
	location = removeChineseCharacters(strings.TrimSpace(item.Location))
	// 去掉开头结束特殊字符
	if trackingDetail != EmptyString {
		trackingDetail = strings.TrimSpace(utils.DelSpecialWord(trackingDetail))
	}
	location = h.trimLocationPrefixes(location)
	if location != EmptyString {
		processedText = trackingDetail + ", " + location
	} else {
		processedText = trackingDetail
	}
	return
}

func (h *HideWordsHandler) trimLocationPrefixes(location string) string {
	for _, prefix := range strings.Split(LocationStartPrefixes, "") {
		location = strings.TrimPrefix(location, prefix)
	}
	return location
}

func (h *HideWordsHandler) processHideSettings(text string) string {
	// "hide_type":隐藏0或替换1,"replace_type":字符替换0或整行1
	for _, hide := range h.HideSetting {
		key := strconv.Itoa(hide.HideType) + strconv.Itoa(hide.ReplaceType)
		if origins, ok := hide.Origin.([]string); ok {
			text = h.processMultiOrigins(key, text, origins, hide.Replace)
		} else {
			origin := hide.Origin.(string)
			text = h.processString(key, text, origin, hide.Replace)
		}
		text = h.removeExtraSpaces(text)
	}
	text = strings.TrimSpace(utils.DelSpecialWord(text))
	if text == "," {
		text = EmptyString
	}
	if text != EmptyString {
		text = strings.TrimSuffix(text, ",")
		if strings.HasPrefix(text, ",") || strings.HasPrefix(text, "/") {
			text = text[1:]
		}
		text = h.removeEmptySymbols(text)
	}
	return text
}

func (h *HideWordsHandler) updateReturnInfo(item *definition.ReturnInfo, strReplace, location string) {
	item.TrackingDetail = strings.TrimSpace(strReplace)
	item.LocationMap = location
	item.Location = EmptyString
}

const (
	HideWord        = HIDE + WORD
	ReplaceWord     = REPLACE + WORD
	HideSentence    = HIDE + SENTENCE
	ReplaceSentence = REPLACE + SENTENCE
)

func applyRegexReplacement(patterns []string, input, replacement string) string {
	re := compilePatterns(patterns)
	return re.ReplaceAllString(input, replacement)
}

func (h *HideWordsHandler) processMultiOrigins(key, output string, origins []string, replace string) string {
	switch key {
	case HideWord:
		return applyRegexReplacement(origins, output, "")
	case ReplaceWord:
		return applyRegexReplacement(origins, output, replace)
	case HideSentence:
		for _, s := range origins {
			output = h.replaceSentence(output, s, "")
			if h.isAllUpperCase(s) {
				output = h.replaceSentence(output, strings.ToUpper(s), "")
			}
			if output == "" {
				break
			}
		}
		return output
	case ReplaceSentence:
		for _, s := range origins {
			output = h.replaceSentence(output, s, replace)
			if h.isAllUpperCase(s) {
				output = h.replaceSentence(output, strings.ToUpper(s), replace)
			}
			if output == "" {
				break
			}
		}
		return output
	default:
		return output
	}
}
func (h *HideWordsHandler) processString(key, strReplace, s, replace string) string {
	switch key {
	case HIDE + WORD:
		return h.replaceString(strReplace, s, "")
	case REPLACE + WORD:
		return h.replaceString(strReplace, s, replace)
	case HIDE + SENTENCE:
		return h.replaceSentence(strReplace, s, "")
	case REPLACE + SENTENCE:
		return h.replaceSentence(strReplace, s, replace)
	default:
		return strReplace
	}
}

// compilePatterns compiles all origins into a single regex pattern
func compilePatterns(origins []string) *regexp.Regexp {
	patterns := make([]string, len(origins))
	for i, origin := range origins {
		patterns[i] = regexp.QuoteMeta(origin)
	}
	return regexp.MustCompile("(?i)" + strings.Join(patterns, "|"))
}

func (h *HideWordsHandler) isAllUpperCase(s string) bool {
	for i := 0; i < len(s); i++ {
		if s[i] < 'A' || s[i] > 'Z' {
			return false
		}
	}
	return true
}

func (h *HideWordsHandler) removeExtraSpaces(s string) string {
	return strings.Join(strings.Fields(s), " ")
}

func (h *HideWordsHandler) updateAdjacentStatus(info []definition.ReturnInfo, k int, status definition.ReturnInfo) {
	if k-1 >= 0 && strings.Contains(info[k-1].CheckpointDeliverySubstatus, "notfound") {
		info[k-1].CheckpointDeliveryStatus = status.CheckpointDeliveryStatus
		info[k-1].CheckpointDeliverySubstatus = status.CheckpointDeliverySubstatus
	} else if k+1 < len(info) && strings.Contains(info[k+1].CheckpointDeliverySubstatus, "notfound") {
		info[k+1].CheckpointDeliveryStatus = status.CheckpointDeliveryStatus
		info[k+1].CheckpointDeliverySubstatus = status.CheckpointDeliverySubstatus
	}
}

func (h *HideWordsHandler) removeEmptySymbols(s string) string {
	s = strings.ReplaceAll(s, "[]", "")
	s = strings.ReplaceAll(s, "()", "")
	s = strings.ReplaceAll(s, "【】", "")
	return s
}
func (h *HideWordsHandler) InitSetting(hideSetting []entity.HideSetting) {
	if len(hideSetting) == 0 {
		return
	}

	// 过滤设置
	filteredHideSetting := make([]entity.HideSetting, 0, len(hideSetting))
	for _, v := range hideSetting {
		if v.Origin != "" {
			if v.Origin == "postman" {
				v.Origin = "/postman :[A-Za-z0-9,]*/"
			}
			filteredHideSetting = append(filteredHideSetting, v)
		}
	}

	// 按 sort 降序排序
	sort.Slice(filteredHideSetting, func(i, j int) bool {
		return filteredHideSetting[i].Sort > filteredHideSetting[j].Sort
	})

	var (
		cityListHidden             entity.HideSetting
		dropShipListHidden         entity.HideSetting
		cityUsed, dropShippingUsed bool
	)

	for _, item := range filteredHideSetting {
		switch {
		case item.Origin == "{china_cities}" && !cityUsed:
			cityList := h.setHideChina()
			cityListHidden = entity.HideSetting{
				HideType:    item.HideType,
				ReplaceType: item.ReplaceType,
				Origin:      cityList,
				Replace:     item.Replace,
				Sort:        item.Sort,
			}
			cityUsed = true

		case item.Origin == "{dropshipping_couriers}" && !dropShippingUsed:
			dropShipList := h.setHideDropShipping()
			dropShipListHidden = entity.HideSetting{
				HideType:    item.HideType,
				ReplaceType: item.ReplaceType,
				Origin:      dropShipList,
				Replace:     item.Replace,
				Sort:        item.Sort,
			}
			dropShippingUsed = true
		}
	}

	if cityUsed {
		h.HideSetting = append(h.HideSetting, cityListHidden)
	}
	if dropShippingUsed {
		h.HideSetting = append(h.HideSetting, dropShipListHidden)
	}

	// Append remaining items to HideSetting
	for _, item := range filteredHideSetting {
		if item.Origin != "{china_cities}" && item.Origin != "{dropshipping_couriers}" {
			h.HideSetting = append(h.HideSetting, item)
		}
	}
}

func (h *HideWordsHandler) setHideChina() []string {
	cityJsonData, _ := database.RS.Get(config.CacheKey.ChinaCityHideKey)
	var cityData []string
	_ = json.Unmarshal(cityJsonData, &cityData)
	if len(cityData) == 0 {
		database.DB.Model(&models.SensitiveWordLists{}).Pluck("en_name", &cityData)
		cityJsonData, _ = json.Marshal(cityData)
		_ = database.RS.SetWithExpire(config.CacheKey.ChinaCityHideKey, string(cityJsonData), 7*24*time.Hour)
	}
	return cityData
}

func (h *HideWordsHandler) setHideDropShipping() []string {
	return []string{
		"2B", " Ali", "Ali Express", "Alibaba", "alicdn", "Aliexpress",
		"amazon", "bang", "banggood", "BangGu", "Cainiao", " CJ", "CJ dropshipping",
		"CJ Packet", "cjcarrier", "CJdropshipping", "CJPacket", "doba",
		"Dropship", "Dropshipper", "Dropshipping", "Dropshipzone", "Dsers", "ebay",
		"eCom", "ECOMMERCE", " EMS", "epacket", "Eprolo", "Global cainiao", "Globalcainiao",
		"Gooten", "huxisudi", "lastmile", "lazada", "Lightinthebox", "megagoods", "oberlo",
		"print", "Printech", "Printful", "Printify", "Printondemand", "printy", "rakuten",
		"salehoo", "saleshoo", "Shopee", "Sunrise", "Supplier", "Taobao", "vendor", "Walmart",
		"warehouse", "Wholesale", "Wish", "Wishpost", "Yanwen", "YouZheng", " YT", " Yun",
		"Yun Express", "Yun Track", "YunExpress", "Yuntrack", "Yuntu", "zen",
		"Zendrop", "ZhongGuoYouZhengSuDiWuLiuGuFenYouXianGongSi", "深东凤岗仓",
		"Shendongfenggangcang", "Yiwu", "amp", "4PX", "xyg",
	}
}

func (h *HideWordsHandler) hideString(sentence, find string) string {
	if find == "" || !strings.Contains(sentence, find) {
		return sentence
	}

	// 因为将逗号去掉可能导致单词拼接在一起,因此添加是否是替换完整单词的判断
	replacements := []string{
		find + ",",
		find + " ,",
		"," + find,
		", " + find,
	}

	for _, replacement := range replacements {
		sentence = h.replaceString(sentence, replacement, find)
	}

	removeStrings := []string{
		find + " from",
		find + " of",
		find + " to",
		find + " on",
		find + " in",
		"from " + find,
		"of " + find,
		"to " + find,
		"on " + find,
		"in " + find,
		find,
	}

	for _, removeStr := range removeStrings {
		sentence = h.replaceString(sentence, removeStr, "")
	}
	return sentence
}

func (h *HideWordsHandler) replaceSentence(sentence, origin, replace string) string {
	if strings.Contains(strings.ToLower(sentence), strings.ToLower(origin)) {
		return replace
	}
	return sentence
}

func (h *HideWordsHandler) replaceString(sentence, origin, replace string) string {
	if !strings.Contains(sentence, origin) {
		return sentence
	}
	return strings.ReplaceAll(sentence, origin, replace)
}

func removeChineseCharacters(input string) string {
	// 定义一个正则表达式匹配中文字符的范围
	re := regexp.MustCompile(`[\p{Han}]`)
	// 用空字符串替换匹配到的中文字符
	return re.ReplaceAllString(input, "")
}

func NewHideWordsHandler() *HideWordsHandler {
	return &HideWordsHandler{
		HideSetting: []entity.HideSetting{},
	}
}
