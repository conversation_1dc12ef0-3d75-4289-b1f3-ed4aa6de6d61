package models

import "time"

type DataUserRenewDetail struct {
	CreateDate time.Time `gorm:"column:create_date;type:date;comment:当天日期;NOT NULL" json:"create_date"`
	UserId     int       `gorm:"column:user_id;type:int(11);default:0;comment:用户ID;NOT NULL" json:"user_id"`
	Type       int       `gorm:"column:type;type:int(11);default:0;comment:付费类型(1:付费,2:续订);NOT NULL" json:"type"`
	Price      int       `gorm:"column:price;type:decimal(10,2);comment:当前订阅金额" json:"price"`
	Version    string    `gorm:"column:version;type:char(6);NOT NULL" json:"version"`
	Model
}

func (DataUserRenewDetail) TableName() string {
	return "data_user_renew_details"
}
