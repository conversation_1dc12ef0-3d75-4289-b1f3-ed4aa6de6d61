package models

import "time"

type DataPaidStatistics struct {
	CreateDate     time.Time `gorm:"column:create_date;type:date;comment:当天日期;NOT NULL" json:"create_date"`
	TotalPaid      int       `gorm:"column:total_paid;type:int(11);default:0;comment:总付费用户数;NOT NULL" json:"total_paid"`
	TotalPaidPrice float64   `gorm:"column:total_paid_price;type:decimal(10,2);comment:总付费用户价值MMR" json:"total_paid_price"`
	NewPaid        int       `gorm:"column:new_paid;type:int(11);default:0;comment:新增付费用户数;NOT NULL" json:"new_paid"`
	NewInstall     int       `gorm:"column:new_install;type:int(11);default:0;comment:新增安装用户数;NOT NULL" json:"new_install"`
	ResumeNum      int       `gorm:"column:resume_num;type:int(11);comment:恢复付费用户数" json:"resume_num"`
	CancelNum      int       `gorm:"column:cancel_num;type:int(11);comment:取消订阅用户数" json:"cancel_num"`
	TotalInstall   int       `gorm:"column:total_install;type:int(11);comment:安装用户总数" json:"total_install"`
	TotalUninstall int       `gorm:"column:total_uninstall;type:int(11);comment:卸载用户总数" json:"total_uninstall"`
	Model
}

func (DataPaidStatistics) TableName() string {
	return "data_paid_statistics"
}
