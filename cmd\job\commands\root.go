package commands

import (
	"log"
	"os"
	"time"

	"github.com/spf13/cobra"
)

var rootCmd = &cobra.Command{
	Use:   "tmshopify-job",
	Short: "tmshopify-job",
	Long:  `tmshopify-job 是一个任务调度系统，用于管理任务的执行，支持定时任务和计划任务。`,
}

// Execute 执行命令行操作并返回错误
func Execute(gracefulTimeout time.Duration) {
	if err := rootCmd.Execute(); err != nil {
		log.Println("exec job err: ", err)
		os.Exit(1)
	}

	// graceful.Shutdown(gracefulTimeout)
}
