package main

import (
	"context"
	"log"
	"os"

	"github.com/channelwill/athena/logger"
	"go.uber.org/zap"

	"tmshopify/cmd/job/commands"
	oldConfig "tmshopify/config"
	"tmshopify/internal/infras/config"
)

var Version string

func init() {
	commands.Version = Version
}

func main() {
	log.Println("tmshopify-job 启动中...")

	// 初始化配置
	appConf := config.InitAppConfig()

	// 兼容旧的设置
	if err := compatible(); err != nil {
		log.Fatalf("compatibility errors. %v", err)
	}

	// 日志输出采用zap框架实现日志json格式输出
	logger.Default(
		logger.WriteToFile(true),
		logger.WithStdout(true), // 将日志写到stdout
		logger.WithAddCaller(true),
		logger.WithLogLevel(appConf.GetLogLevel()),
	)

	// 创建一个上下文，包含请求ID
	ctx := context.Background()
	logger.Info(ctx, "任务系统启动", zap.Int("pid", os.Getpid()))

	// 命令初始化
	commands.InitTaskCommands()

	// 执行命令
	commands.Execute(appConf.GracefulWait)
}

// compatible 兼容旧的 config
func compatible() error {
	// 加载旧的配置文件
	if err := oldConfig.Load("config/config.yaml"); err != nil {
		return err
	}

	return nil
}
