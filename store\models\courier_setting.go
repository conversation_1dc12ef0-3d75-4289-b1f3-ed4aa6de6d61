package models

type CourierSettings struct {
	UserId      int32  `gorm:"column:user_id;type:int UNSIGNED;not null;" json:"user_id"`
	Lang        string `gorm:"column:lang;type:char(2);comment:courier lang;not null;" json:"lang"`
	CourierCode string `gorm:"column:courier_code;type:char(255);comment:courier code;not null;" json:"courier_code"`
	TrackSort   int32  `gorm:"column:track_sort;type:int UNSIGNED;comment:setting sort;not null;default:0;" json:"track_sort"`
	IsDelete    int32  `gorm:"column:is_delete;type:tinyint UNSIGNED;not null;default:0;" json:"is_delete"`
	PostalCode  string `gorm:"column:postal_code;type:char(30);comment:邮编使用字段;not nul;default:shipping_address;" json:"postal_code"`
	Model
}
