package api

import (
	"math"
	"strconv"
	"time"

	"tmshopify/internal/admin/service"
	"tmshopify/pkg/utils/ginparam"
	"tmshopify/pkg/utils/paginate"
	"tmshopify/server/response"

	"github.com/gin-gonic/gin"
)

type Statistics struct {
}

func (*Statistics) PaidSubscribersChart(ctx *gin.Context) {
	input, _ := ginparam.RequestInputs(ctx)
	startStr := input["start_time"].(string)
	endStr := input["end_time"].(string)
	compared := int(input["compared"].(float64))
	comparedStartStr := input["compared_start_time"].(string)
	comparedEndStr := input["compared_end_time"].(string)
	var (
		cType                  int
		percentage             float64
		result, previousResult int
	)
	if startStr != "" && endStr != "" {
		startTime, err := time.Parse(time.DateOnly, startStr)
		if err != nil {
			response.Forbidden(ctx, "时间有误")
			return
		}
		endTime, err := time.Parse(time.DateOnly, endStr)
		if err != nil {
			response.Forbidden(ctx, "时间有误")
			return
		}
		if endTime.Before(startTime) {
			response.Forbidden(ctx, "时间有误")
			return
		}
	} else {
		//全部时间
		cType = 1
	}
	date, count, comparedCount := service.NewPaidStatistic().GetPaidSubscribersChart(startStr, endStr, cType, compared, comparedStartStr, comparedEndStr)
	if count != nil {
		result = count[len(count)-1] //最后一个数据值
	}
	if comparedCount != nil {
		previousResult = comparedCount[len(comparedCount)-1] //对比最后一个数据值
	}
	if previousResult != 0 {
		percentage = math.Round((float64(result)-float64(previousResult))/float64(previousResult)*100) / 100
	} else if previousResult == 0 && result != 0 {
		percentage = 1
	}
	if compared == 0 {
		//不选择对比
		response.Success(ctx, &response.SuccessResponse{
			Data: map[string]interface{}{
				"date":       date,
				"count":      count,
				"result":     result,
				"percentage": percentage,
			},
		})
	} else {
		response.Success(ctx, &response.SuccessResponse{
			Data: map[string]interface{}{
				"date":           date,
				"count":          count,
				"comparedCount":  comparedCount,
				"result":         result,
				"previousResult": previousResult,
				"percentage":     percentage,
			},
		})
	}
}

func (*Statistics) MRRChart(ctx *gin.Context) {
	input, _ := ginparam.RequestInputs(ctx)
	startStr := input["start_time"].(string)
	endStr := input["end_time"].(string)
	compared := int(input["compared"].(float64))
	comparedStartStr := input["compared_start_time"].(string)
	comparedEndStr := input["compared_end_time"].(string)
	var (
		cType                  int
		percentage             float64
		result, previousResult float64
	)
	if startStr != "" && endStr != "" {
		startTime, err := time.Parse(time.DateOnly, startStr)
		if err != nil {
			response.Forbidden(ctx, "时间有误")
			return
		}
		endTime, err := time.Parse(time.DateOnly, endStr)
		if err != nil {
			response.Forbidden(ctx, "时间有误")
			return
		}
		if endTime.Before(startTime) {
			response.Forbidden(ctx, "时间有误")
			return
		}
	} else {
		//全部时间
		cType = 1
	}
	date, price, comparedPrice := service.NewPaidStatistic().GetPaidData(startStr, endStr, cType, compared, comparedStartStr, comparedEndStr, 1)
	if price != nil {
		result = price[len(price)-1] //最后一个数据值
	}
	if comparedPrice != nil {
		previousResult = comparedPrice[len(comparedPrice)-1] //对比最后一个数据值
	}
	if previousResult != 0 {
		percentage = math.Round((result-previousResult)/previousResult*100) / 100
	} else if previousResult == 0 && result != 0 {
		percentage = 1
	}
	if compared == 0 {
		//不选择对比
		response.Success(ctx, &response.SuccessResponse{
			Data: map[string]interface{}{
				"date":       date,
				"price":      price,
				"result":     result,
				"percentage": percentage,
			},
		})
	} else {
		response.Success(ctx, &response.SuccessResponse{
			Data: map[string]interface{}{
				"date":           date,
				"price":          price,
				"comparedPrice":  comparedPrice,
				"result":         result,
				"previousResult": previousResult,
				"percentage":     percentage,
			},
		})
	}
}

func (*Statistics) RevenuePerSubscriberChart(ctx *gin.Context) {
	input, _ := ginparam.RequestInputs(ctx)
	startStr := input["start_time"].(string)
	endStr := input["end_time"].(string)
	compared := int(input["compared"].(float64))
	comparedStartStr := input["compared_start_time"].(string)
	comparedEndStr := input["compared_end_time"].(string)
	var (
		cType                  int
		percentage             float64
		result, previousResult float64
	)
	if startStr != "" && endStr != "" {
		startTime, err := time.Parse(time.DateOnly, startStr)
		if err != nil {
			response.Forbidden(ctx, "时间有误")
			return
		}
		endTime, err := time.Parse(time.DateOnly, endStr)
		if err != nil {
			response.Forbidden(ctx, "时间有误")
			return
		}
		if endTime.Before(startTime) {
			response.Forbidden(ctx, "时间有误")
			return
		}
	} else {
		//全部时间
		cType = 1
	}
	date, price, comparedPrice := service.NewPaidStatistic().GetPaidData(startStr, endStr, cType, compared, comparedStartStr, comparedEndStr, 2)

	if price != nil {
		result = price[len(price)-1] //最后一个数据值
	}
	if comparedPrice != nil {
		previousResult = comparedPrice[len(comparedPrice)-1] //对比最后一个数据值
	}
	if previousResult != 0 {
		percentage = math.Round((result-previousResult)/previousResult*100) / 100
	} else if previousResult == 0 && result != 0 {
		percentage = 1
	}
	if compared == 0 {
		//不选择对比
		response.Success(ctx, &response.SuccessResponse{
			Data: map[string]interface{}{
				"date":       date,
				"price":      price,
				"result":     result,
				"percentage": percentage,
			},
		})
	} else {
		response.Success(ctx, &response.SuccessResponse{
			Data: map[string]interface{}{
				"date":           date,
				"price":          price,
				"comparedPrice":  comparedPrice,
				"result":         result,
				"previousResult": previousResult,
				"percentage":     percentage,
			},
		})
	}
}

func (*Statistics) PaidSubscriberGrowthChart(ctx *gin.Context) {
	input, _ := ginparam.RequestInputs(ctx)
	startStr := input["start_time"].(string)
	endStr := input["end_time"].(string)
	var (
		cType  int
		result int
	)
	if startStr != "" && endStr != "" {
		startTime, err := time.Parse(time.DateOnly, startStr)
		if err != nil {
			response.Forbidden(ctx, "时间有误")
			return
		}
		endTime, err := time.Parse(time.DateOnly, endStr)
		if err != nil {
			response.Forbidden(ctx, "时间有误")
			return
		}
		if endTime.Before(startTime) {
			response.Forbidden(ctx, "时间有误")
			return
		}
	} else {
		//全部时间
		cType = 1
	}
	date, newPaid, resume, cancel, result := service.NewPaidStatistic().GetSubscriberUserData(startStr, endStr, cType)
	response.Success(ctx, &response.SuccessResponse{
		Data: map[string]interface{}{
			"date":   date,
			"new":    newPaid,
			"resume": resume,
			"cancel": cancel,
			"result": result,
		},
	})
}

func (*Statistics) SubscriberConversionRateChart(ctx *gin.Context) {
	var result float64
	date, rate := service.NewPaidConversionRateStatistic().GetSubscriberConversionRateData()
	if rate != nil {
		result = rate[len(rate)-1] //最后一个数据值
	}
	response.Success(ctx, &response.SuccessResponse{
		Data: map[string]interface{}{
			"date":   date,
			"rate":   rate,
			"result": result,
		},
	})
}

func (s *Statistics) MerchantsChart(ctx *gin.Context) {
	input, _ := ginparam.RequestInputs(ctx)
	startStr := input["start_time"].(string)
	endStr := input["end_time"].(string)
	compared := int(input["compared"].(float64))
	comparedStartStr := input["compared_start_time"].(string)
	comparedEndStr := input["compared_end_time"].(string)
	var (
		cType                  int
		percentage             float64
		result, previousResult int
	)
	if startStr != "" && endStr != "" {
		startTime, err := time.Parse(time.DateOnly, startStr)
		if err != nil {
			response.Forbidden(ctx, "时间有误")
			return
		}
		endTime, err := time.Parse(time.DateOnly, endStr)
		if err != nil {
			response.Forbidden(ctx, "时间有误")
			return
		}
		if endTime.Before(startTime) {
			response.Forbidden(ctx, "时间有误")
			return
		}
	} else {
		//全部时间
		cType = 1
	}
	date, count, comparedCount := service.NewPaidStatistic().GetMerchantsData(startStr, endStr, cType, compared, comparedStartStr, comparedEndStr)

	if count != nil {
		//最后一个数据值
		result = count[len(count)-1]
	}
	if comparedCount != nil {
		//对比最后一个数据值
		previousResult = comparedCount[len(comparedCount)-1]
	}
	//对比的差值百分比
	if previousResult != 0 {
		percentage = math.Round((float64(result)-float64(previousResult))/float64(previousResult)*100) / 100
	} else if previousResult == 0 && result != 0 {
		percentage = 1
	}
	if compared == 0 {
		//不选择对比
		response.Success(ctx, &response.SuccessResponse{
			Data: gin.H{
				"date":       date,
				"count":      count,
				"result":     result,
				"percentage": percentage,
			},
		})
	} else {
		response.Success(ctx, &response.SuccessResponse{
			Data: gin.H{
				"date":           date,
				"count":          count,
				"comparedCount":  comparedCount,
				"result":         result,
				"previousResult": previousResult,
				"percentage":     percentage,
			},
		})
	}
}

func (s *Statistics) MerchantsGrowthChart(ctx *gin.Context) {
	input, _ := ginparam.RequestInputs(ctx)
	startStr := input["start_time"].(string)
	endStr := input["end_time"].(string)
	var (
		cType int
	)
	if startStr != "" && endStr != "" {
		startTime, err := time.Parse(time.DateOnly, startStr)
		if err != nil {
			response.Forbidden(ctx, "时间有误")
			return
		}
		endTime, err := time.Parse(time.DateOnly, endStr)
		if err != nil {
			response.Forbidden(ctx, "时间有误")
			return
		}
		if endTime.Before(startTime) {
			response.Forbidden(ctx, "时间有误")
			return
		}
	} else {
		//全部时间
		cType = 1
	}
	date, newUser, install, unInstall, totalCount := service.NewPaidStatistic().GetUserGrowthData(startStr, endStr, cType)

	response.Success(ctx, &response.SuccessResponse{
		Data: gin.H{
			"date":       date,
			"new":        newUser,
			"install":    install,
			"unInstall":  unInstall,
			"totalCount": totalCount,
		},
	})
}

func (s *Statistics) CountryUsersDistribution(ctx *gin.Context) {
	input, _ := ginparam.RequestInputs(ctx)
	startStr := input["start_time"].(string)
	endStr := input["end_time"].(string)
	param := paginate.RequestParam(ctx)

	var (
		cType int
	)
	if startStr != "" && endStr != "" {
		startTime, err := time.Parse(time.DateOnly, startStr)
		if err != nil {
			response.Forbidden(ctx, "时间有误")
			return
		}
		endTime, err := time.Parse(time.DateOnly, endStr)
		if err != nil {
			response.Forbidden(ctx, "时间有误")
			return
		}
		if endTime.Before(startTime) {
			response.Forbidden(ctx, "时间有误")
			return
		}
	} else {
		//全部时间
		cType = 1
	}
	data, countryNum := service.NewUserStatistic().CountryUsersDistribution(startStr, endStr, cType, param)

	response.Success(ctx, &response.SuccessResponse{
		Data: gin.H{
			"data":       data,
			"countryNum": countryNum,
		},
	})
}

func (*Statistics) RetentionBySubscriber(ctx *gin.Context) {
	var (
		t      time.Time
		result [][]string
	)
	currentTime := time.Now()
	startTime := time.Date(currentTime.Year(), currentTime.Month()-13, 1, 0, 0, 0, 0, currentTime.Location()) // 开始时间
	endTime := startTime.AddDate(0, 12, 0).Add(-time.Second)                                                  // 结束时间
	for t = startTime; t.Before(endTime); t = t.AddDate(0, 1, 0) {
		arr := service.NewDataUserRenewStatistic().GetUserRenewSubscriberData(t) //获取每月续费率
		result = append(result, arr)
	}

	// 计算每列的平均值
	averages := make([]string, len(result[0]))
	for col := 1; col < len(result[0]); col++ {
		var sum float64
		var colCount int
		for row := 0; row < len(result); row++ {
			if col < len(result[row]) {
				num, _ := strconv.ParseFloat(result[row][col], 64)
				sum += num //记录列总共
				colCount++ //记录列个数
			}
		}
		if col == 1 {
			//Start value列保留小数点
			averages[col] = strconv.FormatFloat(math.Round(sum/float64(colCount)*100)/100, 'f', -1, 64)
		} else {
			//其余列保留整数部分
			averages[col] = strconv.FormatFloat(math.Round(sum/float64(colCount)*100)/100, 'f', 0, 64)
		}
	}
	averages[0] = "Average"
	result = append([][]string{averages}, result...)

	for key, row := range result {
		for k, col := range row {
			if k > 1 {
				result[key][k] = col + "%"
			}
		}
	}

	//充满表格补充空字符串
	for i := 0; i < 13; i++ {
		for j := 0; j < 14; j++ {
			if j >= len(result[i]) {
				result[i] = append(result[i], "")
			}
		}
	}

	response.Success(ctx, &response.SuccessResponse{
		Data: map[string]interface{}{
			"result": result,
		},
	})
}
