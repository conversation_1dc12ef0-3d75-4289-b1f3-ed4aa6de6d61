package request

import (
	"github.com/thedevsaddam/govalidator"
)

type Tracking struct {
}

func (l *Tracking) Rules() govalidator.MapData {
	return govalidator.MapData{
		"shop": []string{"required"},
	}
}

func (l *Tracking) Messages() govalidator.MapData {
	return govalidator.MapData{
		"shop": []string{"required:Your store is wrong"},
	}
}

type TrackRequest struct {
	Shop       string `form:"shop" validate:"required,max:255"`
	Order      string `form:"order" validate:"max=255"`
	Email      string `form:"email" validate:"max=255"`
	Token      string `form:"token" validate:"max=255"`
	Phone      string `form:"phone" validate:"max=255"`
	PhoneEmail string `form:"phone_email" validate:"max=255"`
	Callback   string `form:"callback" validate:"max=255"`
	Nums       string `form:"nums" validate:"max=255"`
	IsEmbed    bool   `form:"is_embed" validate:"bool" default:"false"`
	IsBase64   bool   `form:"is_base64" validate:"bool" default:"false"`
	IsAjax     bool   `form:"is_ajax" validate:"bool" default:"false"`
	IsPlugin   bool   `form:"is_plugin" validate:"bool" default:"false"`
	IsPreview  bool   `form:"is_preview" validate:"bool" default:"false"`
	PageLang   string `form:"page_lang" validate:"max=255"`
}

func (t *TrackRequest) Rules() govalidator.MapData {
	return govalidator.MapData{
		"shop":       []string{"required", "max:255"},
		"order":      []string{"max:255"},
		"email":      []string{"max:255"},
		"token":      []string{"max:255"},
		"phone":      []string{"max:255"},
		"callback":   []string{"max:255"},
		"nums":       []string{"max:255"},
		"is_embed":   []string{"bool"},
		"is_base64":  []string{"bool"},
		"is_ajax":    []string{"bool"},
		"is_plugin":  []string{"bool"},
		"is_preview": []string{"bool"},
	}
}

type RecommendProduct struct {
}

func (w *RecommendProduct) Rules() govalidator.MapData {
	return govalidator.MapData{
		"shop":    []string{"required", "max:255"},
		"product": []string{"required", "numeric", "max:50"},
	}
}

type CollectionProduct struct {
}

func (w *CollectionProduct) Rules() govalidator.MapData {
	return govalidator.MapData{
		"shop":       []string{"required", "max:255"},
		"collection": []string{"required", "numeric", "max:50"},
	}
}

type OrderProduct struct {
}

func (w *OrderProduct) Rules() govalidator.MapData {
	return govalidator.MapData{
		"shop":  []string{"required", "max:255"},
		"order": []string{"required", "max:100"},
	}
}
