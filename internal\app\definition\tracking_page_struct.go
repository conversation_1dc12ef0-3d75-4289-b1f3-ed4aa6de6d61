package definition

import (
	"time"

	"github.com/shopspring/decimal"

	"tmshopify/config"
	"tmshopify/internal/app/entity"
	"tmshopify/pkg/domain"
	"tmshopify/store/models"
)

type TrackPageProduct struct {
	Id                 int64           `json:"id"`
	Title              string          `json:"title"`
	Url                string          `json:"url"`
	Price              decimal.Decimal `json:"price"`
	Img                string          `json:"img"`
	Vendor             string          `json:"vendor,omitempty"`
	HandleTitle        string          `json:"handle_title,omitempty"`
	ProductType        string          `json:"product_type,omitempty"`
	PublishedTime      time.Time       `json:"published_time,omitempty"`
	ProductCreatedTime time.Time       `json:"product_created_time,omitempty"`
	ComparePrice       decimal.Decimal `json:"compare_price,omitempty"`
}

type OrderRecord struct {
	OrderId              string                          ` json:"order_id"`
	UserId               int                             ` json:"user_id"`
	OrderName            string                          `json:"order_name"`
	CustomerName         string                          `json:"customer_name"`
	CustomerEmail        string                          `json:"customer_email"`
	CustomerPhone        string                          `json:"customer_phone"`
	Product              string                          `json:"product"`
	Currency             string                          `json:"currency"`
	OrderNote            string                          `json:"order_note"`
	OrderSource          string                          `json:"order_source"`
	OrderCreateTime      int                             `json:"order_create_time"`
	FulfillmentStatus    int                             `json:"fulfillment_status"`
	FinancialStatus      int                             `json:"financial_status"`
	RecipientPhone       string                          `json:"recipient_phone"`
	RecipientZip         string                          `json:"recipient_zip"`
	RecipientCountryCode string                          `json:"recipient_country_code"`
	ShippingAddress      string                          `json:"shipping_address"`
	CustomTrackStatus    config.StatusNodeNumber         `json:"custom_track_status"`
	CustomStatusTime     map[config.StatusNodeNumber]int `json:"custom_status_time"`
	IsDelete             int                             `json:"is_delete"`
	DataSource           int                             `json:"data_source"`
	WhetherPay           int                             `json:"whether_pay"`
	Destination          string                          `json:"destination"`
	OrderFulfillTime     int                             `json:"order_fulfill_time"`
}

type TrackNumberData struct {
	ID                 int        `json:"id"`
	OrderId            string     `json:"order_id"`
	TrackId            int        `json:"track_id"`
	TrackNumber        string     `json:"track_number"`
	Courier            string     `json:"courier"`
	TrackStatus        uint       `json:"track_status"`
	AlterTrackStatus   uint       `json:"alter_track_status"`
	TransitTime        uint       `json:"transit_time"`
	LastDate           uint       `json:"last_date"`
	LastEvent          string     `json:"last_event"`
	Destination        string     `json:"destination"`
	OrderName          string     `json:"order_name"`
	CustomerName       string     `json:"customer_name"`
	CustomerEmail      string     `json:"customer_email"`
	CustomerPhone      string     `json:"customer_phone"`
	FulfillmentStatus  int        `json:"fulfillment_status"`
	FinancialStatus    int        `json:"financial_status"`
	OrderCreateTime    int        `json:"order_create_time"`
	FulfillmentId      string     `json:"fulfillment_id"`
	OrderFulfillTime   int        `json:"order_fulfill_time"`
	Notes              string     `json:"notes"`
	IsDelete           int        `json:"is_delete"`
	UpdatedAt          time.Time  `json:"updated_at"`
	TrackInfos         TrackInfos `json:"track_infos"`
	TrackInfoCreatedAt int        `json:"track_info_created_at"`
}

type TrackingInfoData struct {
	CourierCode             string      `json:"courier_code"`
	CourierPhone            string      `json:"courier_phone"`
	Weblink                 string      `json:"weblink"`
	TrackingLink            string      `json:"tracking_link"`
	ReferenceNumber         string      `json:"reference_number"`
	ReceivedDate            string      `json:"received_date"`
	DispatchedDate          string      `json:"dispatched_date"`
	DepartedAirportDate     string      `json:"departed_airport_date"`
	ArrivedAbroadDate       string      `json:"arrived_abroad_date"`
	CustomsReceivedDate     string      `json:"customs_received_date"`
	ArrivedDestinationDate  string      `json:"arrived_destination_date"`
	DestinationTrackCourier string      `json:"destination_track_courier,omitempty"`
	DestinationCourier      string      `json:"destination_courier,omitempty"`
	DestinationTrackNumber  string      `json:"destination_track_number,omitempty"`
	TrackInfo               []TrackInfo `json:"trackinfo"`
}

type TrackInfos struct {
	ID                   int              `json:"id"`
	OriginalCountry      string           `json:"original_country"`
	DestinationCountry   string           `json:"destination_country"`
	ExpectedDeliveryTime time.Time        `json:"expected_delivery_time"`
	DestinationInfo      TrackingInfoData `json:"destination_info"`
	OriginInfo           TrackingInfoData `json:"origin_info"`
}
type RootData struct {
	Error        int                       `json:"error"`
	IsNoTrack    int                       `json:"is_no_track"`
	Product      string                    `json:"product"`
	OrderNumber  string                    `json:"order_number"`
	HideCainiao  bool                      `json:"hide_cainiao" default:"false"`
	Email        string                    `json:"email"`
	ConfigData   map[string]interface{}    `json:"config"`
	Tracking     []TrackingData            `json:"tracking"`
	StatusKeyArr []config.StatusNodeNumber `json:"status_key_arr"`
}
type TrackPageGlobalData struct {
	User                  models.Users
	TrackSettingEntity    entity.TrackSettingEntityStruct
	TrackThemeEntity      entity.TrackThemeEntityStruct
	TrackRequestDomain    domain.TrackRequestDomain
	PermissionDomain      domain.PermissionDomain
	OrderRecord           OrderRecord
	CustomStatusTime      map[config.StatusNodeNumber]int
	ShippingAddressResult HandleShippingAddressResult
	MapLocationResult     MapLocation
	NumberMessage         []TrackNumberData
	RootData              RootData
	TrackingData          []TrackingData
	ConfigData            map[string]interface{}
	IsPlugin              bool
	EddSetting            entity.EddSettingEntityStruct
}

type StatusNum struct {
	Status            config.StatusNodeNumber `json:"status"`
	Name              string                  `json:"name"`
	StatusDescription string                  `json:"StatusDescription"`
}
type LocationInfo struct {
	Status      string `json:"checkpoint_status"`
	Description string `json:"description"`
	Location    string `json:"location"`
}

type MapLocation struct {
	Lat         float32      `json:"lat"`
	Lon         float32      `json:"lon"`
	Info        LocationInfo `json:"info"`
	Origin      string       `json:"origin"`
	Destination string       `json:"destination"`
}

type TrackInfo struct {
	CheckpointDate              string `json:"checkpoint_date"`
	TrackingDetail              string `json:"tracking_detail"`
	Location                    string `json:"location"`
	CheckpointDeliveryStatus    string `json:"checkpoint_delivery_status"`
	CheckpointDeliverySubstatus string `json:"checkpoint_delivery_substatus"`
}
type ReturnInfo struct {
	LocationMap string `json:"location_map"`
	TrackInfo
	StatusDescription string `json:"status_description"`
	Details           string `json:"details"`
	CheckpointStatus  string `json:"checkpoint_status"`
	Substatus         string `json:"substatus"`
	Date              string `json:"date"`
	Name              string `json:"name"`
	Icon              string `json:"icon"`
}
type TrackingData struct {
	StatusNode            map[config.StatusNodeNumber]ReturnInfo `json:"status_node"`
	StatusNum             StatusNum                              `json:"status_num"`
	Map                   MapLocation                            `json:"map"`
	Trackinfo             []ReturnInfo                           `json:"trackinfo"`
	Countryname           string                                 `json:"countryname"`
	Destinationcountry    string                                 `json:"destinationcountry"`
	StausDataNum          config.StatusNodeNumber                `json:"stausDataNum"`
	ScheduledDeliveryDate string                                 `json:"ScheduledDeliveryDate"`
	ShippingTimeShow      int                                    `json:"shipping_time_show"`
	ShippingTimeCon       string                                 `json:"shipping_time_con"`
	Status                string                                 `json:"status"`
	OriginPhone           string                                 `json:"OriginPhone"`
	Url                   string                                 `json:"url"`
	CompanyCode           string                                 `json:"companyCode"`
	CarrierName           string                                 `json:"carrier_name"`
	Img                   string                                 `json:"img"`
	Destination           string                                 `json:"destination"`
	Original              string                                 `json:"original"`
	TrackNumber           string                                 `json:"track_number"`
	Title                 []OrderProductImgData                  `json:"title"`
	OrderCreateTime       interface{}                            `json:"order_create_time"`
	OrderFulfillTime      interface{}                            `json:"order_fulfill_time"`
	Notes                 string                                 `json:"notes"`
	Comment               string                                 `json:"comment"`
	ShippingMap           HandleShippingAddressResult            `json:"shipping_map"`
	Courier               struct {
		Name string `json:"name"`
		Code string `json:"code"`
		Url  string `json:"url"`
		Img  string `json:"img"`
	} `json:"courier"`
	DestinationCourier struct {
		Name        string `json:"name"`
		Code        string `json:"code"`
		Url         string `json:"url"`
		Img         string `json:"img"`
		TrackNumber string `json:"track_number"`
	} `json:"destination_courier"`
}
type HandleShippingAddressResult struct {
	Lat       float32 `json:"lat"`
	Lon       float32 `json:"lon"`
	Location  string  `json:"location"`
	Location1 string  `json:"location1"`
}

type OrderProductImgData struct {
	Title     string `json:"title"`
	TitleImg  string `json:"title_img"`
	ProductId string `json:"product_id"`
	Quantity  int    `json:"quantity"`
	Variant   string `json:"variant"`
}

type StatusData struct {
	Name        string
	Description string
	Key         config.StatusNodeNumber
	Time        int
}
