package handlers

import (
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"tmshopify/pkg/jobs"
	"tmshopify/pkg/shopify"
	"tmshopify/pkg/shopify/graphql"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type OrderProductHandler struct {
}

func (o *OrderProductHandler) Handle(user models.Users, OrderId string) {
	UserId := user.ID
	message := fmt.Sprintf("order product handler %s %d", OrderId, UserId)
	if UserId == 0 || OrderId == "" {
		logrus.Error("empty user or order " + message)
		return
	}
	queueSentKey := fmt.Sprintf("save_product_%d_%s", UserId, OrderId)
	inQueue, err := database.RS.Get(queueSentKey)
	if err == nil && string(inQueue) == "1" {
		logrus.Warn("order has send in queue " + message)
		return
	}
	err = database.RS.SetWithExpire(queueSentKey, "1", time.Second*120)
	if err != nil {
		logrus.Error("set queue sent key fail " + message)
		return
	}
	unSyncProductKey := fmt.Sprintf("jump_user_because_is_no_auth_%d", UserId)
	unSyncProduct, _ := database.RS.Get(unSyncProductKey)
	if string(unSyncProduct) == "1" {
		logrus.Warn("jump_user_because_is_no_auth " + message)
		return
	}
	session := user.AuthSession()
	client := shopify.API.NewAuthClient(session, shopify.WithLogger())
	var variables = struct {
		OrderId string `json:"orderId"`
	}{
		OrderId: fmt.Sprintf("gid://shopify/Order/%s", OrderId),
	}
	fulfillments := new(graphql.OrderFulfillments)
	err = client.GraphQL.Query(graphql.QueryOrderFulfillmentItems, variables, &fulfillments)

	if err != nil {
		if strings.Contains(err.Error(), "Access denied for product field.") {
			database.RS.SetWithExpire(fmt.Sprintf("jump_user_because_is_no_auth_%d", UserId), "1", time.Hour*24)
		}
		logrus.WithFields(map[string]interface{}{
			"user_id":  UserId,
			"order_id": OrderId,
		}).Errorf(" Fetch Product Fail %s", err.Error())
		return
	}

	if len(fulfillments.Fulfillments) > 0 {
		var products []graphql.ProductInfo
		for _, fulfillment := range fulfillments.Fulfillments {
			for _, value := range fulfillment.FulfillmentLineItems.Nodes {
				// 检查 line_items 是否不为空
				value.Name = fulfillment.Name
				product := shopify.ExtractProductFromFulfillmentItems(value, fulfillment.ID, OrderId)
				products = append(products, product)

				if len(products) > 0 {
					batchJob := jobs.BatchSaveProductJob{
						BatchSaveProductJobStruct: jobs.BatchSaveProductJobStruct{
							User:     user,
							Products: products,
						},
					}
					batchJob.DispatchSync()
				}
			}
		}
	}
}
