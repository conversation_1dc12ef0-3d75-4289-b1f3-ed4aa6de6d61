package shopify

import "time"

// Shop represents a Shopify shop
type Shop struct {
	ID                                   int64     `json:"id"`
	Address1                             string    `json:"address1"`
	Address2                             string    `json:"address2"`
	CheckoutApiSupported                 bool      `json:"checkout_api_supported"`
	City                                 string    `json:"city"`
	Country                              string    `json:"country"`
	CountryCode                          string    `json:"country_code"`
	CountryName                          string    `json:"country_name"`
	CountyTaxes                          *bool     `json:"county_taxes"`
	CreatedAt                            time.Time `json:"created_at"`
	CustomerEmail                        string    `json:"customer_email"`
	Currency                             string    `json:"currency"`
	Domain                               string    `json:"domain"`
	EnabledPresentmentCurrencies         []string  `json:"enabled_presentment_currencies"`
	EligibleForCardReaderGiveaway        bool      `json:"eligible_for_card_reader_giveaway"`
	EligibleForPayments                  bool      `json:"eligible_for_payments"`
	Email                                string    `json:"email"`
	Finances                             bool      `json:"finances"`
	ForceSsl                             bool      `json:"force_ssl"`
	GoogleAppsDomain                     *string   `json:"google_apps_domain"`
	GoogleAppsLoginEnabled               *bool     `json:"google_apps_login_enabled"`
	HasDiscounts                         bool      `json:"has_discounts"`
	HasGiftCards                         bool      `json:"has_gift_cards"`
	HasStorefront                        bool      `json:"has_storefront"`
	IanaTimezone                         string    `json:"iana_timezone"`
	Latitude                             float64   `json:"latitude"`
	Longitude                            float64   `json:"longitude"`
	MoneyFormat                          string    `json:"money_format"`
	MoneyInEmailsFormat                  string    `json:"money_in_emails_format"`
	MoneyWithCurrencyFormat              string    `json:"money_with_currency_format"`
	MoneyWithCurrencyInEmailsFormat      string    `json:"money_with_currency_in_emails_format"`
	MultiLocationEnabled                 bool      `json:"multi_location_enabled"`
	MyshopifyDomain                      string    `json:"myshopify_domain"`
	Name                                 string    `json:"name"`
	PasswordEnabled                      bool      `json:"password_enabled"`
	Phone                                *string   `json:"phone"`
	PlanDisplayName                      string    `json:"plan_display_name"`
	PreLaunchEnabled                     bool      `json:"pre_launch_enabled"`
	PlanName                             string    `json:"plan_name"`
	PrimaryLocale                        string    `json:"primary_locale"`
	PrimaryLocationId                    int       `json:"primary_location_id"`
	Province                             string    `json:"province"`
	ProvinceCode                         string    `json:"province_code"`
	RequiresExtraPaymentsAgreement       bool      `json:"requires_extra_payments_agreement"`
	SetupRequired                        bool      `json:"setup_required"`
	ShopOwner                            string    `json:"shop_owner"`
	Source                               *string   `json:"source"`
	TaxesIncluded                        *bool     `json:"taxes_included"`
	TaxShipping                          *bool     `json:"tax_shipping"`
	Timezone                             string    `json:"timezone"`
	TransactionalSmsDisabled             bool      `json:"transactional_sms_disabled"`
	UpdatedAt                            time.Time `json:"updated_at"`
	WeightUnit                           string    `json:"weight_unit"`
	Zip                                  string    `json:"zip"`
	MarketingSmsConsentEnabledAtCheckout bool      `json:"marketing_sms_consent_enabled_at_checkout"`
}
