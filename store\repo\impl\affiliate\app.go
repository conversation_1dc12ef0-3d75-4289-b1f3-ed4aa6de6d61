package affiliate

import (
	"tmshopify/pkg/utils/paginate"
	"tmshopify/store/database"
	"tmshopify/store/models/affiliate"
)

type AppRepository struct {
}

func (a *AppRepository) Count(search string) int64 {
	var count int64
	database.DB.Model(&affiliate.Apps{}).Where(search).Count(&count)
	return count
}

func (a *AppRepository) Paginate(paginateParam *paginate.Param) ([]affiliate.Apps, int64, error) {
	var items []affiliate.Apps
	total := a.Count("id != 0")
	result := database.DB.Scopes(paginate.ORMScope(paginateParam)).Order("created_at desc").Find(&items)
	return items, total, result.Error
}
