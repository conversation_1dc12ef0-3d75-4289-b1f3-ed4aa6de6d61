package middleware

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"io"
	"net/http"
	"tmshopify/config"
	"tmshopify/pkg/shopify/webhook"
	"tmshopify/server/response"
	"tmshopify/store/database"
)

func verifyWebhook(data []byte, hmacHeader string) bool {
	var shopifyApiSecret = config.Get().ShopifyApiSecret
	hashMac := hmac.New(sha256.New, []byte(shopifyApiSecret))
	hashMac.Write(data)
	computedHmac := base64.StdEncoding.EncodeToString(hashMac.Sum(nil))
	return hmac.Equal([]byte(computedHmac), []byte(hmacHeader))
}

func ShopifyWebhook() gin.HandlerFunc {
	return func(c *gin.Context) {
		hmacHeader := c.Request.Header.Get(webhook.XShopifyHmac)
		shop := c.Request.Header.Get(webhook.XShopifyDomain)
		data, err := c.GetRawData()
		topic := c.Request.Header.Get(webhook.XShopifyTopic)
		logrus.WithFields(logrus.Fields{
			"webhook_id": c.Request.Header.Get(webhook.XShopifyWebhookId),
			"version":    c.Request.Header.Get(webhook.XShopifyApiVersion),
			"topic":      topic,
			"shop":       shop,
			"trigger_at": c.Request.Header.Get(webhook.XShopifyTriggeredAt),
		}).Warn("webhook incoming")
		if len(shop) == 0 || err != nil || !verifyWebhook(data, hmacHeader) {
			message := "verify fail:" + c.Request.Header.Get(webhook.XShopifyWebhookId) + " shop:" + shop
			if err != nil {
				message += " error:" + err.Error()
			}
			logrus.Warn(message)
			response.Fail(c, &response.FailResponse{
				Code:    http.StatusUnauthorized,
				Message: "Webhook verify fail!",
			})
			c.Abort()
		}
		_ = database.RS.HSet(config.CacheKey.WebhookEntityLogCache, topic, string(data))
		c.Request.Body = io.NopCloser(bytes.NewBuffer(data))
		c.Next()
	}
}

func ShopifyFlowWebhook() gin.HandlerFunc {
	return func(c *gin.Context) {
		hmacHeader := c.Request.Header.Get(webhook.XShopifyHmac)
		data, err := c.GetRawData()
		logrus.Warn("flow webhook incoming :" + string(data))
		if err != nil || !verifyWebhook(data, hmacHeader) {
			message := "flow webhook verify fail:" + string(data)
			if err != nil {
				message += " error:" + err.Error()
			}
			logrus.Error(message)
			response.Fail(c, &response.FailResponse{
				Code:    http.StatusUnauthorized,
				Message: "Webhook verify fail!",
			})
			c.Abort()
		}
		c.Request.Body = io.NopCloser(bytes.NewBuffer(data))
		c.Next()
	}
}
