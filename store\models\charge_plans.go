package models

import (
	"gorm.io/gorm"
)

type BillingCycle string

const (
	MONTHLY BillingCycle = "monthly"
	ANNUAL  BillingCycle = "annual"
	ONETIME BillingCycle = "onetime"
	HYBRID  BillingCycle = "hybrid"
)

type PlanType string

const (
	SYSTEM PlanType = "system"
	CUSTOM PlanType = "custom"
)

type ChargePlans struct {
	Name                string         `gorm:"column:name;type:VARCHAR(255);comment:套餐名称;not null;" json:"name"`
	Description         string         `gorm:"column:description;type:TEXT(65535);comment:套餐描述;default:NULL;" json:"description"`
	PlanType            PlanType       `gorm:"column:plan_type;type:ENUM('system', 'custom');comment:套餐类型，标准（system）或自定义（custom）;not null;default:system;" json:"plan_type"`
	BillingCycle        BillingCycle   `gorm:"column:billing_cycle;type:ENUM('monthly', 'annual', 'onetime', 'hybrid');comment:计费周期;not null;default:hybrid;" json:"billing_cycle"`
	Price               float32        `gorm:"column:price;type:DECIMAL(10, 2);comment:套餐价格;not null;default:0.00;" json:"price"`
	Quota               int            `gorm:"column:quota;type:INT(10) UNSIGNED;comment:套餐额度，如服务次数等;not null;default:0;" json:"quota"`
	ReplaceSystemPlanId *int           `gorm:"column:replace_system_plan_id;type:BIGINT(20) UNSIGNED;comment:替代的系统套餐ID（指向同表内部的标准套餐ID）;default:NULL;foreignKey:ReplaceSystemPlanId;references:ID" json:"replace_system_plan_id"`
	Version             Version        `gorm:"column:version;type:CHAR(6);comment:套餐版本;not null;default:202407;" json:"version"`
	Level               int            `gorm:"column:level;type:TINYINT(3) UNSIGNED;comment:权限等级;not null;default:0;" json:"level"`
	UserId              int            `gorm:"column:user_id;type:BIGINT(20) UNSIGNED;comment:用户ID，仅对 custom 类型套餐有效;default:NULL;" json:"user_id"`
	CreatedBy           int            `gorm:"column:created_by;type:BIGINT(20) UNSIGNED;comment:创建者ID，仅对 custom 类型套餐有效;default:NULL;" json:"created_by"`
	DeletedAt           gorm.DeletedAt `gorm:"column:deleted_at;type:TIMESTAMP;default:NULL;" json:"deleted_at"`
	Model
}

func (ChargePlans) TableName() string {
	return "charge_plans"
}
