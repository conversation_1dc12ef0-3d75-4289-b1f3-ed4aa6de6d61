package users

import (
	"context"
	"fmt"

	"github.com/redis/go-redis/v9"

	"tmshopify/internal/domain/repo/users"
)

var _ users.UserCacheRepository = (*userCacheImpl)(nil)

type userCacheImpl struct {
	redisClient redis.UniversalClient
}

// NewUserCacheRepository 创建用户缓存实例
func NewUserCacheRepository(redisClient redis.UniversalClient) users.UserCacheRepository {
	return &userCacheImpl{redisClient: redisClient}
}

// GetTimezone 获取用户时区
func (i *userCacheImpl) GetTimezone(ctx context.Context, id int) (string, error) {
	key := fmt.Sprintf("tmshopify_database_UserSetting_%d", id)
	field := "user_time_zone"

	return i.redisClient.HGet(ctx, key, field).Result()
}
