package impl

import (
	"encoding/json"
	"tmshopify/config"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type TrackPageSetting struct {
	Helper
	trackPageThemeRep *TrackPageTheme
}

func (t *TrackPageSetting) FirstByUserId(userId int) (models.TrackPageSettings, error) {
	var trackPageSettings models.TrackPageSettings
	result := database.DB.Model(&trackPageSettings).Where("user_id = ?", userId).Select("theme_id").First(&trackPageSettings)
	return trackPageSettings, result.Error
}

func (t *TrackPageSetting) GetUserTrackPage(userId int, theme models.Theme) (models.TrackPageSettings, error) {
	if theme == "" {
		theme = models.ThemeClassic
	}
	var m models.TrackPageSettings

	result := database.DB.Model(models.TrackPageSettings{}).Preload("ActiveTheme").Where("user_id = ?", userId).First(&m)
	if result.Error != nil {
		activeTheme := t.trackPageThemeRep.CreateByUserIdAndCode(userId, theme)
		defaultEstimate, _ := json.Marshal(config.DefaultEstimate)
		trackSetting := models.TrackPageSettings{
			TrackLink:             "",
			UserId:                userId,
			EstimatedDeliveryTime: string(defaultEstimate),
			SeoTitle:              "",
			SeoDesc:               "",
			HideSetting:           "",
			ShowCopyRight:         0,
			IsNoJq:                0,
			ThemeId:               activeTheme.ID,
		}
		result := database.DB.Create(&trackSetting)
		if result.Error != nil {
			return trackSetting, result.Error
		}
		trackSetting.ActiveTheme = activeTheme
		return trackSetting, nil
	}
	return m, result.Error
}

func (t *TrackPageSetting) GetUserActiveTheme(userId int) (models.TrackPageThemes, error) {
	var trackPageSetting models.TrackPageSettings
	err := database.DB.Model(&models.TrackPageSettings{}).Preload("ActiveTheme").Where("user_id =?", userId).Select("theme_id").First(&trackPageSetting).Error
	if err != nil {
		return t.trackPageThemeRep.CreateByUserIdAndCode(userId, models.ThemeClassic), err
	}
	return trackPageSetting.ActiveTheme, nil
}

func (t *TrackPageSetting) GetEstimatedSetting(userId int) (string, error) {
	var estimatedDeliveryTime []string
	result := database.DB.Model(models.TrackPageSettings{}).Where("user_id = ?", userId).Pluck("estimated_delivery_time", &estimatedDeliveryTime)
	if result.RowsAffected > 0 && result.Error == nil {
		return estimatedDeliveryTime[0], nil
	}
	return "", result.Error
}

// GetUserTrackPageMessage 根据 user id 获取 track page设置
func (t *TrackPageSetting) GetUserTrackPageMessage(userId int) *models.TrackPageSettings {
	var m *models.TrackPageSettings
	database.DB.Model(&models.TrackPageSettings{}).Preload("User.ChargeGroupStore").Preload("User.UserPermissions").Where("user_id = ?", userId).Select("id, user_id, track_link, estimated_delivery_time").First(&m)
	return m
}

func (t *TrackPageSetting) GetUserTrackLink(userId int) string {
	var trackLink string
	database.DB.Model(models.TrackPageSettings{}).Where("user_id = ?", userId).Select("track_link").Scan(&trackLink)
	return trackLink
}
