package mail

import (
	"context"
	"fmt"
	"time"

	"tmshopify/config"
	"tmshopify/pkg/utils/helper"
	"tmshopify/store/database"

	"github.com/mailgun/mailgun-go/v5"
	"github.com/sirupsen/logrus"
)

type Mailgun struct {
	mailer *mailgun.Client
	domain string
}

func generateVerifyCode(key string) (string, error) {
	// 生成随机码
	code := helper.GetRandomString(5)

	// 为方便开发，本地环境使用固定验证码
	if helper.IsLocal() {
		code = config.Get().DebugCode
	}
	logrus.Debugf("生成验证码%s", code)
	// 将验证码及 KEY（邮箱或手机号）存放到 Redis 中并设置过期时间
	expireTime := time.Minute * time.Duration(15)
	err := database.RS.SetWithExpire(key, code, expireTime)
	if err != nil {
		return "", err
	}
	return code, nil
}

func (m Mailgun) SendVerifyCode(to string, from string, fromName string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()
	fromUser := config.Get().MailFromName
	if len(fromName) > 0 {
		fromUser = fromName
	}
	fromAddress := config.Get().MailFromAddress
	if len(from) > 0 {
		fromAddress = from
	}
	// 将验证码及 KEY（邮箱或手机号）存放到 Redis 中并设置过期时间
	code, err := generateVerifyCode("adminLogin:" + to)
	if err != nil {
		return err
	}
	content := fmt.Sprintf("<h1>Your email verify code is %s </h1>", code)
	message := mailgun.NewMessage(
		m.domain,
		fmt.Sprintf("%s <%s>", fromUser, fromAddress),
		"Email Verify Code",
		fmt.Sprintf("Your email verify code is %s", code),
		to,
	)
	message.SetHTML(content)
	resp, err := m.mailer.Send(ctx, message)
	if err != nil {
		logrus.Error(resp)
		return err
	}
	logrus.WithFields(logrus.Fields{"message": resp.Message, "id": resp.ID}).Warning("send result record")
	return nil
}

func (m Mailgun) SendResetLink(to string, token string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()
	fromUser := config.Get().MailFromName
	fromAddress := config.Get().MailFromAddress

	// 构造重置链接
	resetLink := fmt.Sprintf("%s/auth/reset-passwd?pass=%s", "https://data-center.trackingmore.net", token)
	// 邮件内容
	content := fmt.Sprintf(`
        <html>
			<body>
				<h1>Reset Password Request</h1>
				<p>Hello,</p>
				<p>We received a request to reset your password. Please click the link below to proceed with resetting your password:</p>
				<a href="%s" target="_blank">Reset Password</a>
				<p>This link will expire in <strong>15 minutes</strong>. If you did not request to reset your password, please ignore this email or contact support if you have concerns.</p>
				<p>Thank you,</p>
				<p>The TrackingMore Support Team</p>
			</body>
		</html>
    `, resetLink)

	// 构建邮件消息
	message := mailgun.NewMessage(
		m.domain,
		fmt.Sprintf("%s <%s>", fromUser, fromAddress),
		"Reset Password Request",
		fmt.Sprintf(`Reset Password Request

Hello,

We received a request to reset your password. Please use the link below to reset your password:

%s

This link will expire in 15 minutes. If you did not request this, please ignore the email or contact support if you have concerns.

Thank you,
The [Your Company] Support Team`, resetLink), // 文本模式备用
		to,
	)
	message.SetHTML(content)

	resp, err := m.mailer.Send(ctx, message)
	if err != nil {
		logrus.WithFields(logrus.Fields{"error": err}).Error("发送邮件失败")
		return err
	}

	logrus.WithFields(logrus.Fields{"message": resp.Message, "id": resp.ID}).Info("重置密码邮件发送成功")
	return nil

}

func NewMailgun() *Mailgun {
	mg := mailgun.NewMailgun(config.Get().MailgunApiKey)
	return &Mailgun{mailer: mg, domain: config.Get().MailgunDomain}
}
