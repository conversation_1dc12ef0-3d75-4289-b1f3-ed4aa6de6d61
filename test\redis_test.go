package main

import (
	"fmt"
	"testing"
	"tmshopify/config"
	"tmshopify/pkg/utils/helper"
	"tmshopify/store/database"
)

func TestRedis(t *testing.T) {
	if err := config.Load("../config/config.yaml"); err != nil {
		fmt.Println("Failed to load configuration")
		return
	}
	database.InitRedis()
	redis := database.RS
	res := redis.Set("test", "1")
	t.Log(res)
}

func TestRandomString(t *testing.T) {
	str := helper.GetRandomString(5)
	t.Log(str)
}
