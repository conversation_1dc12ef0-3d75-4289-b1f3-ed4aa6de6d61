package config

import (
	"context"
	"encoding/json"

	"google.golang.org/api/option"
	"google.golang.org/api/sheets/v4"
)

// googleConfig for google sheet
type googleConfig struct {
	Credentials googleCredentials `mapstructure:"credentials"`
}

type googleCredentials struct {
	Type                string `json:"type" mapstructure:"type"`
	ProjectID           string `json:"project_id" mapstructure:"project_id"`
	PrivateKeyID        string `json:"private_key_id" mapstructure:"private_key_id"`
	PrivateKey          string `json:"private_key" mapstructure:"private_key"`
	ClientEmail         string `json:"client_email" mapstructure:"client_email"`
	ClientID            string `json:"client_id" mapstructure:"client_id"`
	AuthURI             string `json:"auth_uri" mapstructure:"auth_uri"`
	TokenURI            string `json:"token_uri" mapstructure:"token_uri"`
	AuthProviderCertURL string `json:"auth_provider_x509_cert_url" mapstructure:"auth_provider_cert_url"`
	ClientCertURL       string `json:"client_x509_cert_url" mapstructure:"client_cert_url"`
	UniverseDomain      string `json:"universe_domain" mapstructure:"universe_domain"`
}

// NewGoogleSheet return google sheets service
func NewGoogleSheet(name string) (*sheets.Service, error) {
	setting := &googleConfig{}
	err := conf.ReadSection(name, setting)
	if err != nil {
		return nil, err
	}

	b, err := json.Marshal(&setting.Credentials)
	if err != nil {
		return nil, err
	}

	srv, err := sheets.NewService(context.Background(), option.WithCredentialsJSON(b))
	if err != nil {
		return nil, err
	}

	return srv, nil
}
