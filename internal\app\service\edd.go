package service

import (
	"encoding/json"

	"tmshopify/internal/app/entity"
	"tmshopify/pkg/domain"
	"tmshopify/store/database"
	"tmshopify/store/repo/impl"
	"tmshopify/store/repo/impl/order"
)

type EddService struct {
	UserSettingRepo      *impl.UserSetting
	TrackPageSettingRepo *impl.TrackPageSetting
	OrderEstimateRep     *order.EstimateRepoImpl
	ShopifyCountryRepo   *impl.ShopifyCountry
}

type EddInterface interface {
	CalculateEddByOrderAndDestinationByEntities(userId int, calculateEntity entity.CalculateEntity, estimatedSetting entity.EddSettingEntityStruct) struct {
		StartShow int64
		EndShow   int64
	}
}

var _ EddInterface = &EddService{}

func (e *EddService) CalculateEddByOrderAndDestinationByEntities(userId int, calculateEntity entity.CalculateEntity, estimatedSetting entity.EddSettingEntityStruct) struct {
	StartShow int64
	EndShow   int64
} {
	var data struct {
		StartShow int64
		EndShow   int64
	}
	if userId == 0 {
		return data
	}
	if !estimatedSetting.Switch {
		return data
	}
	startTime := calculateEntity.OrderCreateTime
	if estimatedSetting.CalType == entity.EstimateCalFulfill {
		startTime = calculateEntity.OrderFulfillTime
	}
	if startTime == 0 {
		return data
	}

	//结合edd设置 获取预计到达时间
	eddDomain := domain.NewEddDomain().InitEddDomain(estimatedSetting, startTime, domain.Edd)
	edd := eddDomain.CalculateEstimateTime()
	if edd.IsZero() {
		return data
	}
	//开始判断订单目的国
	destinationInfo, err := e.ShopifyCountryRepo.GetShopifyCountryIdAndContinent(calculateEntity.Destination)
	if err != nil {
		return data
	}
	data = eddDomain.CalculateEddResultsByCalEntity(edd, calculateEntity, destinationInfo)
	return data
}

func containsCode(dataSets []entity.AvailableDataSet, code string) bool {
	for _, data := range dataSets {
		if data.Code == code {
			return true
		}
	}
	return false
}

type TrackCalculateData struct {
	UserId           int
	FulfillmentId    string
	TrackNumber      string
	Destination      string
	Courier          string
	OrderCreateTime  int
	OrderFulfillTime int
}

func (e *EddService) GetCalculateEntity(setting entity.EddSettingEntityStruct, track TrackCalculateData) entity.CalculateEntity {
	estimateCalculateEntity := entity.CalculateEntity{
		OrderCreateTime:  track.OrderCreateTime,
		OrderFulfillTime: track.OrderFulfillTime,
		TrackNumber:      track.TrackNumber,
		Destination:      track.Destination,
	}

	if track.TrackNumber != "" && setting.AiEdd == 1 && track.UserId != 0 {
		orderEstimate, err := e.OrderEstimateRep.GetEstimateByTrackingIndex(track.UserId, track.FulfillmentId, track.TrackNumber)
		if err != nil || orderEstimate.ID == "" {
			return estimateCalculateEntity
		}
		var couriers, countries []entity.AvailableDataSet
		couriersResult, _ := database.RS.Get(entity.AVAILABLE_COURIER_KEY)
		_ = json.Unmarshal(couriersResult, &couriers)
		countriesResult, _ := database.RS.Get(entity.AVAILABLE_COUNTRY_KEY)
		_ = json.Unmarshal(countriesResult, &countries)
		if containsCode(countries, track.Destination) && containsCode(couriers, track.Courier) {
			estimateCalculateEntity.OrderEstimate = orderEstimate
		}
	}

	return estimateCalculateEntity
}

func NewEddService() *EddService {
	return &EddService{
		UserSettingRepo:      &impl.UserSetting{},
		TrackPageSettingRepo: &impl.TrackPageSetting{},
		OrderEstimateRep:     &order.EstimateRepoImpl{},
		ShopifyCountryRepo:   &impl.ShopifyCountry{},
	}
}
