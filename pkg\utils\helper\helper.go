package helper

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"tmshopify/config"
)

func InSliceUint(needle uint, haystack []uint) bool {
	for _, value := range haystack {
		if value == needle {
			return true
		}
	}
	return false
}

// InSliceInt 判断元素是否在切片中
func InSliceInt(needle int, haystack []int) bool {
	for _, item := range haystack {
		if item == needle {
			return true
		}
	}
	return false
}

// UnderscoreName 驼峰式写法转为下划线写法
func UnderscoreName(name string) string {
	var buffer bytes.Buffer
	for i, r := range name {
		if unicode.IsUpper(r) {
			if i != 0 {
				buffer.WriteString("_")
			}
			buffer.WriteString(string(unicode.ToLower(r)))
		} else {
			buffer.WriteString(string(r))
		}
	}

	return buffer.String()
}

// CamelName 下划线写法转为驼峰写法
func CamelName(name string) string {
	name = strings.Replace(name, "_", " ", -1)
	caser := cases.Title(language.English)
	name = caser.String(name)
	return strings.Replace(name, " ", "", -1)
}

// GetRandomString 随机生成指定位数的大写字母和数字的组合
func GetRandomString(l int) string {
	str := "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	randomBytes := []byte(str)
	var result []byte
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < l; i++ {
		result = append(result, randomBytes[r.Intn(len(randomBytes))])
	}
	return string(result)
}

func IsLocal() bool {
	return config.Get().AppEnv == "local"
}

func IsProduction() bool {
	return config.Get().AppEnv == "production"
}

func IsTesting() bool {
	return config.Get().AppEnv == "testing"
}

func CallWilding(error string) {
	secret := "SEC3283300ab92509db664438f325b85331f316580e1c6691d442ebd827fea5a504"
	timestamp := time.Now().UnixMilli()
	// 将timestamp和secret拼接成签名字符串
	signStr := strconv.FormatInt(timestamp, 10) + "\n" + secret
	// 使用HmacSHA256算法计算签名
	hmacSha256 := hmac.New(sha256.New, []byte(secret))
	hmacSha256.Write([]byte(signStr))
	signBytes := hmacSha256.Sum(nil)
	// 进行Base64 encode
	signBase64 := base64.StdEncoding.EncodeToString(signBytes)
	// 进行urlEncode
	signUrlEncode := url.QueryEscape(signBase64)

	// 发送markdown消息
	urlP := fmt.Sprintf("https://oapi.dingtalk.com/robot/send?access_token=a843ec588eba6834b25f0dd8125f4a28503d431393041cbfea653a7d687e6ccf&timestamp=%d&sign=%s", timestamp, signUrlEncode)
	requestBody := fmt.Sprintf(`{"msgtype": "text","text": {"content":"%s"}}`, error)
	var jsonStr = []byte(requestBody)
	req, err := http.NewRequest("POST", urlP, bytes.NewBuffer(jsonStr))

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}

	resp, err := client.Do(req)
	body, _ := io.ReadAll(resp.Body)
	fmt.Println(string(body))
	if err != nil {
		fmt.Println(err)
		return
	}
	defer resp.Body.Close()
}

func HttpPostJSON(url string, payload interface{}) ([]byte, error) {
	// 创建HTTP客户端，并设置 10 秒超时时间
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// 将 payload 序列化为 JSON 格式
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}

	// 创建 POST 请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求 Content-Type 为 application/json
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	body, _ := io.ReadAll(resp.Body)

	defer resp.Body.Close()
	return body, nil
}

var trackingNumberRegex = regexp.MustCompile(`^[0-9a-zA-Z-_]{5,100}$`)

// TrackNumberRule 单号正则验证
func TrackNumberRule(tracking string) bool {
	return trackingNumberRegex.MatchString(tracking)
}

// MergeMaps 创建一个新的 map 用于保存合并后的值。返回新的 map。
func MergeMaps(destMap map[string]interface{}, sourceMap map[string]interface{}) map[string]interface{} {
	newMap := make(map[string]interface{})

	// 将目标 map 的元素复制到新 map 中
	for key, value := range destMap {
		newMap[key] = value
	}

	// 将源 map 中的元素合并到新 map 中
	for key, value := range sourceMap {
		newMap[key] = value
	}

	return newMap
}
