package impl

import (
	"fmt"
	"gorm.io/gorm"
	"tmshopify/config"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type ChargePlanRepository interface {
	HelperInterface
	GetPlanList(userId, isDelete int) []models.ChargePlans
	CreatePlan(m *models.ChargePlans) error
	UpdatePlan(m *models.ChargePlans) error
	ChangePlanStatus(id string, status string) error
	DeleteSystemPlanRs()
	DeleteCustomPlanRs(userId int)
}

type ChargePlanRepositoryImpl struct {
	Helper
}

func (c *ChargePlanRepositoryImpl) GetPlanList(userId, isDelete int) []models.ChargePlans {
	var plans []models.ChargePlans
	var query *gorm.DB
	if isDelete == 2 {
		query = database.DB.Model(&models.ChargePlans{}).Unscoped()
	} else if isDelete == 1 {
		query = database.DB.Model(&models.ChargePlans{}).Unscoped().Where("deleted_at IS NOT NULL")
	} else {
		query = database.DB.Model(&models.ChargePlans{})
	}
	if userId > 0 {
		query.Where("user_id = ?", userId).Find(&plans)
	} else {
		query.Find(&plans)
	}
	return plans
}

func (c *ChargePlanRepositoryImpl) CreatePlan(m *models.ChargePlans) error {
	if *m.ReplaceSystemPlanId == 0 {
		m.ReplaceSystemPlanId = nil
	}
	return database.DB.Create(m).Error
}

func (c *ChargePlanRepositoryImpl) UpdatePlan(m *models.ChargePlans) error {
	if *m.ReplaceSystemPlanId == 0 {
		m.ReplaceSystemPlanId = nil
	}
	return database.DB.Unscoped().Omit("created_at").Save(m).Error
}

func (c *ChargePlanRepositoryImpl) ChangePlanStatus(id string, status string) error {
	if status == "0" {
		return database.DB.Model(&models.ChargePlans{}).Unscoped().Where("id = ? ", id).Update("deleted_at", nil).Error
	} else {
		return c.DeleteById(&models.ChargePlans{}, id)
	}
}

func (c *ChargePlanRepositoryImpl) DeleteSystemPlanRs() {
	redisKey := config.CacheKey.ChargePlansV2
	_ = database.RS.Delete(redisKey)
}

func (c *ChargePlanRepositoryImpl) DeleteCustomPlanRs(userId int) {
	redisKey := fmt.Sprintf("%s%d", config.CacheKey.UserChargeCustomizePlansV2, userId)
	_ = database.RS.Delete(redisKey)
}
