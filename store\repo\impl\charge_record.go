package impl

import (
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type ChargeRecordRepository interface {
	CheckHasBeenActive(id string) bool
}

type ChargeRecordRepositoryImpl struct {
}

func (c *ChargeRecordRepositoryImpl) CheckHasBeenActive(id string) bool {
	var count int64
	database.DB.Model(&models.ChargeRecords{}).Where("plan_id = ? AND status = 1", id).Count(&count)
	return count > 0
}
