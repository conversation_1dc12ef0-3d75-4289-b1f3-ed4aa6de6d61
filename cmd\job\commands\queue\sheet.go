package queue

import (
	"errors"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/channelwill/athena/logger"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cobra"

	"tmshopify/cmd/job/commands/export"
	"tmshopify/internal/infras/config"
	"tmshopify/store/database"
)

func newGoogleSheetCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "sheet",
		Short: "Export Google Sheet",
		Long:  "Export Google Sheet with redis queue",
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()

			// connect db
			db, err := config.NewDB("db_conf")
			if err != nil {
				log.Fatalf("db init error:%v", err)
			}
			// 兼容旧配置
			database.DB = db

			// 兼容旧版本
			database.InitRedis()

			// connect redis
			redisUniversalClient, err := config.NewRedisClient("redis_conf")
			if err != nil {
				log.Fatalf("redis init error:%v", err)
			}

			// init google sheets service
			service, err := config.NewGoogleSheet("google_sheets")
			if err != nil {
				log.Fatalf("google_sheets init error: %v", err)
			}

			// user service
			user := initUserService(db, redisUniversalClient, service)

			for {
				r, err := redisUniversalClient.BRPop(cmd.Context(), time.Hour, export.GoogleSheetQueue).Result()
				if err != nil {
					if errors.Is(err, redis.Nil) {
						continue
					}
					return err
				}

				logger.Info(ctx, fmt.Sprintf("BRPOP %v", r))

				// 判断队列推送的数据是否有效
				if len(r) != 2 {
					logger.Error(ctx, fmt.Sprintf("invalid data. %v", r))
					continue
				}

				// 0: userid, 1: sheet
				v := strings.Split(r[1], ",")
				if len(v) != 2 {
					logger.Error(ctx, fmt.Sprintf("invalid data. %s", r[1]))
					continue
				}

				// parse userid
				id, err := strconv.Atoi(v[0])
				if err != nil {
					logger.Error(ctx, fmt.Sprintf("invalid userid. %s", r[1]))
					continue
				}

				if err := user.ExportSheet(ctx, id, v[1]); err != nil {
					logger.Error(ctx, fmt.Sprintf("export failed. %v", err))
					continue
				}
			}
		},
	}
}
