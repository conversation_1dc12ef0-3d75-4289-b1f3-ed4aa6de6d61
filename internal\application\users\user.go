package users

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/channelwill/athena/logger"

	"tmshopify/internal/domain/repo/google"
	"tmshopify/internal/domain/repo/orders"
	"tmshopify/internal/domain/repo/users"
	"tmshopify/pkg/domain"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

// TrackStatus 物流状态
type TrackStatus int8

const (
	// Pending 新增包裹正在查询中，请等待
	Pending TrackStatus = iota
	// NotFound 包裹暂时查询不到信息
	NotFound
	// InTransit 包裹在运输途中
	InTransit
	// OutForDelivery 包裹已到达当地地点或正在前往您家的途中
	OutForDelivery
	// Delivered 包裹已签收
	Delivered
	// Expired 包裹最后一条轨迹距今30天未更新。发生这种情况时，请联系快递员查询更多详细信息
	Expired
	// Undelivered 已尝试递送包裹，但由于某些原因而失败。通常，快递员会很快安排另一次送货
	Undelivered
	// Exception 包裹可能已退回给发件人、损坏、丢失或其他异常
	Exception
	// InfoReceived 快递员已收到包裹信息，正要取件
	InfoReceived
)

var trackStatusMap = map[TrackStatus]string{
	Pending:        "Pending",
	NotFound:       "Not Found",
	InTransit:      "In Transit",
	OutForDelivery: "Out for Delivery",
	Delivered:      "Delivered",
	Expired:        "Expired",
	Undelivered:    "Failed Attempt",
	Exception:      "Exception",
	InfoReceived:   "Info Received",
}

// GoogleSheetsSetting 谷歌表格设置
type GoogleSheetsSetting struct {
	SpreadsheetID string `json:"spreadsheet_id"`
	Link          string `json:"link"`
	View          int    `json:"view"`
}

// UserService 用户服务
type UserService struct {
	userRepo      users.UserRepository
	userCacheRepo users.UserCacheRepository
	ordersRepo    orders.OrderRepository
	sheetsRepo    google.SheetsRepository

	userRepository impl.UserRepository
}

// NewUserService 创建用户服务实例
func NewUserService(userRepo users.UserRepository, userCacheRepo users.UserCacheRepository, ordersRepo orders.OrderRepository, sheetsRepo google.SheetsRepository, useri impl.UserRepository) *UserService {
	return &UserService{userRepo: userRepo, userCacheRepo: userCacheRepo, ordersRepo: ordersRepo, sheetsRepo: sheetsRepo, userRepository: useri}
}

// GetLatestUser 获取最新的一条用户数据
func (s *UserService) GetLatestUser(ctx context.Context, cols ...string) (*models.Users, error) {
	return s.userRepo.GetLatestUser(ctx, cols...)
}

// GetUsers 根据游标获取所有用户
func (s *UserService) GetUsers(ctx context.Context, cursor int, limit int, cols ...string) ([]*models.Users, error) {
	return s.userRepo.GetUsers(ctx, cursor, limit, cols...)
}

// ShouldExportSheet 是否导出 GoogleSheet
func (s *UserService) ShouldExportSheet(ctx context.Context, u *models.Users) bool {
	// 非活跃用户
	if u.IsDelete != 0 || u.Redact != 0 {
		return false
	}

	// 获取 Group Store
	u.ChargeGroupStore, _ = s.userRepo.GetGroupStore(ctx, u.ID, "id", "main_user_id", "pro_disabled", "store_name")

	if u.PlanId > 0 || u.OldTotal > 0 || (u.ChargeGroupStore.MainUserId != 0 && u.ChargeGroupStore.ProDisabled == 0) {
		// 是否具有导出权限, 并且是否为用户当地时间 6 点
		perm := s.userRepository.GetPermissionByUser(*u)
		return perm.VerifyPermission(domain.GoogleSheet) && s.checkExportTime(u)
	}

	return false
}

// GetUserTimezone 获取用户时区
func (s *UserService) GetUserTimezone(ctx context.Context, id int) (string, error) {
	// from cache
	timezone, _ := s.userCacheRepo.GetTimezone(ctx, id)
	if len(timezone) != 0 {
		return timezone, nil
	}

	// from db
	user, err := s.userRepo.Get(ctx, id, "time_zone")
	if err != nil {
		return "", err
	}
	return user.TimeZone, nil
}

func (s *UserService) checkExportTime(u *models.Users) bool {
	return time.Now().In(s.parseTimezone(u.TimeZone)).Hour() == 6
}

func (s *UserService) parseTimezone(offset string) *time.Location {
	// +0800
	if len(offset) == 5 && (offset[0] == '-' || offset[0] == '+') {
		// 小时
		hour := offset[1:3]
		// 分钟
		minute := offset[3:]

		// 将字符串转换为整数
		h, aErr := strconv.Atoi(hour)
		m, bErr := strconv.Atoi(minute)
		if aErr == nil && bErr == nil {
			total := h*60*60 + m*60
			if offset[0] == '-' {
				total *= -1
			}
			return time.FixedZone(fmt.Sprintf("UTC%s", offset), total)
		}
	}

	// (GMT+08:00) Asia/Shanghai
	if v := strings.Split(offset, " "); len(v) == 2 {
		if t, err := time.LoadLocation(v[1]); err == nil {
			return t
		}
	}

	// Zone
	return time.UTC
}

// ExportSheet 导出 GoogleSheet
func (s *UserService) ExportSheet(ctx context.Context, userID int, sheetName string) error {
	// 获取用户 GoogleRepository Sheet Setting
	setting, err := s.getGoogleSheetSetting(ctx, userID)
	if err != nil {
		return err
	}

	logger.Info(ctx, fmt.Sprintf("[UserID: %d, Sheet: %s] exporting ...", userID, sheetName))

	// 先清理 Google Sheets
	if err := s.sheetsRepo.Recreate(ctx, setting.SpreadsheetID, sheetName); err != nil {
		return err
	}

	// 用户时区
	timezone, err := s.GetUserTimezone(ctx, userID)
	if err != nil {
		return err
	}
	loc := s.parseTimezone(timezone)

	// GoogleSheet row index
	index := 1

	// 写入表头
	headers := []interface{}{
		"Order",
		"Status",
		"Tracking Number",
		"Courier",
		"Created at",
		"Fulfilled at",
		"Transit Time(day)",
		"Residence Time(day)",
		"Last Tracking Info",
		"Last Tracking time",
		"Destination Country",
		"Customer Name",
		"Customer Email",
		"Customer Phone Number",
		"Product Info",
	}
	s.sheetsRepo.Write(ctx, setting.SpreadsheetID, sheetName, index, headers)

	index++

	var (
		cursor = 0
		limit  = 1000
	)

	for {
		list, err := s.ordersRepo.GetOrderTracks(ctx, userID, cursor, limit, "*")
		if err != nil {
			return err
		}

		// 一次写入多行数据，避免频繁调用 GoogleSheet API
		rows := make([][]interface{}, 0, len(list))

		for _, order := range list {
			// 数据筛选
			if s.filterOrder(ctx, order, setting) {
				// 数据转换
				rows = append(rows, s.convertOrder(loc, order))
			}
		}

		// 写入 google sheets
		for i := 0; i < 2; i++ {
			err = s.sheetsRepo.Write(ctx, setting.SpreadsheetID, sheetName, index, rows...)
			if err != nil {
				// 重试
				continue
			}
		}

		index += len(rows)

		if err != nil {
			logger.Error(ctx, fmt.Sprintf("write to google_sheets failed. %s: %v", sheetName, err))
		}

		if len(list) != limit {
			break
		}

		cursor = list[len(list)-1].ID
	}

	return nil
}

func (s *UserService) filterOrder(ctx context.Context, order *models.OrderTracks, setting *GoogleSheetsSetting) bool {
	// 获取订单轨迹
	if len(order.TrackNumber) != 0 {
		order.TrackInfos, _ = s.ordersRepo.GetOrderTrackInfo(ctx, order.UserId, order.TrackNumber, order.Courier,
			"id", "transit_time", "stay_time", "last_event", "last_date", "original_country")
	}

	// 获取订单产品, 如果没有 order.FulfillmentId 则使用 order.OrderID
	if len(order.FulfillmentId) != 0 {
		order.OrderProducts, _ = s.ordersRepo.GetOrderProductsByFulfillmentID(ctx, order.UserId, order.FulfillmentId, "title")
	} else {
		order.OrderProducts, _ = s.ordersRepo.GetOrderProductsByOrderID(ctx, order.UserId, order.OrderId, "title")
	}

	// 使用订单视图筛选
	if setting.View != -1 {
		if view, _ := s.ordersRepo.GetOrderView(ctx, setting.View, "*"); view != nil {
			return s.filterOrderWithView(order, view)
		}
	}

	// 使用订单创建时间筛选
	return s.filterOrderWithInterval(order)
}

func (s *UserService) filterOrderWithView(order *models.OrderTracks, view *models.OrderViewSet) bool {
	// 订单创建时间
	var (
		timeDateRange      = make([]int64, 0, 2)
		orderCreateTimeMax = time.Now().Unix()
		orderCreateTimeMin = time.Now().AddDate(0, -6, 0).Unix()
	)
	json.Unmarshal([]byte(view.TimeRange), &timeDateRange)
	day, _ := strconv.Atoi(view.DateFilter)
	if day != 0 {
		orderCreateTimeMin = time.Now().AddDate(0, 0, -day).Unix()
	} else {
		// 如果设置了时间区间
		if len(timeDateRange) == 2 {
			orderCreateTimeMin = timeDateRange[0]
			orderCreateTimeMax = time.Unix(timeDateRange[1], 0).AddDate(0, 0, 1).Unix()
		}
	}
	if int64(order.OrderCreateTime) > orderCreateTimeMax || int64(order.OrderCreateTime) < orderCreateTimeMin {
		return false
	}

	// 数据创建时间
	if len(view.CreatedDateFilter) != 0 {
		var (
			createDateRange = make([]int64, 0, 2)
			createTimeMax   = time.Now().Unix()
			createTimeMin   = time.Now().AddDate(0, -6, 0).Unix()
		)

		json.Unmarshal([]byte(view.CreatedRange), &createDateRange)
		day, _ = strconv.Atoi(view.CreatedDateFilter)

		// 如果设置了时间区间
		if len(createDateRange) == 2 {
			createTimeMin = createDateRange[0]
			createTimeMax = createDateRange[1]
		}
		// 如果设置了时间筛选
		if day != 0 {
			createTimeMin = time.Now().AddDate(0, 0, -day).Unix()
		}

		createTime := order.CreatedAt.UTC().Unix()
		if createTime > createTimeMax || createTime < createTimeMin {
			return false
		}
	}

	// 最后更新时间
	if len(view.LastUpdatedDateFilter) != 0 {
		var (
			lastDateRange = make([]int64, 0, 2)
			updateTimeMin = time.Now().Unix()
			updateTimeMax = time.Now().AddDate(0, -6, 0).Unix()
		)
		json.Unmarshal([]byte(view.LastUpdatedRange), &lastDateRange)
		day, _ = strconv.Atoi(view.LastUpdatedDateFilter)

		// 如果设置了时间区间
		if len(lastDateRange) == 2 {
			updateTimeMin = lastDateRange[0]
			updateTimeMax = lastDateRange[1]
		}
		// 如果设置了时间筛选
		if day != 0 {
			updateTimeMin = time.Now().AddDate(0, 0, -day).Unix()
		}

		if int64(order.LastDate) > updateTimeMax || int64(order.LastDate) < updateTimeMin {
			return false
		}
	}

	// 运输商
	if len(view.Couriers) > 2 {
		couriers := make([]string, 0)
		json.Unmarshal([]byte(view.Couriers), &couriers)

		// 若 couriers 长度为 0, 则不进行运输商筛选
		found := len(couriers) == 0
		for _, v := range couriers {
			if v == order.Courier {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// 目的国
	if len(view.Destinations) > 2 {
		destinations := make([]string, 0)
		json.Unmarshal([]byte(view.Destinations), &destinations)

		// 若 destinations 长度为 0, 则不进行目的国筛选
		found := len(destinations) == 0
		for _, v := range destinations {
			if v == order.Destination {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// 发件国
	if len(view.Origins) > 2 {
		origins := make([]string, 0)
		json.Unmarshal([]byte(view.Origins), &origins)

		// 若 origins 长度为 0, 则不进行发件国筛选
		found := len(origins) == 0
		for _, v := range origins {
			if v == order.TrackInfos.OriginalCountry {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// 物流状态
	if len(view.StatusMulti) > 2 {
		s := make([]uint, 0)
		json.Unmarshal([]byte(view.StatusMulti), &s)

		ok := false
		for _, v := range s {
			if order.TrackStatus == v {
				ok = true
				break
			}
		}

		if !ok {
			return false
		}
	}

	// 发货状态
	if view.FulfillmentStatus == 1 && len(order.TrackNumber) == 0 {
		return false
	}
	if view.FulfillmentStatus == 2 && len(order.TrackNumber) != 0 {
		return false
	}

	// 运输时效
	if len(view.TransitTime) > 2 {
		transitTime := make([]string, 0)
		json.Unmarshal([]byte(view.TransitTime), &transitTime)

		ok := len(transitTime) == 0
		// 转换成天数区间筛选
		for _, v := range transitTime {
			if v == "fast" && order.TransitTime >= 0 && order.TransitTime <= 5 {
				ok = true
				break
			}
			if v == "normal" && order.TransitTime >= 6 && order.TransitTime <= 11 {
				ok = true
				break
			}
			if v == "slow" && order.TransitTime >= 12 && order.TransitTime <= 20 {
				ok = true
				break
			}
			if v == "vSlow" && order.TransitTime > 20 {
				ok = true
				break
			}
		}
		if !ok {
			return false
		}
	}

	// 模糊搜索
	if len(view.Keywords) > 0 {
		// 去除emoji和特殊字符
		keyword := regexp.MustCompile(`[\x{10000}-\x{10FFFF}]`).ReplaceAllString(view.Keywords, "")

		// customer_email, order_name, track_number
		if !strings.Contains(order.CustomerEmail, keyword) && !strings.Contains(order.OrderName, keyword) && !strings.Contains(order.TrackNumber, keyword) {
			return false
		}
	}

	// 备注
	if len(view.HasNote) > 2 {
		notes := make([]string, 0)
		json.Unmarshal([]byte(view.HasNote), &notes)

		if len(notes) > 0 {
			// with
			if notes[0] == "with" && len(order.Notes) == 0 {
				return false
			}
			// without
			if notes[0] == "without" && len(order.Notes) != 0 {
				return false
			}
		}
	}

	return true
}

// filterOrderWithInterval 只查询 6 个月以内的订单
func (s *UserService) filterOrderWithInterval(order *models.OrderTracks) bool {
	return int64(order.OrderCreateTime) >= time.Now().UTC().AddDate(0, -6, 0).UTC().Unix()
}

// convertOrder 将 models.OrderTracks 转换为 GoogleSheets row
func (s *UserService) convertOrder(loc *time.Location, order *models.OrderTracks) []interface{} {
	row := make([]interface{}, 0, 15)

	// Order
	row = append(row, order.OrderName)

	// Status
	row = append(row, trackStatusMap[TrackStatus(order.TrackStatus)])

	// Tracking Number
	row = append(row, order.TrackNumber)

	// Courier
	row = append(row, order.Courier)

	// Created at
	row = append(row, order.CreatedAt.In(loc).Format(time.DateTime))

	// Fulfilled at
	row = append(row, time.Unix(int64(order.OrderFulfillTime), 0).In(loc).Format(time.DateTime))

	// Transit Time(day)
	transitTime := ""
	if order.TrackInfos.TransitTime != 0 {
		transitTime = strconv.Itoa(int(order.TrackInfos.TransitTime))
	}
	row = append(row, transitTime)

	// Residence Time(day)
	stayTime := ""
	if order.TrackInfos.StayTime != 0 {
		stayTime = strconv.Itoa(int(order.TrackInfos.StayTime))
	}
	row = append(row, stayTime)

	// Last Tracking Info
	row = append(row, order.TrackInfos.LastEvent)

	// Last Tracking time
	lastTrackingTime := ""
	if order.TrackInfos.LastDate > 0 {
		lastTrackingTime = time.Unix(int64(order.TrackInfos.LastDate), 0).In(loc).Format(time.DateTime)
	}
	row = append(row, lastTrackingTime)

	// Destination Country
	row = append(row, strings.ToUpper(order.Destination))

	// Customer Name
	row = append(row, order.CustomerName)

	// Customer Email
	row = append(row, strings.TrimSpace(order.CustomerEmail))

	// Customer Phone Number
	row = append(row, strings.TrimSpace(order.CustomerPhone))

	// Product Info
	productTitles := []string{}
	for _, p := range order.OrderProducts {
		productTitles = append(productTitles, p.Title)
	}
	productInfo := strings.Join(productTitles, ",")
	row = append(row, productInfo)

	return row
}

func (s *UserService) getGoogleSheetSetting(ctx context.Context, userID int) (*GoogleSheetsSetting, error) {
	// 查询用户绑定的 app 信息
	i, err := s.userRepo.GetIntegrations(ctx, userID, "googleSheets", "id", "setting")
	if err != nil {
		return nil, err
	}

	// 序列化 setting
	setting := &GoogleSheetsSetting{}
	if err := json.Unmarshal([]byte(i.Setting), setting); err != nil {
		return nil, err
	}

	// 用户为绑定 GoogleRepository Sheet
	if len(setting.SpreadsheetID) == 0 {
		return nil, fmt.Errorf("user[%d] not bind google sheet", userID)
	}

	// 默认视图
	if setting.View == 0 {
		setting.View = -1
	}

	return setting, nil
}
