package application

import (
	"context"

	repo "tmshopify/internal/domain/repo/orders"
	"tmshopify/internal/providers"
)

type SyncOrderService struct {
	shopifyOrderRepo repo.ShopifyOrderRepository
}

func NewSyncOrderService(repo *providers.Repositories) *SyncOrderService {
	return &SyncOrderService{}
}

func (s *SyncOrderService) SyncOrder(ctx context.Context, UserID int, FromDate string, ToDate string) {

}
