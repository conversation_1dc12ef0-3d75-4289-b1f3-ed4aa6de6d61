package entity

import (
	"encoding/json"
	"tmshopify/config"
	"tmshopify/store/models"
)

type TrackThemeEntityStruct struct {
	UserId         int
	ThemeCode      models.Theme
	CustomStyle    string
	TopHtml        string
	BottomHtml     string
	ClassicSetting config.ClassicTheme
	ModernSetting  config.ModernTheme
	Translation    map[string]string
}

func HandleTheme(activeTheme models.TrackPageThemes, lang string) TrackThemeEntityStruct {
	if lang == "zh-CN" {
		lang = "cn"
	}
	var translationList map[string]map[string]string
	err := json.Unmarshal([]byte(activeTheme.Translation), &translationList)
	TrackThemeEntity := TrackThemeEntityStruct{
		UserId:         int(activeTheme.UserId),
		ThemeCode:      activeTheme.ThemeCode,
		CustomStyle:    activeTheme.CustomStyle,
		TopHtml:        activeTheme.TopHtml,
		BottomHtml:     activeTheme.BottomHtml,
		ClassicSetting: config.ClassicTheme{},
		ModernSetting:  config.ModernTheme{},
	}
	switch activeTheme.ThemeCode {
	case models.ThemeModern:
		var modernTheme config.ModernTheme
		err = json.Unmarshal([]byte(activeTheme.Settings), &modernTheme)
		if err != nil {
			modernTheme = config.DefaultModernTheme
		}
		TrackThemeEntity.ModernSetting = modernTheme
		if modernTheme.General.AutoLanguage {
			TrackThemeEntity.Translation = translationList[lang]
			if len(TrackThemeEntity.Translation) == 0 {
				TrackThemeEntity.Translation = translationList["en"]
			}
		} else {
			TrackThemeEntity.Translation = translationList["custom"]
		}
	default:
		var classicTheme config.ClassicTheme
		err = json.Unmarshal([]byte(activeTheme.Settings), &classicTheme)
		if err != nil {
			classicTheme = config.DefaultClassicTheme
		}
		TrackThemeEntity.ClassicSetting = classicTheme
		if classicTheme.General.AutoLanguage {
			TrackThemeEntity.Translation = translationList[lang]
			if len(TrackThemeEntity.Translation) == 0 {
				TrackThemeEntity.Translation = translationList["en"]
			}
		} else {
			TrackThemeEntity.Translation = translationList["custom"]
		}
	}
	return TrackThemeEntity
}
