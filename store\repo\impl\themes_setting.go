package impl

import (
	"encoding/json"
	"time"
	"tmshopify/config"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type ThemesSetting struct {
	Helper
}

func (t *ThemesSetting) GetThemesSettingArr() (map[string]interface{}, error) {
	cacheKey := config.CacheKey.ThemeSetting
	themeArr := make(map[string]interface{})
	settingCache, _ := database.RS.Get(cacheKey)
	var themesSetting []models.ThemesSetting
	err := json.Unmarshal(settingCache, &themesSetting)
	if err != nil || string(settingCache) == "" {
		err := database.DB.Where("is_active = ?", 1).Find(&themesSetting).Error
		if err == nil {
			settingCache, _ = json.Marshal(themesSetting)
			_ = database.RS.SetWithExpire(config.CacheKey.ThemeSetting, string(settingCache), time.Hour*24)
		} else {
			return themeArr, err
		}
	}

	if len(themesSetting) != 0 {
		for _, v := range themesSetting {
			modType := v.ModType
			modeClass := make(map[string]interface{})
			if v.ModClass != "" {
				_ = json.Unmarshal([]byte(v.ModClass), &modeClass)
			}
			themeArr[modType] = map[string]map[string]string{
				"class": {
					"button": modeClass["button"].(string),
					"input":  modeClass["input"].(string),
					"a":      modeClass["a"].(string),
				},
			}
		}
	}
	return themeArr, err
}
