package service

import (
	"tmshopify/pkg/utils/paginate"
	"tmshopify/server/response"
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type CommonRecognition struct {
	DailyPendingTracksRepo *impl.DailyPendingTracks
	CourierTopRuleRepo     *impl.CourierTopRule
}

func (c *CommonRecognition) TotalTable() []map[string]interface{} {
	data, _ := c.DailyPendingTracksRepo.GroupByTrackLength()
	return data
}

func (c *CommonRecognition) DataTable(trackLength int) (*response.SuccessResponse, error) {
	courierDistribution, err := c.DailyPendingTracksRepo.GetCourierDistributionByTrackLength(trackLength)
	if err != nil {
		return &response.SuccessResponse{}, err
	}
	samplingTable, err := c.DailyPendingTracksRepo.GetUniqueTracksByUserAndCourier(trackLength)
	if err != nil {
		return &response.SuccessResponse{}, err
	}
	return &response.SuccessResponse{
		Data: map[string]interface{}{
			"pieData":       courierDistribution,
			"samplingTable": samplingTable,
		},
	}, nil
}

func (c *CommonRecognition) CreateCommonRule(m models.CourierTopRules) error {
	return c.CourierTopRuleRepo.Create(&m)
}

func (c *CommonRecognition) UpdateCommonRule(m models.CourierTopRules) error {
	return c.CourierTopRuleRepo.Update(&m)
}

func (c *CommonRecognition) DeleteCommonRule(id string) error {
	return c.CourierTopRuleRepo.DeleteById(models.CourierTopRules{}, id)
}

func (c *CommonRecognition) GetCommonRuleList(param *paginate.Param) (*response.SuccessResponse, error) {
	items, total, err := c.CourierTopRuleRepo.GetCourierTopRuleList(param)

	return &response.SuccessResponse{
		Data: map[string]interface{}{
			"items":    items,
			"page":     param.Page,
			"total":    total,
			"pageSize": param.PageSize,
		},
	}, err
}

func NewCommonRecognition() *CommonRecognition {
	return &CommonRecognition{
		DailyPendingTracksRepo: &impl.DailyPendingTracks{},
		CourierTopRuleRepo:     &impl.CourierTopRule{},
	}
}
