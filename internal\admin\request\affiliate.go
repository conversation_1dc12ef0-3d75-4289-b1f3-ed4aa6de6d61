package request

type UpdateAffiliate struct {
	ID           int    `json:"id" validate:"required,number,gt=0"`
	Name         string `json:"name" validate:"required,max=50"`
	LandingPage  string `json:"landing_page" validate:"required,url"`
	ContactEmail string `json:"contact_email" validate:"email"`
	ContactName  string `json:"contact_name" validate:"max=20"`
	ContactPhone string `json:"contact_phone" validate:"max=40"`
}

type CreateAffiliate struct {
	Name         string `json:"name" validate:"required,max=50"`
	Password     string `json:"password" validate:"required,max=50"`
	LandingPage  string `json:"landing_page" validate:"required,url"`
	ContactEmail string `json:"contact_email" validate:"email"`
	ContactName  string `json:"contact_name" validate:"max=20"`
	ContactPhone string `json:"contact_phone" validate:"max=40"`
}
