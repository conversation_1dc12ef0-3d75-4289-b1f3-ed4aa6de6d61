package api

import (
	"github.com/gin-gonic/gin"
	"strconv"
	"tmshopify/internal/admin/service"
	"tmshopify/pkg/utils/paginate"
	"tmshopify/server/response"
)

type Notification struct {
}

func (n *Notification) ShopifyNotificationLog(ctx *gin.Context) {
	var searchParam struct {
		UserId      string `json:"userId"`
		TrackNumber string `json:"trackNumber"`
		paginate.Param
	}
	_ = ctx.ShouldBind(&searchParam)

	trackNumber := searchParam.TrackNumber

	userId, err := strconv.Atoi(searchParam.UserId)
	if err != nil {
		userId = 0
	}

	data, count := service.NewNotification().GetShopifyNotificationLogList(&searchParam.Param, userId, trackNumber)

	response.Success(ctx, &response.SuccessResponse{
		Data: gin.H{
			"items":    data,
			"page":     searchParam.Param.Page,
			"pageSize": searchParam.Param.PageSize,
			"total":    count,
		},
	})
}
