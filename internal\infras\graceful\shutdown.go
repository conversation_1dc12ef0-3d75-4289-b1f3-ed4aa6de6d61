package graceful

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"
)

var (
	stopChan   = make(chan struct{}, 1)  // 手动退出信号量
	stopSignal = make(chan os.Signal, 1) // 接收退出信号量
	shutdownFn = func() {}               // 平滑退出执行函数
)

// Stop 手动退出
func Stop(fn ...func()) {
	close(stopChan)
	if len(fn) > 0 && fn[0] != nil {
		shutdownFn = fn[0]
	}
}

// Shutdown 服务平滑退出
func Shutdown(d time.Duration) {
	// We'll accept graceful shutdowns when quit via SIGINT (Ctrl+C)
	// receive signal to exit main goroutine.
	signal.Notify(stopSignal, syscall.SIGINT, syscall.SIGTERM)

	// Block until we receive our signal.
	select {
	case sig := <-stopSignal:
		log.Println("receive exit signal: ", sig.String())
	case <-stopChan:
		log.Println("receive stop signal")
	}

	// Create a deadline to wait for.
	ctx, cancel := context.WithTimeout(context.Background(), d)
	defer cancel()

	// Doesn't block if no connections, but will otherwise wait
	// until the timeout deadline.
	// Optionally, you could run srv.Shutdown in a goroutine and block on
	// if your application should wait for other services
	// to finalize based on context cancellation.
	done := make(chan struct{}, 1)
	go func() {
		defer close(done)
		shutdownFn()
	}()

	<-done
	<-ctx.Done()

	log.Println("server shutting down")
}
