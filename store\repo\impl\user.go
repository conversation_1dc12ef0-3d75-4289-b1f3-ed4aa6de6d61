package impl

import (
	"gorm.io/gorm"
	"strconv"
	"time"
	"tmshopify/pkg/domain"

	"tmshopify/pkg/utils/paginate"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type User struct {
	Helper
}

type UserRepository interface {
	Count() int64
	Get() ([]models.Users, error)
	Paginate(paginateParam *paginate.Param) ([]models.Users, int64, error)
	FirstById(id int64) (*models.Users, error)
	Exist(id int64) bool
	Create(m *models.Users) error
	Update(id string, fields map[string]interface{}) error
	Delete(id string) error
	CountryUsersDistribution(paginateParam *paginate.Param, startTime time.Time, endTime time.Time) ([]struct {
		Key                      int
		Country                  string
		CountryPayUserNum        int
		CountryUserNum           int
		CountryPriceTotal        float32
		CountryUserProportion    float32
		CountryPriceProportion   float32
		CountryPayUserProportion float32
	}, int64, int64, error)
	PayUsersData(startTime time.Time, endTime time.Time) (struct {
		PriceTotal float32
		PayUserNum int
	}, error)
	GetPermissionByUser(user models.Users) domain.PermissionDomain
	PreloadSubscriptionDetailByStoreName(storeName string) (models.Users, error)
	FirstByStoreName(storeName string) (*models.Users, error)
	FirstActiveUserIdByShop(shop string) (models.Users, error)
	FirstByMinId() (*models.Users, error)
	SearchUserByIdField(id int) []models.Users
	SearchUserByEmailOrStoreNameField(name string) []models.Users
}

func (u *User) Count() int64 {
	var count int64
	database.DB.Model(&models.Users{}).Count(&count)
	return count
}

func (u *User) Get() ([]models.Users, error) {
	var items []models.Users
	result := database.DB.Find(&items)
	return items, result.Error
}

func (u *User) Paginate(paginateParam *paginate.Param) ([]models.Users, int64, error) {
	var items []models.Users
	total := u.Count()

	result := database.DB.Scopes(paginate.ORMScope(paginateParam)).Order("id desc").Find(&items)

	return items, total, result.Error
}

func (u *User) FirstById(id int64) (*models.Users, error) {
	var m *models.Users

	result := database.DB.First(&m, id)

	return m, result.Error
}

func (u *User) Exist(id int64) bool {
	var count int64
	database.DB.Where("id=?", id).Count(&count)
	return count > 0
}

func (u *User) Create(m *models.Users) error {
	result := database.DB.Create(m)

	return result.Error
}

func (u *User) Update(id string, fields map[string]interface{}) error {
	return u.UpdateByMap(&models.Users{}, id, fields)
}

func (u *User) Delete(id string) error {
	return u.DeleteById(&models.Users{}, id)
}

func (u *User) CountryUsersDistribution(paginateParam *paginate.Param, startTime time.Time, endTime time.Time) ([]struct {
	Key                      int
	Country                  string
	CountryPayUserNum        int
	CountryUserNum           int
	CountryPriceTotal        float32
	CountryUserProportion    float32
	CountryPriceProportion   float32
	CountryPayUserProportion float32
}, int64, int64, error) {
	var results []struct {
		Key                      int     //序列号
		Country                  string  //国家/地区名
		CountryPayUserNum        int     //该国家的付费用户人数
		CountryUserNum           int     //该国家的用户数
		CountryPriceTotal        float32 //该国家的用户付费总金额
		CountryUserProportion    float32 //用户占比
		CountryPriceProportion   float32 //付费占比
		CountryPayUserProportion float32 //付费用户占比
	}

	var uniqueCountriesCount int64

	//去除多余数据
	subQuery := database.DB.Table("data_subscription_details").
		Select("user_id, status AS subscription_status, SUM(CASE WHEN status = 1 THEN price ELSE 0 END) AS price").
		Group("user_id")

	result := database.DB.Table("users").
		Select("shopify_countries.name as country, "+
			"COUNT(CASE WHEN users.plan_id > 0 THEN 1 ELSE NULL END) AS country_pay_user_num, "+
			"COUNT(users.id) AS country_user_num, "+
			"SUM(CASE WHEN subscription_status = 1 AND users.plan_id > 0 THEN price ELSE 0 END) AS country_price_total").
		Joins("LEFT JOIN (?) AS subscription_details ON users.id = subscription_details.user_id", subQuery).
		Joins("LEFT JOIN shopify_countries ON users.country = shopify_countries.code").
		Where("users.created_at between ? and ?", startTime, endTime).
		Group("users.country,shopify_countries.name").
		Order("country_user_num DESC").
		Scopes(paginate.ORMScope(paginateParam)).
		Scan(&results)
	//获取用户国家数量
	database.DB.Table("users").
		Select("COUNT(DISTINCT country)").
		Where("created_at between ? and ?", startTime, endTime).
		Scan(&uniqueCountriesCount)
	//用户国家数量可以凑成的页数
	pageNum := int(uniqueCountriesCount) / paginateParam.PageSize
	//减去完整页后的数据条数
	var pageRedundantDataNum int
	if int(uniqueCountriesCount) < paginateParam.PageSize {
		pageRedundantDataNum = int(uniqueCountriesCount)
	} else {
		pageRedundantDataNum = int(uniqueCountriesCount) % paginateParam.PageSize
	}

	var (
		fillingDataNum int //需要填充的数据条数
		countriesList  []struct {
			Name string
		}
	)
	fillingDataNum = paginateParam.PageSize - pageRedundantDataNum
	//有多出条数时，实际页数+1
	if pageRedundantDataNum != 0 {
		pageNum++
	}
	//没有用户属于的国家地区用户信息填充
	userQuery := database.DB.Model(&models.Users{}).Select("country").Where("created_at between ? and ?", startTime, endTime).Group("country")
	if paginateParam.Page > pageNum {
		offset := (paginateParam.Page-pageNum-1)*paginateParam.PageSize + fillingDataNum
		database.DB.Table("shopify_countries").
			Select("name").
			Where("code NOT IN (?)", userQuery).
			Offset(offset).Limit(paginateParam.PageSize).Scan(&countriesList)
	} else if len(results) < paginateParam.PageSize {
		database.DB.Table("shopify_countries").
			Select("name").
			Where("code NOT IN (?)", userQuery).
			Offset(0).Limit(fillingDataNum).Scan(&countriesList)
	}

	for _, value := range countriesList {
		results = append(results, struct {
			Key                      int
			Country                  string
			CountryPayUserNum        int
			CountryUserNum           int
			CountryPriceTotal        float32
			CountryUserProportion    float32
			CountryPriceProportion   float32
			CountryPayUserProportion float32
		}{
			Country: value.Name,
		})
	}

	var count int64
	database.DB.Table("shopify_countries").Select("id").Count(&count)

	var userCount int64
	database.DB.Model(&models.Users{}).Select("id").Where("created_at between ? and ?", startTime, endTime).Count(&userCount)

	return results, count, userCount, result.Error
}

func ActiveUser(db *gorm.DB) *gorm.DB {
	return db.Where("is_delete = 0 and redact=0")
}

func (u *User) PayUsersData(startTime time.Time, endTime time.Time) (struct {
	PriceTotal float32
	PayUserNum int
}, error) {
	var results struct {
		PriceTotal float32 //付费总金额
		PayUserNum int     //付费总人数
	}
	result := database.DB.Table("users").
		Select("SUM(CASE WHEN data_subscription_details.status = 1 THEN price ELSE 0 END) AS price_total, COUNT(users.id) as pay_user_num").
		Joins("LEFT JOIN data_subscription_details ON users.id = data_subscription_details.user_id").
		Where("users.plan_id > 0").
		Where("users.created_at between ? and ?", startTime, endTime).
		Scan(&results)

	return results, result.Error
}

func (u *User) FirstByMinId() (*models.Users, error) {
	var m *models.Users
	result := database.DB.First(&m)
	return m, result.Error
}

func (u *User) FirstActiveUserIdByShop(shop string) (models.Users, error) {
	user := models.Users{}
	result := database.DB.Model(user).Scopes(ActiveUser).Where("store_name = ?", shop).Select("id").First(&user)
	return user, result.Error
}

func (u *User) FirstByStoreName(storeName string) (*models.Users, error) {
	m := new(models.Users)
	result := database.DB.Scopes(ActiveUser).Where("store_name = ?", storeName).First(m)
	return m, result.Error
}

func (u *User) PreloadSubscriptionDetailByStoreName(storeName string) (models.Users, error) {
	var user models.Users
	result := database.DB.Preload("SubscriptionDetail").Preload("ChargeGroupStore").Preload("UserPermissions").Scopes(ActiveUser).Where("store_name = ?", storeName).First(&user)
	return user, result.Error
}

func (u *User) GetPermissionByUser(user models.Users) domain.PermissionDomain {
	var newUser models.Users
	// 判断是否存在关联主店铺
	if user.ChargeGroupStore.MainUserId != 0 && user.ChargeGroupStore.ProDisabled == 0 {
		database.DB.Preload("SubscriptionDetail").Preload("UserPermissions").Where("id = ?", user.ChargeGroupStore.MainUserId).First(&newUser)
	}
	if newUser.ID == 0 {
		newUser = user
	}
	totalPrevious := newUser.OldTotal
	consumePrevious := newUser.OldConsume
	if (totalPrevious-consumePrevious > 0) && newUser.PlanId <= 0 && (newUser.ID == 26237 || newUser.ID == 11845) {
		return GetLevelPermissionDomain(domain.PRO, newUser, domain.LATEST)
	}
	var subscriptionDetails models.DataSubscriptionDetails
	var nullDataSubscriptionDetails models.DataSubscriptionDetails
	if newUser.SubscriptionDetail == nullDataSubscriptionDetails {
		err := database.DB.Model(&newUser).Association("SubscriptionDetail").Find(&subscriptionDetails)
		if err != nil {
			return GetLevelPermissionDomain(domain.FREE, newUser, domain.LATEST)
		}
	} else {
		subscriptionDetails = newUser.SubscriptionDetail
	}

	if subscriptionDetails == nullDataSubscriptionDetails || subscriptionDetails.PlanId == 0 {
		return GetLevelPermissionDomain(domain.FREE, newUser, domain.LATEST)
	}
	levelDomain := domain.InitLevelDomain(subscriptionDetails.PlanId, subscriptionDetails.Version, newUser.ID)
	return GetLevelPermissionDomain(levelDomain.Level, newUser, levelDomain.Version)
}

func (u *User) SearchUserByIdField(id int) []models.Users {
	var users []models.Users
	database.DB.Model(&models.Users{}).Scopes(ActiveUser).Where("id like ?", "%"+strconv.Itoa(id)+"%").Limit(30).Find(&users)
	return users
}
func (u *User) SearchUserByEmailOrStoreNameField(name string) []models.Users {
	var users []models.Users
	database.DB.Model(&models.Users{}).Scopes(ActiveUser).Where("store_name like ? or email like ? or store_real_name like ? ", "%"+name+"%", "%"+name+"%", "%"+name+"%").Limit(30).Find(&users)
	return users
}

func (u *User) FirstByToken(token string) (models.Users, error) {
	var user models.Users
	result := database.DB.Scopes(ActiveUser).Preload("ChargeGroupStore").Preload("UserPermissions").Where("password = ?", token).First(&user)
	return user, result.Error
}
