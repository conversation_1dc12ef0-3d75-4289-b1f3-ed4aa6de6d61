package impl

import (
	"fmt"
	"tmshopify/pkg/utils/paginate"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type IntegrationApp struct {
	Helper
}

type IntegrationSearchParams struct {
	paginate.Param
	Name       string `json:"name"`
	Status     int    `json:"status"`
	AppType    int    `json:"app_type"`
	CategoryID int    `json:"category_id"`
}

func (ips *IntegrationSearchParams) ToSql() string {
	where := ""
	if len(ips.Name) > 0 {
		where += fmt.Sprintf("name like '%%%s%%'", ips.Name)
	}
	if ips.Status > 0 {
		if len(where) > 0 {
			where += " and "
		}
		where += fmt.Sprintf("status=%d", ips.Status-1)
	}
	if ips.AppType > 0 {
		if len(where) > 0 {
			where += " and "
		}
		where += fmt.Sprintf("app_type=%d", ips.AppType-1)
	}
	if ips.CategoryID > 0 {
		if len(where) > 0 {
			where += " and "
		}
		where += fmt.Sprintf("category_id=%d", ips.CategoryID)
	}
	return where
}
func (i *IntegrationApp) Count(search string) int64 {
	var count int64
	database.DB.Model(&models.IntegrationApps{}).Where(search).Count(&count)
	return count
}

func (i *IntegrationApp) Paginate(search *IntegrationSearchParams, paginateParam *paginate.Param) ([]models.IntegrationApps, int64, error) {
	var items []models.IntegrationApps
	total := i.Count(search.ToSql())
	result := database.DB.Where(search.ToSql()).Preload("Category").Scopes(paginate.ORMScope(paginateParam)).Order("sort desc,created_at desc").Find(&items)
	return items, total, result.Error
}

func (i *IntegrationApp) Create(m *models.IntegrationApps) error {
	result := database.DB.Create(m)
	return result.Error
}

func (i *IntegrationApp) IsUniqueAppByNameAndId(name string, id int) bool {
	var count int64
	if id > 0 {
		database.DB.Model(&models.IntegrationApps{}).Where("name =? and id!=? ", name, id).Count(&count)
	} else {
		database.DB.Model(&models.IntegrationApps{}).Where("name=?", name).Count(&count)
	}
	return count == 0
}
func (i *IntegrationApp) IsUniqueAppBySettingLinkAndId(settingLink string, id int) bool {
	var count int64
	if id > 0 {
		database.DB.Model(&models.IntegrationApps{}).Where("setting_link =? and id!=? ", settingLink, id).Count(&count)
	} else {
		database.DB.Model(&models.IntegrationApps{}).Where("setting_link=?", settingLink).Count(&count)
	}
	return count == 0
}

func (i *IntegrationApp) Update(m *models.IntegrationApps) error {
	return database.DB.Omit("created_at").Save(m).Error
}

func (i *IntegrationApp) UpdatedWhenCategoryDeleted(categoryId string) error {
	err := database.DB.Model(&models.IntegrationApps{}).Where("category_id = ?", categoryId).Updates(map[string]interface{}{
		"category_id": 0,
	}).Error
	return err
}
