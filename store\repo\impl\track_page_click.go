package impl

import (
	"github.com/dromara/carbon/v2"

	"tmshopify/store/database"
	"tmshopify/store/models"
)

type TrackPageClicks struct {
	Helper
}

func (t *TrackPageClicks) ClickOrderStatistics(userId int) {
	createDate := carbon.SetTimezone(carbon.UTC).StartOfDay().StdTime().Unix()
	var trackPageClick *models.TrackPageClicks
	result := database.DB.Model(&models.TrackPageClicks{}).Where("user_id = ? AND create_date = ?", userId, createDate).Select("click_time").First(&trackPageClick)
	if result.RowsAffected == 0 {
		database.DB.Create(&models.TrackPageClicks{
			UserId:     userId,
			ClickTime:  1,
			CreateDate: uint(createDate),
		})
	} else {
		database.DB.Model(&models.TrackPageClicks{}).Where("user_id = ? AND create_date = ?", userId, createDate).Updates(map[string]interface{}{
			"click_time": trackPageClick.ClickTime + 1,
		})
	}
}
