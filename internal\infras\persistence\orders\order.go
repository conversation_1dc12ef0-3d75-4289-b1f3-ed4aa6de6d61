package orders

import (
	"context"

	"gorm.io/gorm"

	"tmshopify/internal/domain/repo/orders"
	"tmshopify/store/models"
)

var _ orders.OrderRepository = (*orderRepoImpl)(nil)

type orderRepoImpl struct {
	db *gorm.DB
}

// NewOrderRepository 创建订单资源实例
func NewOrderRepository(db *gorm.DB) orders.OrderRepository {
	return &orderRepoImpl{db: db}
}

// GetOrderView 获取订单视图
func (i *orderRepoImpl) GetOrderView(ctx context.Context, id int, cols ...string) (*models.OrderViewSet, error) {
	m := &models.OrderViewSet{}
	err := i.db.WithContext(ctx).Select(cols).Where("id = ?", id).First(m).Error
	if err != nil {
		return nil, err
	}
	return m, nil
}

// GetOrderTracks 根据游标获取用户所有订单
func (i *orderRepoImpl) GetOrderTracks(ctx context.Context, userID int, cursor int, limit int, cols ...string) ([]*models.OrderTracks, error) {
	var (
		r = &models.OrderTracks{}
		m = []*models.OrderTracks{}
	)
	err := i.db.Scopes(r.TableOfUser(r.TableName(), userID)).
		WithContext(ctx).
		Select(cols).
		Where("id > ? AND user_id = ? AND is_delete = 0", cursor, userID).
		Limit(limit).
		Find(&m).
		Error
	if err != nil {
		return nil, err
	}
	return m, nil
}

// GetOrderTrackInfo 获取订单轨迹
func (i *orderRepoImpl) GetOrderTrackInfo(ctx context.Context, userID int, trackNumber string, courier string, cols ...string) (models.TrackInfos, error) {
	m := models.TrackInfos{}
	err := i.db.WithContext(ctx).
		Scopes(m.TableOfUser(m.TableName(), userID)).
		Select(cols).
		Where("user_id = ? AND track_number = ? AND courier = ?", userID, trackNumber, courier).
		First(&m).
		Error
	return m, err
}

// GetOrderProductsByOrderID 获取订单产品
func (i *orderRepoImpl) GetOrderProductsByOrderID(ctx context.Context, userID int, orderID string, cols ...string) ([]models.OrderProduct, error) {
	var (
		r = &models.OrderProduct{}
		m = []models.OrderProduct{}
	)
	err := i.db.Scopes(r.TableOfUser(r.TableName(), userID)).
		WithContext(ctx).
		Select(cols).
		Where("user_id = ? AND order_id = ?", userID, orderID).
		Find(&m).
		Error
	if err != nil {
		return nil, err
	}
	return m, nil
}

// GetOrderProductsByFulfillmentID 获取订单产品
func (i *orderRepoImpl) GetOrderProductsByFulfillmentID(ctx context.Context, userID int, fullID string, cols ...string) ([]models.OrderProduct, error) {
	var (
		r = &models.OrderProduct{}
		m = []models.OrderProduct{}
	)
	err := i.db.Scopes(r.TableOfUser(r.TableName(), userID)).
		WithContext(ctx).
		Select(cols).
		Where("user_id = ? AND fulfillment_id = ?", userID, fullID).
		Find(&m).
		Error
	if err != nil {
		return nil, err
	}
	return m, nil
}
