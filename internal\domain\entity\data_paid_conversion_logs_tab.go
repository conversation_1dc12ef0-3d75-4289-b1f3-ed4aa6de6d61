package entity

// DataPaidConversionLog 用户付费转换记录
type DataPaidConversionLog struct {
	ID          int64 `json:"id" gorm:"column:id;primaryKey;autoIncrement"` // 主键ID
	UserID      int64 `json:"user_id" gorm:"column:user_id"`                // 用户id
	Status      int8  `json:"status" gorm:"column:status"`                  // 采集状态. 0: 采集中, 1: 采集完成
	TrialTime   int64 `json:"trial_time" gorm:"column:trial_time"`          // 选择订阅计划并开启免费试用的时间
	PaymentTime int64 `json:"payment_time" gorm:"column:payment_time"`      // 付费时间
	RenewalTime int64 `json:"renewal_time" gorm:"column:renewal_time"`      // 续费时间
	CreateTime  int64 `json:"create_time" gorm:"column:create_time"`        // 创建时间
}

// TableName data_paid_conversion_logs
func (m *DataPaidConversionLog) TableName() string {
	return "data_paid_conversion_logs"
}
