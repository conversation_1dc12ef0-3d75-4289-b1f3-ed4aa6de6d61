package cache

import (
	"errors"
	"github.com/gomodule/redigo/redis"
	"github.com/sirupsen/logrus"
	"strings"
	"tmshopify/config"
	"tmshopify/store/database"
)

type RedisCache struct {
	tags []string
}

func NewRedisCache() *RedisCache {
	return &RedisCache{tags: []string{}}
}

func (cache *RedisCache) Tags(tags []string) *RedisCache {
	cache.tags = tags
	return cache
}

func (cache *RedisCache) getCacheKey(key string) string {
	return config.Get().RedisPrefix + cache.getNamespace() + ":" + key
}
func (cache *RedisCache) getNamespace() string {
	if cache.tags == nil {
		return ""
	}
	return strings.Join(cache.tags, "|")
}
func (cache *RedisCache) selectDatabase() redis.Conn {
	conn := database.RS.Pool.Get()
	/*_, err := conn.Do("SELECT", 1)
	if err != nil {
		logrus.Error("缓存器选择数据库时出错:" + err.Error())
	}*/
	return conn
}
func (cache *RedisCache) Get(key string) []byte {
	var result []byte
	conn := cache.selectDatabase()
	defer conn.Close()
	result, err := redis.Bytes(conn.Do("GET", cache.getCacheKey(key)))
	if err != nil && !errors.Is(err, redis.ErrNil) {
		logrus.Warnf("error getting key %s: %v", key, err)
	}
	return result
}

func (cache *RedisCache) SetForever(key string, value string) bool {
	conn := cache.selectDatabase()
	defer conn.Close()
	_, err := conn.Do("SET", cache.getCacheKey(key), []byte(value))
	if err != nil && !errors.Is(err, redis.ErrNil) {
		v := string(value)
		if len(v) > 15 {
			v = v[0:12] + "..."
		}
		logrus.Warn("save cache fail,error:" + err.Error())
		return false
	}
	return true
}

func (cache *RedisCache) SetWithTTL(key string, value string, second int) bool {
	conn := cache.selectDatabase()
	defer conn.Close()

	_, err := conn.Do("Set", cache.getCacheKey(key), []byte(value), "EX", second)

	if err != nil {
		v := value
		if len(v) > 15 {
			v = v[0:12] + "..."
		}
		logrus.Warn("save cache with ttl fail ,error:" + err.Error() + " value:" + v)
		return false
	}

	return true
}
