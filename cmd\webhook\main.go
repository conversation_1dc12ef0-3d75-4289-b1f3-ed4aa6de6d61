package main

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"tmshopify/config"
	"tmshopify/pkg/utils/helper"
	"tmshopify/server"
	"tmshopify/server/middleware"
	"tmshopify/store/database"
	"tmshopify/store/logger"
)

func main() {
	if err := config.Load("config/config.yaml"); err != nil {
		fmt.Println("Failed to load configuration")
		return
	}
	logger.InitLogger("./logs/webhook")
	_, err := database.InitDB()
	if err != nil {
		fmt.Println("err open databases")
		return
	}
	database.InitRedis()
	gin.SetMode(config.Get().GinMode)

	router := gin.Default()
	router.Use(middleware.Cors("Webhook"))
	server.InitWebhookRouter(router)

	_ = router.Run(config.Get().WebhookAddr)
	defer func() {
		if err := recover(); err != nil {
			// 构建错误详情
			errDetails := fmt.Sprintf("Webhook init Panic info: %v, request: %s", err, "main application")
			fmt.Println(errDetails)           // 本地打印
			go helper.CallWilding(errDetails) // 异步推送错误信息，例如 Slack、邮件等
		}
	}()
}
