package models

type DailyPendingTracks struct {
	UserId      int32  `gorm:"column:user_id;type:INT(11);comment:用户id;not null;" json:"user_id"`                              // 用户id
	TrackLength int32  `gorm:"column:track_length;type:TINYINT(4);comment:单号长度;default:NULL;" json:"track_length"`             // 单号长度
	TrackNumber string `gorm:"column:track_number;type:VARCHAR(255);comment:单号;not null;" json:"track_number"`                 // 单号
	Courier     string `gorm:"column:courier;type:VARCHAR(255);comment:物流商简码;not null;" json:"courier"`                        // 物流商简码
	PendingDays int32  `gorm:"column:pending_days;type:TINYINT(4);comment:pending 天数;not null;default:0;" json:"pending_days"` // pending 天数
	Model
}
