package models

type ShopifyEddSettings struct {
	UserId        int    `gorm:"column:user_id;type:BIGINT(20) UNSIGNED;not null;" json:"user_id"`
	OrderCutoff   string `gorm:"column:order_cutoff;type:CHAR(5);comment:Order cutoff time;not null;default:12;" json:"order_cutoff"`
	TimeZone      string `gorm:"column:time_zone;type:CHAR(5);comment:time zone;not null;default:0;" json:"time_zone"`
	ProgressTime  int    `gorm:"column:progress_time;type:INT(11);not null;default:0;" json:"progress_time"`
	WorkDays      string `gorm:"column:work_days;type:VARCHAR(255);comment:暂定为数组格式;not null;default:[];" json:"work_days"`
	CartEddRule   int    `gorm:"column:cart_edd_rule;type:TINYINT(4);comment:购物车显示最早或最晚时间默认0最晚;not null;default:0;" json:"cart_edd_rule"`
	TransitMin    int    `gorm:"column:transit_min;type:TINYINT(4);not null;default:7;" json:"transit_min"`
	TransitMax    int    `gorm:"column:transit_max;type:TINYINT(4);not null;default:10;" json:"transit_max"`
	Zones         string `gorm:"column:zones;type:TEXT(65535);comment:国家设置;default:NULL;" json:"zones"`
	EnableDefault int    `gorm:"column:enable_default;type:TINYINT(3) UNSIGNED;comment:是否启用默认设置;not null;default:1;" json:"enable_default"`
	CartEddShow   int    `gorm:"column:cart_edd_show;type:TINYINT(3) UNSIGNED;comment:1显示最早,2显示最晚,3都显示;not null;default:3;" json:"cart_edd_show"`
	TimeFormat    int    `gorm:"column:time_format;type:TINYINT(4);comment:时间显示格式,0为十二小时制,1为二十四小时制;not null;default:0;" json:"time_format"`
	DateFormat    int    `gorm:"column:date_format;type:TINYINT(3) UNSIGNED;comment:日期格式;not null;default:0;" json:"date_format"`
	Lang          string `gorm:"column:lang;type:CHAR(5);comment:插件语言;not null;default:auto;" json:"lang"`
	Countries     string `gorm:"column:countries;type:TEXT(65535);comment:插件所适用的国家;default:NULL;" json:"countries"`
	WorkOnWeekend int    `gorm:"column:work_on_weekend;type:TINYINT(3) UNSIGNED;comment:运输时间是否排除周末;not null;default:0;" json:"work_on_weekend"`
	Carriers      string `gorm:"column:carriers;type:TEXT(65535);comment:运输商设置列表;default:NULL;" json:"carriers"`
	EddType       int    `gorm:"column:edd_type;type:TINYINT(4);comment:0: pre edd, 1: edd;not null;default:0;" json:"edd_type"`
	UsePre        int    `gorm:"column:use_pre;type:TINYINT(4);comment:edd 是否使用 pre edd的设置;not null;default:0;" json:"use_pre"`      // edd 是否使用 pre edd的设置
	AiEdd         int    `gorm:"column:ai_edd;type:TINYINT(4);comment:edd 是否使用 ai计算，0为不用1为用 默认开启;not null;default:1;" json:"ai_edd"` // edd 是否使用 pre edd的设置
	CustomStyle   string `gorm:"column:custom_style;type:TEXT(65535);comment:自定义样式;default:NULL;" json:"custom_style"`
	Translation   string `gorm:"column:translation;type:MEDIUMTEXT;comment:json页面语言翻译项;default:NULL;" json:"translation"`

	Model
}

func (ShopifyEddSettings) TableName() string {
	return "shopify_edd_settings"
}
