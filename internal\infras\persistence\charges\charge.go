package charges

import (
	"context"
	"time"

	"gorm.io/gorm"

	"tmshopify/internal/domain/entity"
	"tmshopify/internal/domain/repo/charges"
)

var _ charges.ChargeRepository = (*chargeRepoImpl)(nil)

type chargeRepoImpl struct {
	db *gorm.DB
}

// NewChargeRepository 创建充值记录资源实例
func NewChargeRepository(db *gorm.DB) charges.ChargeRepository {
	return &chargeRepoImpl{db: db}
}

// GetChargeRecordsByUserIDs 根据用户ID获取60天内充值记录（按激活时间排序）
func (c *chargeRepoImpl) GetChargeRecordsByUserIDs(ctx context.Context, userIDs []int64, cols ...string) ([]*entity.ChargeRecords, error) {
	m := make([]*entity.ChargeRecords, 0)
	err := c.db.WithContext(ctx).
		Select(cols).
		Where("user_id IN (?) AND create_time >= ?", userIDs, time.Now().AddDate(0, 0, -60)).
		Find(&m).
		Error
	if err != nil {
		return nil, err
	}
	return m, nil
}
