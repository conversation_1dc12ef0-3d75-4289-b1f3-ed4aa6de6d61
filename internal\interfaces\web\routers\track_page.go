package routers

import (
	"github.com/gin-gonic/gin"

	"tmshopify/internal/app/api"
	"tmshopify/internal/infras/config"
	"tmshopify/internal/providers"
)

func RegisterTrackPageRoute(routeGroup *gin.RouterGroup, repos *providers.Repositories, appConfig *config.AppConfig) {
	trackPageGroup := routeGroup.Group("/track-page")

	TrackPage := new(api.TrackPage)
	trackPageGroup.GET("/back-data", TrackPage.Tracking)
	trackPageGroup.GET("/recommend-product", TrackPage.RecommendProduct)
	trackPageGroup.GET("/collection-product", TrackPage.CollectionProduct)
	trackPageGroup.POST("/review/:shop", TrackPage.ReviewList)
	trackPageGroup.PUT("/review/:shop", TrackPage.DoReview)
	trackPageGroup.GET("/order-product", TrackPage.OrderProduct)
	trackPageGroup.GET("/plugin/back-data", TrackPage.PluginTracking)
	trackPageGroup.POST("/click-product", TrackPage.ProductClick)

}
