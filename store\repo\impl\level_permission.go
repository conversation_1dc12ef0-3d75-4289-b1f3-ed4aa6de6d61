package impl

import (
	"tmshopify/pkg/domain"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type LevelPermission struct {
	Helper
}

func getLevelPermissions(level int, version models.Version) []models.Permission {
	var res []int
	var levelPermissionList []models.Permission
	database.DB.Model(models.LevelsPermissions{}).Where("level = ? AND version = ?", level, version).Pluck("permission_id", &res)
	database.DB.Model(models.Permissions{}).Where("id IN (?)", res).Pluck("slug", &levelPermissionList)
	return levelPermissionList
}

func GetLevelPermissionDomain(level int, user models.Users, version models.Version) domain.PermissionDomain {
	permissions := getLevelPermissions(level, version)
	var userPermissions []models.Permission
	for _, permission := range user.UserPermissions {
		userPermissions = append(userPermissions, permission.Slug)
	}
	userPermissions = append(userPermissions, permissions...)
	permissionDomain := domain.PermissionDomain{
		Level:       level,
		Permissions: userPermissions,
		Version:     version,
	}
	return permissionDomain
}
