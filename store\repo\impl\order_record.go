package impl

import (
	"encoding/json"
	"gorm.io/gorm"
	"strings"
	"tmshopify/config"
	"tmshopify/internal/app/definition"
	"tmshopify/internal/app/utils"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type OrderRecord struct {
	Helper
}

type ShippingAddress struct {
	Latitude     float32 `json:"latitude"`
	Longitude    float32 `json:"longitude"`
	Address1     string  `json:"address1"`
	Address2     string  `json:"address2"`
	City         string  `json:"city"`
	ProvinceCode string  `json:"provinceCode"`
	Country      string  `json:"country"`
}

func (o *OrderRecord) FirstByOrderId(userId int, orderId string) (models.OrderRecords, error) {
	orderRecord := models.OrderRecords{}
	result := database.DB.Scopes(orderRecord.TableOfUser(orderRecord.TableName(), userId)).Where("order_id = ?", orderId).First(&orderRecord)
	return orderRecord, result.Error
}

func (o *OrderRecord) DealModernOrderMessageData(orderMessage models.OrderRecords) (models.OrderRecords, map[config.StatusNodeNumber]int) {
	if orderMessage.OrderName == "" {
		return orderMessage, nil
	}
	//只取第一个产品
	product := strings.Split(orderMessage.Product, ",")
	orderMessage.Product = product[0]
	//用户自定义订单状态
	var customStatusTime map[config.StatusNodeNumber]int
	err := json.Unmarshal([]byte(orderMessage.CustomStatusTime), &customStatusTime)
	if err != nil {
		customStatusTime = nil
	}
	return orderMessage, customStatusTime
}

func (o *OrderRecord) DealOrderMessageData(orderMessage models.OrderRecords, mapLocation bool) (models.OrderRecords, map[config.StatusNodeNumber]int, definition.HandleShippingAddressResult, definition.MapLocation) {
	var location definition.MapLocation
	if orderMessage.OrderName == "" {
		return orderMessage, nil, definition.HandleShippingAddressResult{}, location
	}
	//只取第一个产品
	product := strings.Split(orderMessage.Product, ",")
	orderMessage.Product = product[0]
	//用户自定义订单状态
	var customStatusTime map[config.StatusNodeNumber]int
	err := json.Unmarshal([]byte(orderMessage.CustomStatusTime), &customStatusTime)
	if err != nil {
		customStatusTime = nil
	}
	//当前的发货地址
	var shippingAddress ShippingAddress
	var shippingAddressResult definition.HandleShippingAddressResult
	nullShippingAddress := ShippingAddress{}
	err = json.Unmarshal([]byte(orderMessage.ShippingAddress), &shippingAddress)
	if err == nil && shippingAddress != nullShippingAddress {
		shippingAddressResult, location = handleShippingAddress(shippingAddress, mapLocation)
	}

	return orderMessage, customStatusTime, shippingAddressResult, location
}

func (o *OrderRecord) FirstByOrderNameWithUser(userId int, order string) models.OrderRecords {
	var orderRecord models.OrderRecords
	database.DB.Scopes(orderRecord.TableOfUser(orderRecord.TableName(), userId)).Preload("User").Where("order_name = ? and user_id = ?", order, userId).First(&orderRecord)
	return orderRecord
}

func (o *OrderRecord) FirstByOrderName(userId int, order string) models.OrderRecords {
	var orderRecord models.OrderRecords
	database.DB.Scopes(orderRecord.TableOfUser(orderRecord.TableName(), userId)).Where("order_name = ? and user_id = ?", order, userId).First(&orderRecord)
	return orderRecord
}

// 组装发货信息
func handleShippingAddress(shippingAddress ShippingAddress, mapLocation bool) (definition.HandleShippingAddressResult, definition.MapLocation) {
	var arr []string
	latitude := shippingAddress.Latitude
	longitude := shippingAddress.Longitude
	address1 := shippingAddress.Address1
	address2 := shippingAddress.Address2
	city := shippingAddress.City
	provinceCode := shippingAddress.ProvinceCode
	country := shippingAddress.Country
	if address1 != "" {
		arr = append(arr, address1)
	}
	if address2 != "" {
		arr = append(arr, address2)
	}
	if city != "" {
		arr = append(arr, city)
	}
	if provinceCode != "" {
		arr = append(arr, provinceCode)
	}
	if country != "" {
		arr = append(arr, country)
	}
	place := country
	if len(arr) != 0 {
		place = strings.Join(arr, ", ")
	}
	if (longitude != 0 || latitude != 0) && country != "" && mapLocation {
		var placeMap string
		if city == "" {
			placeMap = country + "-" + city
		} else {
			placeMap = country
		}
		location := utils.GetMapLocation([]definition.ReturnInfo{}, placeMap)
		return definition.HandleShippingAddressResult{}, location
	}
	return definition.HandleShippingAddressResult{
		Lat:       latitude,
		Lon:       longitude,
		Location:  place,
		Location1: city + " " + provinceCode + " " + country,
	}, definition.MapLocation{}
}

func (o *OrderRecord) FirstByOrderNameWithOrderTrack(userId int, order string) (models.OrderRecords, error) {
	var orderTrack models.OrderTracks
	var orderRecord models.OrderRecords
	err := database.DB.Scopes(orderRecord.TableOfUser(orderRecord.TableName(), userId)).Preload("OrderTracks", func(db *gorm.DB) *gorm.DB {
		return database.DB.Scopes(orderTrack.TableOfUser(orderTrack.TableName(), userId)).Where("user_id = ? AND order_name = ? AND is_delete != ?", userId, order, 1).Order("created_at desc").Find(&models.OrderTracks{})
	}).Where("order_name = ? and user_id = ?", order, userId).First(&orderRecord).Error
	return orderRecord, err
}
