package service

import (
	"tmshopify/store/models"
	"tmshopify/store/repo/impl"
)

type BuriedPoint struct {
	buriedPointRepo *impl.BuriedPoint
}

func NewBuriedPoint() *BuriedPoint {
	return &BuriedPoint{
		buriedPointRepo: &impl.BuriedPoint{},
	}
}

func (b *BuriedPoint) GetBuriedPointStatistic() ([]map[string]interface{}, error) {
	data, err := b.buriedPointRepo.GetBuriedPointData()
	arr := models.BuriedPointName()
	for key, value := range data {
		for k, v := range arr {
			if k == value["name"] {
				data[key]["name"] = v
				break
			}
		}
	}
	return data, err
}
