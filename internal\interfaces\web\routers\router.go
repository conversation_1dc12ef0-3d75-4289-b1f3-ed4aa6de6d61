package routers

import (
	"fmt"
	"log"
	"time"

	"github.com/gin-gonic/gin"

	"tmshopify/internal/infras/config"
	"tmshopify/internal/interfaces/web/middleware"
	"tmshopify/internal/providers"
	"tmshopify/pkg/utils/helper"
)

// InitRouters 初始化router规则
func InitRouters(router *gin.Engine, repos *providers.Repositories, appConfig *config.AppConfig) {
	// 访问日志中间件处理
	logWare := &middleware.LogWare{}

	// 对所有的请求进行性能监控，一般来说生产环境，可以对指定的接口做性能监控
	router.Use(
		logWare.Access(), middleware.Cors(), func() gin.HandlerFunc {
			return func(c *gin.Context) {
				defer func() {
					if err := recover(); err != nil {
						log.Printf("Panic info is: %v", err)
						go helper.CallWilding(fmt.Sprintf("Painc App:%s\nInfo is: %v\nRequest is: %s\nDomain: %s", "tms-api", err, c.Request.URL, c.<PERSON>eader("X-Shopify-Shop-Domain")))
					}
				}()
			}
		}(), middleware.TimeoutHandler(40*time.Second),
	)

	// gin 框架prometheus接入
	router.Use(middleware.WrapMonitor())

	// 路由找不到的情况
	router.NoRoute(middleware.NotFoundHandler())

	apiGroup := router.Group("/api/v2") // 定义路由组
	RegisterThirdTrackingRoute(apiGroup, repos, appConfig)
	RegisterTrackPageRoute(apiGroup, repos, appConfig)
	RegisterCheckoutRoute(apiGroup, repos, appConfig)

	apiGroup.Use(middleware.AuthMiddleware(*appConfig, repos))
	RegisterOrderRoute(apiGroup, repos, appConfig)

	// 注册付费转化漏斗路由
	RegisterPaidConversionFunnelRoute(apiGroup, repos)
}
