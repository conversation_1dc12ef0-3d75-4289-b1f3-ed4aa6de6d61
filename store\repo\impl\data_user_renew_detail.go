package impl

import (
	"time"
	"tmshopify/store/database"
	"tmshopify/store/models"
)

type DataUserRenewDetail struct {
	Helper
}

func (p *DataUserRenewDetail) GetUserPaidIds(startTime string, endTime string) ([]int, error) {
	var userIds []int
	result := database.DB.Model(&models.DataUserRenewDetail{}).Where("type = 1 and create_date between ? and ?", startTime, endTime).Pluck("user_id", &userIds)
	return userIds, result.Error
}

func (p *DataUserRenewDetail) GetUserRenewCount(startTime time.Time, endTime time.Time, startIds []int) ([]map[string]interface{}, error) {
	var column []map[string]interface{}
	result := database.DB.Model(&models.DataUserRenewDetail{}).Where("type = 2 and user_id in ? and create_date between ? and ?", startIds, startTime, endTime).Select("date_format(create_date,\"%Y-%m-%d\") as date,count(*) as count").Group("create_date").Scan(&column)
	return column, result.Error
}
