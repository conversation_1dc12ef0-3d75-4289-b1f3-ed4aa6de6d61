package routers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"tmshopify/internal/app/api"
	"tmshopify/server/middleware"
	"tmshopify/server/response"

	"tmshopify/internal/infras/config"
	"tmshopify/internal/providers"
)

func RegisterThirdTrackingRoute(routeGroup *gin.RouterGroup, repos *providers.Repositories, appConfig *config.AppConfig) {

	ThirdTracking := new(api.ThirdTracking)
	// 集成app对外接口
	routeGroup.GET("/:thirdpart/install", middleware.RequestLimiter("240-M"), ThirdTracking.ThirdpartInstall)    // thirdpart查询安装状态
	routeGroup.POST("/:thirdpart/tracking", middleware.RequestLimiter("240-M"), ThirdTracking.ThirdpartTracking) // thirdpart单号查询
	routeGroup.POST("/", func(c *gin.Context) {
		logrus.Warnf("ClientIP: %s\n", c.ClientIP())
		response.Success(c, &response.SuccessResponse{
			Code: http.StatusOK,
			Data: map[string]interface{}{
				"test": 123,
			},
		})
	})
	routeGroup.GET("/gorgias_tracking", ThirdTracking.GorgiasTracking) // Gorgias 插件查询接口
}
