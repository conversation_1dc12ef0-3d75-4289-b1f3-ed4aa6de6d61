package domain

import "tmshopify/store/models"

const (
	CusNotification         models.Permission = "cus_notification" //状态变更买家通知
	MerNotification         models.Permission = "mer_notification" //状态变更卖家通知
	AdvancedSummary         models.Permission = "advanced_summary" //卖家日报周报邮件
	Export                  models.Permission = "export"           //导出
	Analytics               models.Permission = "analytics"        //分析页面
	GoogleSheet             models.Permission = "google_sheet"     //google 表格
	OrderStatusCard         models.Permission = "order_status_card"
	ShipmentReview          models.Permission = "shipment_review"
	WordMasking             models.Permission = "word_masking"               //track page屏蔽关键词
	MultilingualTrackPage   models.Permission = "multilingual_track_page"    //track page 页面多语言
	RemoveBranding          models.Permission = "remove_branding"            //track page 移除branding
	KlaviyoIntegration      models.Permission = "klaviyo_integration"        //klaviyo 集成
	SlackIntegration        models.Permission = "slack_integration"          // slack 集成
	GorgiasIntegration      models.Permission = "gorgias_integration"        // slack 集成
	CouponSells             models.Permission = "coupon_sells"               //优惠券
	CustomShippingRuleBasic models.Permission = "custom_shipping_rule_basic" //自定义国家运输时长规则
	CustomShippingRulePro   models.Permission = "custom_shipping_rule_pro"   //自定义国家运输时长规则
	TrackPagePlugins        models.Permission = "track_page_plugins"         //trackpage插件权限
)

type PermissionDomain struct {
	Level       int
	Permissions []models.Permission
	Version     models.Version
}

func (p *PermissionDomain) VerifyPermission(permission models.Permission) bool {
	for _, v := range p.Permissions {
		if v == permission {
			return true
		}
	}
	return false
}
