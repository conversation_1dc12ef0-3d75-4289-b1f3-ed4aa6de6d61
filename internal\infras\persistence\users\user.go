package users

import (
	"context"
	"time"

	"gorm.io/gorm"

	"tmshopify/internal/domain/entity"
	"tmshopify/internal/domain/repo/users"
	"tmshopify/store/models"
)

var _ users.UserRepository = (*userRepoImpl)(nil)

type userRepoImpl struct {
	db *gorm.DB
}

// NewUserRepository 创建用户资源实例
func NewUserRepository(db *gorm.DB) users.UserRepository {
	return &userRepoImpl{db: db}
}

// Get 根据 userid 获取用户
func (u *userRepoImpl) Get(ctx context.Context, id int, cols ...string) (*models.Users, error) {
	m := &models.Users{}
	err := u.db.WithContext(ctx).Select(cols).Where("id = ?", id).First(m).Error
	if err != nil {
		return nil, err
	}
	return m, nil
}

// GetLatestUser 获取最新的一条用户
func (u *userRepoImpl) GetLatestUser(ctx context.Context, cols ...string) (*models.Users, error) {
	m := &models.Users{}
	err := u.db.WithContext(ctx).Select(cols).Order("id DESC").First(m).Error
	if err != nil {
		return nil, err
	}
	return m, nil
}

// GetUsers 使用游标获取所有用户
func (u *userRepoImpl) GetUsers(ctx context.Context, cursor int, limit int, cols ...string) ([]*models.Users, error) {
	m := make([]*models.Users, 0)
	err := u.db.WithContext(ctx).
		Select(cols).
		Where("id > ?", cursor).
		Limit(limit).
		Find(&m).
		Error
	if err != nil {
		return nil, err
	}
	return m, nil
}

// GetGroupStore 获取子店铺数据
func (u *userRepoImpl) GetGroupStore(ctx context.Context, userID int, cols ...string) (models.ChargeGroupStores, error) {
	m := models.ChargeGroupStores{}
	err := u.db.WithContext(ctx).
		Select(cols).
		Where("user_id = ? AND main_user_id != 0", userID).
		First(&m).Error
	return m, err
}

// GetIntegrations 获取用户设置
func (u *userRepoImpl) GetIntegrations(ctx context.Context, userID int, setting string, cols ...string) (*models.UserIntegrations, error) {
	// 根据 setting 查询 integration_apps.id
	app := &models.IntegrationApps{}
	err := u.db.WithContext(ctx).
		Select("id").
		Where("setting_link = ?", setting).
		First(app).
		Error
	if err != nil {
		return nil, err
	}

	// 使用 userid 和 integration_apps.id 查询 user_integrations
	m := &models.UserIntegrations{}
	err = u.db.WithContext(ctx).
		Select(cols).
		Where("user_id = ? AND app_id = ? AND connected = 1", userID, app.ID).
		First(m).
		Error
	if err != nil {
		return nil, err
	}

	return m, nil
}

// GetPaidConversion 获取用户付费转换记录
func (u *userRepoImpl) GetPaidConversion(ctx context.Context, userID int, cols ...string) (*entity.DataPaidConversionLog, error) {
	m := &entity.DataPaidConversionLog{}
	err := u.db.WithContext(ctx).
		Select(cols).
		Where("user_id = ?", userID).
		First(m).
		Error
	if err != nil {
		return nil, err
	}
	return m, nil
}

// AddPaidConversion 添加用户付费转化记录
func (u *userRepoImpl) AddPaidConversion(ctx context.Context, m *entity.DataPaidConversionLog) error {
	return u.db.WithContext(ctx).Create(m).Error
}

// GetActiveUsersCreatedAfter 获取指定时间后注册的活跃用户（用于数据同步）
// SQL: SELECT cols FROM users WHERE id > ? AND created_at >= ? AND is_delete = 0 AND redact = 0 LIMIT ?
func (u *userRepoImpl) GetActiveUsersCreatedAfter(ctx context.Context, cursor int, limit int, createdAfter time.Time, cols ...string) ([]*models.Users, error) {
	m := make([]*models.Users, 0)
	err := u.db.WithContext(ctx).
		Select(cols).
		Where("id > ? AND created_at >= ? AND is_delete = 0 AND redact = 0", cursor, createdAfter).
		Order("id ASC").
		Limit(limit).
		Find(&m).
		Error
	if err != nil {
		return nil, err
	}
	return m, nil
}
