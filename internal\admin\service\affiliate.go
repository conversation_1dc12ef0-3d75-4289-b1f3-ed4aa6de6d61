package service

import (
	"tmshopify/pkg/utils/paginate"
	"tmshopify/server/response"
	affiliateModels "tmshopify/store/models/affiliate"
	affiliateRep "tmshopify/store/repo/impl/affiliate"
)

type AffiliateInterface interface {
	GetApps(params paginate.Param) affiliateModels.Apps
}

type AffiliateService struct {
	appRep *affiliateRep.AppRepository
}

func (a *AffiliateService) GetApps(params *paginate.Param) (*response.SuccessResponse, error) {
	apps, total, err := a.appRep.Paginate(params)
	return &response.SuccessResponse{
		Data: map[string]interface{}{
			"items":    apps,
			"page":     params.Page,
			"total":    total,
			"pageSize": params.PageSize,
		},
	}, err
}

func NewAffiliateService() *AffiliateService {
	return &AffiliateService{appRep: &affiliateRep.AppRepository{}}
}
