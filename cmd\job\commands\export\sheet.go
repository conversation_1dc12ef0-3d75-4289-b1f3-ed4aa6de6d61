package export

import (
	"fmt"
	"log"
	"strings"

	"github.com/channelwill/athena/logger"
	"github.com/spf13/cobra"

	"tmshopify/internal/infras/config"
	"tmshopify/internal/infras/graceful"
	"tmshopify/store/database"
)

// GoogleSheetQueue 队列 key
const GoogleSheetQueue = "tmshopify_database_ExportGoogleSheet"

// newGoogleSheetCommand Google Sheet 导出
func newGoogleSheetCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "sheet",
		Short: "Google Sheet store data export",
		Long:  "Export the store data of the Group store user to the Google sheet",
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := cmd.Context()
			log.Println("running export google sheet")

			log.Println("querying db")

			// connect db
			db, err := config.NewDB("db_conf")
			if err != nil {
				log.Fatalf("db init error:%v", err)
			}
			// 兼容旧配置
			database.DB = db

			// 兼容旧版本
			database.InitRedis()

			// connect redis
			redisUniversalClient, err := config.NewRedisClient("redis_conf")
			if err != nil {
				log.Fatalf("redis init error:%v", err)
			}

			// init google sheets service
			service, err := config.NewGoogleSheet("google_sheets")
			if err != nil {
				log.Fatalf("google_sheets init error: %v", err)
			}

			// user service
			user := initUserService(db, redisUniversalClient, service)

			// 使用游标获取所有活跃用户
			var (
				cursor = 0
				limit  = 1000
			)

			for {
				list, err := user.GetUsers(ctx, cursor, limit, "id", "store_name", "is_delete", "redact", "plan_id", "old_total", "time_zone")
				if err != nil {
					return err
				}

				for _, m := range list {
					// 是否导出 googleSheet
					if user.ShouldExportSheet(ctx, m) {
						// 拼接 user.id,sheetname
						sheet := "Trackingmore"
						if len(m.StoreName) != 0 {
							sheet = fmt.Sprintf("%s-%s", sheet, strings.TrimSuffix(m.StoreName, ".myshopify.com"))
						}

						// redis queue value
						v := fmt.Sprintf("%d,%s", m.ID, sheet)

						log.Println(fmt.Sprintf(`push to redis queue: "%s"`, v))
						if err := redisUniversalClient.LPush(ctx, GoogleSheetQueue, v).Err(); err != nil {
							logger.Error(ctx, fmt.Sprintf("[UserID: %d] push redis queue failed. %v", m.ID, err))
						}
					}
				}

				if len(list) != limit {
					break
				}

				// 移动游标
				cursor = list[len(list)-1].ID
			}

			logger.Info(ctx, "execution successfully")
			graceful.Stop()
			return nil
		},
	}
}
