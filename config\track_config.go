package config

type UploadConfig struct {
	Server string `json:"server"`
	Sign   string `json:"sign"`
}

type TrackConfigType struct {
	Upload        UploadConfig `json:"upload"`
	TmDomain      string       `json:"tm_domain"`
	AssetsVersion string       `json:"assets_version"`
	TmApiDomain   string       `json:"tm_api_domain"`
	TmCdnDomain   string       `json:"tm_cdn_domain"`
	BaseCourier   string       `json:"base_courier"`
	StatusList    []string     `json:"status_list"`
	DateFormat    []string     `json:"date_format"`
	TimeFormat    []string     `json:"time_format"`
}

var TrackConfig = TrackConfigType{
	Upload: UploadConfig{
		Server: "https://file.channelwill.com/v1/upload",
		Sign:   "tmshopify",
	},
	TmDomain:      "www.trackingmore.com",
	AssetsVersion: "1.0.0",
	TmApiDomain:   "api.trackingmore.com",
	TmCdnDomain:   "cdn.trackingmore.com",
	BaseCourier:   "TM",
	StatusList:    []string{"pending", "shipped", "delivered", "returned", "cancelled", "error"},
	DateFormat: []string{"Dec 31, 2022",
		"Dec 31",
		"Dec 31st 2022",
		"12/31/2022 ( m/d/yyyy )",
		"31/12/2022 ( d/m/yyyy )",
		"31.12.2022 ( dd.mm.yyyy )",
		"2022-12-31 ( yyyy-mm-dd )",
	},
	TimeFormat: []string{"12-hour time",
		"24-hour time",
		"Do not display time",
	},
}
