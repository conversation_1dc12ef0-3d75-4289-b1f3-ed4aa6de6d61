package cache

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/channelwill/athena/logger"
	"github.com/gomodule/redigo/redis"
	"go.uber.org/zap"

	authRepo "tmshopify/internal/domain/repo/auth"
	"tmshopify/internal/infras/cache"
	"tmshopify/store/database"

	authEntity "tmshopify/internal/domain/entity/auth"
)

var _ authRepo.UserInfoCache = (*UserAuthInfoCacheImpl)(nil)

// UserAuthInfoCacheImpl 用户信息缓存
type UserAuthInfoCacheImpl struct {
	redisClient      *database.Redis
	userAuthInfoRepo authRepo.UserInfoRepository
}

// NewUserAuthInfoCache 创建用户信息缓存
func NewUserAuthInfoCache(redisClient *database.Redis, userAuthInfoRepo authRepo.UserInfoRepository) *UserAuthInfoCacheImpl {
	return &UserAuthInfoCacheImpl{
		redisClient:      redisClient,
		userAuthInfoRepo: userAuthInfoRepo,
	}
}

// GetUserById 获取用户信息缓存
func (c *UserAuthInfoCacheImpl) GetUserById(ctx context.Context, userID int64) (*authEntity.UserAuthInfoEntity, error) {
	key := fmt.Sprintf("%s:user_auth_info:%d", cache.RedisShopifyPrefix, userID)
	// 从缓存中获取用户信息
	userAuthInfoBytes, err := c.redisClient.Get(key)
	if err != nil && !errors.Is(err, redis.ErrNil) {
		logger.Error(ctx, "failed to get from redis", zap.String("key", key), zap.Error(err))
		return nil, err
	}
	if errors.Is(err, redis.ErrNil) {
		var userAuthInfo *authEntity.UserAuthInfoEntity
		// 从数据库中获取用户信息
		userAuthInfo, err = c.userAuthInfoRepo.GetUserByID(ctx, userID)
		if err != nil {
			return nil, err
		}
		// 将用户信息写入缓存
		userAuthInfoBytes, err = json.Marshal(userAuthInfo)
		if err != nil {
			return nil, err
		}
		c.redisClient.SetWithExpire(key, string(userAuthInfoBytes), time.Minute*5)
	}

	userAuthInfo := &authEntity.UserAuthInfoEntity{}
	err = json.Unmarshal(userAuthInfoBytes, userAuthInfo)
	if err != nil {
		return nil, err
	}

	return userAuthInfo, nil
}

// GetUserByStore 获取用户信息缓存
func (c *UserAuthInfoCacheImpl) GetUserByStore(ctx context.Context, storeName string) (*authEntity.UserAuthInfoEntity, error) {
	key := fmt.Sprintf("%s:user_auth_info:%s", cache.RedisShopifyPrefix, storeName)
	// 从缓存中获取用户信息
	userAuthInfoBytes, err := c.redisClient.Get(key)
	if err != nil && !errors.Is(err, redis.ErrNil) {
		logger.Error(ctx, "failed to get from redis", zap.String("key", key), zap.Error(err))
		return nil, err
	}
	if errors.Is(err, redis.ErrNil) {
		var userAuthInfo *authEntity.UserAuthInfoEntity
		if storeName == "" {
			// 第三种情况，没有店铺名称，直接从数据库中获取用户信息
			userAuthInfo, err = c.userAuthInfoRepo.GetUserByStoreName(ctx, storeName)
			if err != nil {
				return nil, err
			}

		} else {
			// 从数据库中获取用户信息
			userAuthInfo, err = c.userAuthInfoRepo.GetUserByStoreName(ctx, storeName)
			if err != nil {
				return nil, err
			}
		}
		// 将用户信息写入缓存
		userAuthInfoBytes, err = json.Marshal(userAuthInfo)
		if err != nil {
			return nil, err
		}
		c.redisClient.SetWithExpire(key, string(userAuthInfoBytes), time.Minute*5)
	}

	userAuthInfo := &authEntity.UserAuthInfoEntity{}
	err = json.Unmarshal(userAuthInfoBytes, userAuthInfo)
	if err != nil {
		return nil, err
	}

	return userAuthInfo, nil
}

// Delete 删除用户信息缓存
func (c *UserAuthInfoCacheImpl) Delete(ctx context.Context, userID int64) error {
	key := fmt.Sprintf("%s:user_auth_info:%d", cache.RedisShopifyPrefix, userID)
	return c.redisClient.Delete(key)
}
