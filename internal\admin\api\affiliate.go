package api

import (
	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"

	"tmshopify/internal/admin/request"
	"tmshopify/internal/admin/service"
	"tmshopify/pkg/utils/helper"
	"tmshopify/pkg/utils/paginate"
	"tmshopify/server/response"
	"tmshopify/store/database"
	"tmshopify/store/models/affiliate"
)

type Affiliate struct {
}

func (a *Affiliate) AppList(ctx *gin.Context) {
	params := paginate.RequestJsonParam(ctx)

	affiliateService := service.NewAffiliateService()
	result, err := affiliateService.GetApps(params)
	if err != nil {
		response.InternalServerError(ctx, err.Error())
		return
	}
	response.Success(ctx, result)
}

func (a *Affiliate) AppCreate(ctx *gin.Context) {
	var createApp request.CreateAffiliate
	if err := ctx.ShouldBindJSON(&createApp); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
	}

	err := validate.Struct(createApp)
	if err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	// 1. 创建App记录
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(createApp.Password), 12)
	if err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	app := affiliate.Apps{
		Password:     string(hashedPassword),
		Name:         createApp.Name,
		LandingPage:  createApp.LandingPage,
		ContactEmail: createApp.ContactEmail,
		ContactName:  createApp.ContactName,
		ContactPhone: createApp.ContactPhone,
	}

	// 保存App到数据库
	tx := database.DB.Begin()
	if err := tx.Create(&app).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(ctx, err.Error())
		return
	}

	// 2. 创建关联的OAuth客户端
	// 生成随机密钥
	secret := helper.GetRandomString(40)

	client := affiliate.OauthClients{
		UserId:               0, // 无用户关联
		Name:                 app.Name,
		Secret:               secret,
		Provider:             "integration",
		PasswordClient:       0, // 不允许密码授权
		PersonalAccessClient: 0, // 不是个人访问客户端
		Revoked:              0, // 未撤销
	}

	// 保存OAuth客户端到数据库
	if err := tx.Create(&client).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(ctx, err.Error())

		return
	}

	// 3. 更新App记录，关联OAuth客户端
	app.OauthClientId = uint64(client.ID)
	if err := tx.Save(&app).Error; err != nil {
		tx.Rollback()
		response.InternalServerError(ctx, err.Error())
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		response.InternalServerError(ctx, err.Error())

		return
	}

	// 4. 返回带有客户端凭证的结果
	response.Success(ctx, &response.SuccessResponse{
		Data: map[string]interface{}{
			"app":           app,
			"client_id":     client.ID,
			"client_secret": client.Secret,
		},
	})

}

func (a *Affiliate) AppUpdate(ctx *gin.Context) {
	var updateApp request.UpdateAffiliate
	if err := ctx.ShouldBindJSON(&updateApp); err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	err := validate.Struct(updateApp)
	if err != nil {
		response.UnprocessableEntity(ctx, err.Error())
		return
	}
	database.DB.Model(&affiliate.Apps{}).Updates(updateApp)
	response.Success(ctx, &response.SuccessResponse{
		Data: updateApp,
	})
}
