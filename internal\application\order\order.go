package order

import (
	"context"

	repo "tmshopify/internal/domain/repo/orders"
	"tmshopify/internal/providers"
)

// Service OrderService 实例
type Service struct {
	orderRepo repo.OrderCountRepository
}

// NewService 创建一个 OrderService 实例
func NewService(repos *providers.Repositories) *Service {
	return &Service{
		orderRepo: repos.OrderCountRepo,
	}
}

// GetCount 获取订单数量
func (s *Service) GetCount(ctx context.Context, userID int64) (int64, int64, error) {
	return s.orderRepo.GetCount(ctx, userID)
}
