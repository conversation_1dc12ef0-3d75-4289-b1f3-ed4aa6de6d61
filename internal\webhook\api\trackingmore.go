package api

import (
	"github.com/sirupsen/logrus"
	"io"
	"tmshopify/config"
	"tmshopify/server/response"
	"tmshopify/store/database"

	"github.com/gin-gonic/gin"
)

type TrackingMore struct {
}

func (s *TrackingMore) Webhook(ctx *gin.Context) {
	// 读取请求体数据
	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		// 处理读取错误
		response.UnprocessableEntity(ctx, body)
		return
	}
	if len(body) <= 0 {
		response.BadRequest(ctx, "empty body")
		return
	}
	_, err = database.RS.SAdd(config.CacheKey.TrackingWebhookUpdate, string(body))
	if err != nil {
		logrus.Warn("redis save error:" + err.Error() + " with data:" + string(body))
		response.InternalServerError(ctx, "Save data fail")
		return
	}
	response.Success(ctx, &response.SuccessResponse{
		Code:    200,
		Status:  "200",
		Message: "success",
	})
}
